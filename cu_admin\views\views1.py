from decimal import Decimal
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.contrib.auth.hashers import check_password, make_password
import logging
from rest_framework import generics, status, views
from cu_project.common.utils import generate_response
from cu_app.utils.refund_utils import calculate_refund_percentage
from cu_app.services.airwallex import AirwallexService
from cu_admin.user_notifications import *
from cu_app.services.email_services import EmailService
from ..serializers import *
from datetime import *
from rest_framework.response import Response
import json
from django.contrib.auth.models import Group, Permission
from django.views import View
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import get_user_model
from cu_app.models import *
import os
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import stripe
import re
from django.conf import settings
from cu_app.cu_library import *
import random
from django.db.models import Q
from rest_framework import permissions
from ..models import *
import requests
from firebase_admin.messaging import Message, Notification, WebpushConfig, WebpushFCMOptions
from fcm_django.models import FCMDevice
from django.utils import timezone
from python_http_client.exceptions import HTTPError
from django.utils.dateparse import parse_datetime
from django.core.paginator import EmptyPage, Paginator
from dotenv import load_dotenv
from rest_framework.views import APIView
from django.http import JsonResponse
from cu_app.utils.helper_functions import *

load_dotenv()
# added

# added admin-content-approval-push-notification function
logger = logging.getLogger(__name__)

email_service = EmailService()


class TestNotificationView(APIView):
    def get(self, request):
        user_id = request.GET.get('user_id')
        if not user_id:
            return JsonResponse({"error": "user_id parameter is required"}, status=status.HTTP_400_BAD_REQUEST)

        # Get the FCM device for the user
        devices = FCMDevice.objects.filter(user_id=user_id)
        if not devices.exists():
            return JsonResponse({"error": "No devices found for the user"}, status=status.HTTP_404_NOT_FOUND)

        # Log device tokens
        for device in devices:
            logger.info(f"Device Token: {device.registration_id}")

        # Send the notification
        try:
            response = devices.send_message(
                Message(
                    notification=Notification(
                        title="Test Notification",
                        body="This is a test notification to check FCM setup."
                    )
                )
            )

            if response is None:
                return JsonResponse({"error": "No response from FCM"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            return JsonResponse({"message": "Notification sent successfully", "fcm_response": str(response)}, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error sending notification: {e}")
            return JsonResponse({"error": f"Failed to send notification: {str(e)}"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


def filter_user_data(a):
    print("data inside the filter user data", a)
    keys = ["is_admin", "is_active", "is_superuser", "password",
            "user_permissions", "groups", "PWVerifyCode", "PWCodeGentime"]
    result = {k: v for k, v in CuUserRegisterSerializer(
        a).data.items() if k not in keys}

    if a.groups.all():
        result['role'] = a.groups.all()[0].name
    else:
        result['role'] = None

    # result['role'] = a.groups.all()[0].name
    if result['role'] == "doctor" or result['role'] == "researcher" or result['role'] == "influencer":
        print(
            f"expertise dataaaaa-----{a.expertise.all()}---{type(a.expertise.all())}")
        result['expertise'] = get_expertise_data(a.expertise.all())
        result['doctor_other_details'] = serialize_model(
            a.doctordetails, DoctorDetailsSerializer)
    elif result['role'] == "patient":
        result['patient_other_details'] = serialize_model(
            a.patientdetails, PatientDetailsSerializer)
    else:
        pass
    return result
# added


def get_cu_user_type(a):
    try:
        print(f"in user type---{a}")
        b = get_user_model().objects.get(id=a)
        u_groups = b.groups.all()
        print(f"user groups----{u_groups}")
        if len(u_groups) >= 1:

            return u_groups[0].name
        else:
            return "no valid user found"
    except Exception as e:
        print(f"get user exception----{e}")
        return str(e)

# Create your views here.


def get_expertise_data(expertise):
    p_data1 = []
    for x in expertise:
        p_data1.append(
            serialize_model(ExpertiseCancertype.objects.filter(id__exact=x.id)[0], ExpertiseCancertypeSerializer))

    return p_data1


def serialize_model(a, serializer):

    result = {k: v for k, v in serializer(a).data.items()}
    print(f"in filter model data   {result}")
    return result


class IsCuAdminPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        user_id = request.GET['user_id']
        is_cu_admin = get_user_model().objects.filter(id__exact=user_id, groups__name__in=[
            'admin', 'child_admin']).exists()  # needs a discussion on this
        return is_cu_admin


class GetExpertsByStatusTypeView(generics.ListAPIView):
    permission_classes = [IsCuAdminPermission]
    queryset = get_user_model().objects.all()
    serializer_class = CuUserFilterSerializer

    def get_queryset(self):

        user_status = ''
        if self.kwargs['approval'] == str(1):
            user_status = "Approved"
        elif self.kwargs['approval'] == str(0):
            user_status = "pending"
        elif self.kwargs['approval'] == str(2):
            user_status = "Rejected"
        elif self.kwargs['approval'] == str(3):
            user_status = "Deactivated"
        elif self.kwargs['approval'] == str(4):
            user_status = "Approval_requested"
        elif self.kwargs['approval'] == str(5):
            user_status = "self_deactivation"
        elif self.kwargs['approval'] == str(6):
            user_status = "Deleted"
        else:
            pass

        user_type = self.kwargs['user_type']
        admin_id = self.request.GET['user_id']
        print(
            f"admin-------userssss--{user_status}---------{user_type}--------{admin_id}")
        if user_status in ["Approved", "pending", "Rejected", "Deactivated", "Approval_requested", "self_deactivation", "Deleted"]:
            if user_type in ['doctor', 'researcher', 'influencer']:
                user_obj = get_user_model().objects.filter(
                    groups__name__exact=user_type, approval__exact=user_status)
            else:
                user_obj = get_user_model().objects.filter(groups__name__in=[
                    'doctor', 'researcher', 'influencer'], approval__exact=user_status)
        else:
            if user_type in ['doctor', 'researcher', 'influencer']:
                user_obj = get_user_model().objects.filter(groups__name__exact=user_type, approval__in=[
                    "Approved", "pending", "Rejected", "Deactivated", "Approval_requested", "self_deactivation", "Deleted"])
            else:
                user_obj = get_user_model().objects.filter(groups__name__in=['doctor', 'researcher', 'influencer'], approval__in=[
                    "Approved", "pending", "Rejected", "Deactivated", "Approval_requested", "self_deactivation", "Deleted"])

        user_z = user_obj.distinct()
        if 'start_date' in self.request.GET and self.request.GET['start_date'] != '':
            start_date = timezone.make_aware(parse_datetime(self.request.GET['start_date']),
                                             timezone.get_current_timezone())
            exp_list = []
            print(f"-------------------------------in start_date{start_date}")
            user_z = user_z.filter(DateOfRegistration__gte=start_date)

        elif 'end_date' in self.request.GET and self.request.GET['end_date'] != '':
            end_date = timezone.make_aware(parse_datetime(
                self.request.GET['end_date']), timezone.get_current_timezone())
            exp_list = []
            user_z = user_z.filter(DateOfRegistration__lte=end_date)
            print(f"------------------in end_date{end_date}----------------")

        elif 'name' in self.request.GET and self.request.GET['name']:
            try:
                name_id = int(self.request.GET['name'])
                user_z = user_z.filter(id__exact=name_id) | user_z.filter(
                    name__icontains=self.request.GET['name'])
            except ValueError:
                user_z = user_z.filter(
                    name__icontains=self.request.GET['name'])

        elif 'rank' in self.request.GET and self.request.GET['rank'] != '':
            user_z = user_z.filter(expertrank__rank=self.request.GET['rank'])

        elif "review" in self.request.GET and self.request.GET['review'] != '':
            if self.request.GET['review'] == 'ext':
                user_z = user_z.filter(doctorreviews__Review__isnull=False)
                print(
                    f'---------review------------{user_z}--------------------')
            elif self.request.GET['review'] == 'int':
                user_z = user_z.filter(
                    appointments__patientstories__Rating__isnull=False)
                print(
                    f'---------review------------{user_z}--------------------')
            else:
                print({"message": "invalid review filter"})

        else:
            pass
        return user_z

    def get(self, request, *args, **kwargs):
        res = self.list(request, *args, **kwargs)
        total_items = len(res.data)
        print(f"experts data---------------{res.data}")
        y = json.loads(json.dumps(res.data))
        # Get the page number from the request
        page_number = request.GET.get('page', 1)
        # Get the number of items per page from the request
        items_per_page = request.GET.get('per_page', 10)
        paginator = Paginator(y, items_per_page)
        if int(page_number) not in range(1, int(paginator.num_pages)+1):
            return HttpResponse("Not a valid page number", status=400)
        y = paginator.page(page_number)
        print(f"dicttttttt----------{y}")
        # added
        user_count = get_user_model().objects.all()
        exp_list = []
        for a in y:
            user_d = CuUser.objects.filter(id__exact=a['id'])[0]
            print(f"group-------------{user_d.groups.all()[0].name}")
            a['user_role'] = user_d.groups.all()[0].name
            doc_obj = DoctorDetails.objects.filter(DoctorId_id__exact=a['id'])
            if doc_obj.exists():

                # timezone.localtime(user_d.doctordetails.DateOfActivation,timezone.get_current_timezone()) #added
                a['date_of_activation'] = user_d.doctordetails.DateOfActivation
                a['commission_percentage'] = user_d.doctordetails.CommissionPercentage
                a['consultation_fees'] = user_d.doctordetails.ConsultationFees
                print(
                    f"user detailsssssssssssssssss{user_d.id}------------------{user_d.doctordetails.ProfilePhoto}")
                #
                if user_d.doctordetails.ProfilePhoto is not None:
                    ddetails = DoctorDetails.objects.get(
                        DoctorId__exact=user_d.id)
                    new_obj_url = get_s3_signed_url_bykey(
                        ddetails.ProfilePhoto)
                    ddetails.ProfilePhoto = new_obj_url
                    a['profile_photo'] = ddetails.ProfilePhoto
            else:
                pass
            dd = StatusReason.objects.filter(
                ExpertId_id__exact=a['id'], ReasonType="Reactivation", ReasonCategory="user_reactivation")
            if (user_d.approval == "Approval_requested") and (dd.exists()):
                re_a = StatusReason.objects.filter(
                    ExpertId_id__exact=a['id'], ReasonType="Reactivation", ReasonCategory="user_reactivation").order_by("-CurrentTime")[0]
                a["profile_reactivation_reason"] = re_a.Reason
            exp_rank = ExpertRank.objects.filter(ExpertId_id__exact=a['id'])
            if exp_rank.exists():
                a['expert_rank'] = exp_rank[0].rank
            else:
                a['expert_rank'] = None
            Apps = Appointments.objects.filter(slot_id__doctor__exact=a['id'])
            doctors_s = CuUser.objects.filter(id__exact=a['id'])
            stories_count = 0
            reviews_count = 0
            rating_s = 0
            rating_r = 0
            for z in Apps:
                stories = z.patientstories_set
                stories_count += stories.count()
                for v in stories.all():
                    rating_s += v.Rating
            for z in doctors_s:
                reviews = z.doctorreviews_set
                for u in reviews.all():
                    if u.ReviewStatus == 2:
                        rating_r += u.ReviewRating
                        reviews_count += 1
            rating_in_number1 = int(
                rating_s/stories_count) if stories_count != 0 else 0
            rating_in_number2 = int(
                rating_r/reviews_count) if reviews_count != 0 else 0
            rating_in_number = int((rating_in_number1+rating_in_number2)/2)
            # print(f"appssssssssssssssss{stories_count}------------{rating}-------{x.id}----------{Apps}---")
            a["patient_stories"] = stories_count
            a["doctor_reviews"] = reviews_count
            a["rating"] = rating_in_number
            exp_list.append(a)

        y = exp_list

        # added
        print(y)
        doctor_no = dict()
        doctor_no.update({"total doctor number": user_count.filter(groups__name__exact='doctor').count(),
                          "approved": CuUser.objects.filter(approval__exact="Approved", groups__name__exact='doctor').count(),
                          "deactivated": CuUser.objects.filter(approval__exact="Deactivated", groups__name__exact='doctor').count(),
                          "self_deactivated": CuUser.objects.filter(approval__exact="self_deactivation", groups__name__exact='doctor').count(),
                          "approval_requested": CuUser.objects.filter(approval__exact="Approval_requested", groups__name__exact='doctor').count(),
                          "rejected": CuUser.objects.filter(approval__exact="Rejected", groups__name__exact='doctor').count(),
                          "pending": CuUser.objects.filter(approval__exact="pending", groups__name__exact='doctor').count(),
                          "deleted": CuUser.objects.filter(approval__exact="Deleted", groups__name__exact='doctor').count(),
                          })
        res_no = dict()
        res_no.update({"total reseracher number": user_count.filter(groups__name__exact='researcher').count(),
                       "approved": CuUser.objects.filter(approval__exact="Approved", groups__name__exact='researcher').count(),
                       "deactivated": CuUser.objects.filter(approval__exact="Deactivated", groups__name__exact='researcher').count(),
                       "self_deactivated": CuUser.objects.filter(approval__exact="self_deactivation", groups__name__exact='researcher').count(),
                       "approval_requested": CuUser.objects.filter(approval__exact="Approval_requested", groups__name__exact='researcher').count(),
                       "rejected": CuUser.objects.filter(approval__exact="Rejected", groups__name__exact='researcher').count(),
                       "pending": CuUser.objects.filter(approval__exact="pending", groups__name__exact='researcher').count(),
                       "deleted": CuUser.objects.filter(approval__exact="Deleted", groups__name__exact='researcher').count(),
                       })

        print(
            f"--------------eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee{user_count.filter(groups__name__exact='researcher')}")
        inf_no = dict()
        inf_no.update({"total influencer number": user_count.filter(groups__name__exact='influencer').count(),
                       "approved": CuUser.objects.filter(approval__exact="Approved", groups__name__exact='influencer').count(),
                       "deactivated": CuUser.objects.filter(approval__exact="Deactivated", groups__name__exact='influencer').count(),
                       "self_deactivated": CuUser.objects.filter(approval__exact="self_deactivation", groups__name__exact='influencer').count(),
                       "approval_requested": CuUser.objects.filter(approval__exact="Approval_requested", groups__name__exact='influencer').count(),
                       "rejected": CuUser.objects.filter(approval__exact="Rejected", groups__name__exact='influencer').count(),
                       "pending": CuUser.objects.filter(approval__exact="pending", groups__name__exact='influencer').count(),
                       "deleted": CuUser.objects.filter(approval__exact="Deleted", groups__name__exact='influencer').count(),
                       })
        appointments_data = dict()
        appointments_data.update({"total_upcoming_consultations": Appointments.objects.filter(status__in=['B', 'P', 'R'], slot_id_id__schedule_start_time__gte=timezone.now()).count(),
                                  "total_completed_consultations": MeetingSession.objects.filter(IsSuccess=1).count(),
                                  "total_cancelled_consultations": Appointments.objects.filter(status="C").count(),
                                  "total_rescheduled_consultations": Appointments.objects.filter(status="R").count(),
                                  "total_unattended_consultations": MeetingSession.objects.filter(AppointmentId__slot_id_id__schedule_start_time__lte=timezone.now(), AppointmentId__status__in=['B', 'P', 'R'], IsSuccess__in=['0', '2']).count()
                                  })
        app_u_role = ['doctor', 'influencer', 'researcher']
        for aaa in app_u_role:
            latest_app = MeetingSession.objects.filter(
                AppointmentId__slot_id_id__doctor__groups__name__exact=aaa, IsSuccess=1).order_by('-SessionStartTime').first()

            print(latest_app)
            if latest_app is not None:
                patient_data = filter_user_data(CuUser.objects.get(
                    id__exact=latest_app.AppointmentId.patient_id))
                patient_name = patient_data['name']
                patient_profile_photo = patient_data['patient_other_details']['ProfilePhoto']
                if patient_profile_photo is not None:
                    new_obj_url = get_s3_signed_url_bykey(
                        patient_profile_photo)
                    patient_profile_photo = new_obj_url

                doctor_id = SchedulerSlots.objects.filter(
                    id__exact=latest_app.AppointmentId.slot_id_id)[0]
                print(doctor_id)
                doctor_data = filter_user_data(
                    CuUser.objects.get(id__exact=doctor_id.doctor_id))
                doctor_name = doctor_data['name']
                doctor_profile_photo = doctor_data['doctor_other_details']['ProfilePhoto']
                if doctor_profile_photo is not None:
                    new_obj_url = get_s3_signed_url_bykey(doctor_profile_photo)
                    doctor_profile_photo = new_obj_url
                appointments_data.update({f"{aaa}_appointments_data": {"upcoming_consultations": Appointments.objects.filter(slot_id_id__doctor__groups__name__exact=aaa, status__in=['B', 'P', 'R'], slot_id_id__schedule_start_time__gte=timezone.now()).count(),
                                                                       "cancelled_consultations": Appointments.objects.filter(slot_id_id__doctor__groups__name__exact=aaa, status="C").count(),
                                                                       "completed_consultations": MeetingSession.objects.filter(AppointmentId__slot_id_id__doctor__groups__name__exact=aaa, MeetingStatus=2).count(),
                                                                       "unattended_consultations": MeetingSession.objects.filter(AppointmentId__slot_id_id__doctor__groups__name__exact=aaa, AppointmentId__slot_id_id__schedule_start_time__lte=timezone.now(), AppointmentId__status__in=['B', 'P', 'R'], MeetingStatus__in=['0', '3']).count(),
                                                                       "latest_appointment_data": {"expert_id": doctor_id.doctor_id, "approval": doctor_data['approval'], "expert_name": doctor_name, "expert_email": doctor_data['email'], "expert_profile_photo": doctor_profile_photo, "patient_name": patient_name, "patient_profile_photo": patient_profile_photo, "appointment_date": timezone.localtime(doctor_id.schedule_start_time).date(), "appointment_session_start_time": latest_app.SessionStartTime, "appointment_session_end_time": latest_app.SessionEndTime}}})

            else:
                appointments_data.update({f"{aaa}_appointments_data": {"upcoming_consultations": Appointments.objects.filter(slot_id_id__doctor__groups__name__exact=aaa, status__in=['B', 'P', 'R'], slot_id_id__schedule_start_time__gte=timezone.now()).count(),
                                                                       "cancelled_consultations": Appointments.objects.filter(slot_id_id__doctor__groups__name__exact=aaa, status="C").count(),
                                                                       "completed_consultations": MeetingSession.objects.filter(AppointmentId__slot_id_id__doctor__groups__name__exact=aaa, MeetingStatus=2).count(),
                                                                       "unattended_consultations": MeetingSession.objects.filter(AppointmentId__slot_id_id__doctor__groups__name__exact=aaa, AppointmentId__slot_id_id__schedule_start_time__lte=timezone.now(), AppointmentId__status__in=['B', 'P', 'R'], MeetingStatus__in=['0', '3']).count(),
                                                                       "latest_appointment_data": "no_data"}})
    # fetching experts approval_requested by type
        approval_experts = dict()
        d_data = [serialize_model(t, CuUserFilterSerializer) for t in CuUser.objects.filter(
            approval__exact="Approval_requested", groups__name__exact="doctor")]
        exp_list = []
        for a in d_data:
            user_d = CuUser.objects.filter(id__exact=a['id'])[0]
            print(f"group-------------{user_d.groups.all()[0].name}")
            a['user_role'] = user_d.groups.all()[0].name
            doc_obj = DoctorDetails.objects.filter(DoctorId_id__exact=a['id'])
            if doc_obj.exists():

                # timezone.localtime(user_d.doctordetails.DateOfActivation,timezone.get_current_timezone()) #added
                a['date_of_activation'] = user_d.doctordetails.DateOfActivation
                a['commission_percentage'] = user_d.doctordetails.CommissionPercentage
                a['consultation_fees'] = user_d.doctordetails.ConsultationFees
                print(
                    f"user detailsssssssssssssssss{user_d.id}------------------{user_d.doctordetails.ProfilePhoto}")
                #
                if user_d.doctordetails.ProfilePhoto is not None:
                    ddetails = DoctorDetails.objects.get(
                        DoctorId__exact=user_d.id)
                    new_obj_url = get_s3_signed_url_bykey(
                        ddetails.ProfilePhoto)
                    ddetails.ProfilePhoto = new_obj_url
                    a['profile_photo'] = ddetails.ProfilePhoto
            else:
                pass
            dd = StatusReason.objects.filter(
                ExpertId_id__exact=a['id'], ReasonType="Reactivation", ReasonCategory="user_reactivation")
            if (user_d.approval == "Approval_requested") and (dd.exists()):
                re_a = StatusReason.objects.filter(
                    ExpertId_id__exact=a['id'], ReasonType="Reactivation", ReasonCategory="user_reactivation").order_by("-CurrentTime")[0]
                a["profile_reactivation_reason"] = re_a.Reason
            exp_rank = ExpertRank.objects.filter(ExpertId_id__exact=a['id'])
            if exp_rank.exists():
                a['expert_rank'] = exp_rank[0].rank
            else:
                a['expert_rank'] = None
            Apps = Appointments.objects.filter(slot_id__doctor__exact=a['id'])
            doctors_s = CuUser.objects.filter(id__exact=a['id'])
            stories_count = 0
            reviews_count = 0
            rating_s = 0
            rating_r = 0
            for z in Apps:
                stories = z.patientstories_set
                stories_count += stories.count()
                for v in stories.all():
                    rating_s += v.Rating
            for z in doctors_s:
                reviews = z.doctorreviews_set
                for u in reviews.all():
                    if u.ReviewStatus == 2:
                        rating_r += u.ReviewRating
                        reviews_count += 1
            rating_in_number1 = int(
                rating_s/stories_count) if stories_count != 0 else 0
            rating_in_number2 = int(
                rating_r/reviews_count) if reviews_count != 0 else 0
            rating_in_number = int((rating_in_number1+rating_in_number2)/2)
            # print(f"appssssssssssssssss{stories_count}------------{rating}-------{x.id}----------{Apps}---")
            a["patient_stories"] = stories_count
            a["doctor_reviews"] = reviews_count
            a["rating"] = rating_in_number
            exp_list.append(a)
        d_data = exp_list
        i_data = [serialize_model(t, CuUserFilterSerializer) for t in CuUser.objects.filter(
            approval__exact="Approval_requested", groups__name__exact="influencer")]
        exp_list = []
        for a in i_data:
            user_d = CuUser.objects.filter(id__exact=a['id'])[0]
            print(f"group-------------{user_d.groups.all()[0].name}")
            a['user_role'] = user_d.groups.all()[0].name
            doc_obj = DoctorDetails.objects.filter(DoctorId_id__exact=a['id'])
            if doc_obj.exists():

                # timezone.localtime(user_d.doctordetails.DateOfActivation,timezone.get_current_timezone()) #added
                a['date_of_activation'] = user_d.doctordetails.DateOfActivation
                a['commission_percentage'] = user_d.doctordetails.CommissionPercentage
                a['consultation_fees'] = user_d.doctordetails.ConsultationFees
                print(
                    f"user detailsssssssssssssssss{user_d.id}------------------{user_d.doctordetails.ProfilePhoto}")
                #
                if user_d.doctordetails.ProfilePhoto is not None:
                    ddetails = DoctorDetails.objects.get(
                        DoctorId__exact=user_d.id)
                    new_obj_url = get_s3_signed_url_bykey(
                        ddetails.ProfilePhoto)
                    ddetails.ProfilePhoto = new_obj_url
                    a['profile_photo'] = ddetails.ProfilePhoto
            else:
                pass
            dd = StatusReason.objects.filter(
                ExpertId_id__exact=a['id'], ReasonType="Reactivation", ReasonCategory="user_reactivation")
            if (user_d.approval == "Approval_requested") and (dd.exists()):
                re_a = StatusReason.objects.filter(
                    ExpertId_id__exact=a['id'], ReasonType="Reactivation", ReasonCategory="user_reactivation").order_by("-CurrentTime")[0]
                a["profile_reactivation_reason"] = re_a.Reason
            exp_rank = ExpertRank.objects.filter(ExpertId_id__exact=a['id'])
            if exp_rank.exists():
                a['expert_rank'] = exp_rank[0].rank
            else:
                a['expert_rank'] = None
            Apps = Appointments.objects.filter(slot_id__doctor__exact=a['id'])
            doctors_s = CuUser.objects.filter(id__exact=a['id'])
            stories_count = 0
            reviews_count = 0
            rating_s = 0
            rating_r = 0
            for z in Apps:
                stories = z.patientstories_set
                stories_count += stories.count()
                for v in stories.all():
                    rating_s += v.Rating
            for z in doctors_s:
                reviews = z.doctorreviews_set
                for u in reviews.all():
                    if u.ReviewStatus == 2:
                        rating_r += u.ReviewRating
                        reviews_count += 1
            rating_in_number1 = int(
                rating_s/stories_count) if stories_count != 0 else 0
            rating_in_number2 = int(
                rating_r/reviews_count) if reviews_count != 0 else 0
            rating_in_number = int((rating_in_number1+rating_in_number2)/2)
            # print(f"appssssssssssssssss{stories_count}------------{rating}-------{x.id}----------{Apps}---")
            a["patient_stories"] = stories_count
            a["doctor_reviews"] = reviews_count
            a["rating"] = rating_in_number
            exp_list.append(a)
        i_data = exp_list
        r_data = [serialize_model(t, CuUserFilterSerializer) for t in CuUser.objects.filter(
            approval__exact="Approval_requested", groups__name__exact="researcher")]
        exp_list = []
        for a in r_data:
            user_d = CuUser.objects.filter(id__exact=a['id'])[0]
            print(f"group-------------{user_d.groups.all()[0].name}")
            a['user_role'] = user_d.groups.all()[0].name
            doc_obj = DoctorDetails.objects.filter(DoctorId_id__exact=a['id'])
            if doc_obj.exists():

                # timezone.localtime(user_d.doctordetails.DateOfActivation,timezone.get_current_timezone()) #added
                a['date_of_activation'] = user_d.doctordetails.DateOfActivation
                a['commission_percentage'] = user_d.doctordetails.CommissionPercentage
                a['consultation_fees'] = user_d.doctordetails.ConsultationFees
                print(
                    f"user detailsssssssssssssssss{user_d.id}------------------{user_d.doctordetails.ProfilePhoto}")
                #
                if user_d.doctordetails.ProfilePhoto is not None:
                    ddetails = DoctorDetails.objects.get(
                        DoctorId__exact=user_d.id)
                    new_obj_url = get_s3_signed_url_bykey(
                        ddetails.ProfilePhoto)
                    ddetails.ProfilePhoto = new_obj_url
                    a['profile_photo'] = ddetails.ProfilePhoto
            else:
                pass
            dd = StatusReason.objects.filter(
                ExpertId_id__exact=a['id'], ReasonType="Reactivation", ReasonCategory="user_reactivation")
            if (user_d.approval == "Approval_requested") and (dd.exists()):
                re_a = StatusReason.objects.filter(
                    ExpertId_id__exact=a['id'], ReasonType="Reactivation", ReasonCategory="user_reactivation").order_by("-CurrentTime")[0]
                a["profile_reactivation_reason"] = re_a.Reason
            exp_rank = ExpertRank.objects.filter(ExpertId_id__exact=a['id'])
            if exp_rank.exists():
                a['expert_rank'] = exp_rank[0].rank
            else:
                a['expert_rank'] = None
            Apps = Appointments.objects.filter(slot_id__doctor__exact=a['id'])
            doctors_s = CuUser.objects.filter(id__exact=a['id'])
            stories_count = 0
            reviews_count = 0
            rating_s = 0
            rating_r = 0
            for z in Apps:
                stories = z.patientstories_set
                stories_count += stories.count()
                for v in stories.all():
                    rating_s += v.Rating
            for z in doctors_s:
                reviews = z.doctorreviews_set
                for u in reviews.all():
                    if u.ReviewStatus == 2:
                        rating_r += u.ReviewRating
                        reviews_count += 1
            rating_in_number1 = int(
                rating_s/stories_count) if stories_count != 0 else 0
            rating_in_number2 = int(
                rating_r/reviews_count) if reviews_count != 0 else 0
            rating_in_number = int((rating_in_number1+rating_in_number2)/2)
            # print(f"appssssssssssssssss{stories_count}------------{rating}-------{x.id}----------{Apps}---")
            a["patient_stories"] = stories_count
            a["doctor_reviews"] = reviews_count
            a["rating"] = rating_in_number
            exp_list.append(a)
        r_data = exp_list
        approval_experts.update({'doctor_approval_requests': d_data,
                                 'influencer_requests': i_data,
                                 'researcher_requests': r_data})
        return JsonResponse({'total_items': total_items,
                             'total_pages': paginator.num_pages, "experts_data": y, "total_experts": total_items, "doctor_no": doctor_no, "researcher_no": res_no, "influencer_no": inf_no, "appointments_requests": appointments_data, "approval_requested_data": approval_experts})


def SendPushNewDRIRegister(user_id, new_user_name, role, current_time):
    device = FCMDevice.objects.filter(user_id=user_id).first()

    if not device:
        print(f"No FCM device found for user {user_id}")
        return

    user = get_user_model().objects.get(id=user_id)
    notification = Message(
        notification=Notification(
            title=f'Welcome our new {role}!',
            body=f'Hi {user.name}, Let us welcome {new_user_name} who joined our team as {role}!'
        ),
        webpush=WebpushConfig(
            fcm_options=WebpushFCMOptions(link=os.getenv(
                'NEXT_CANCER_UNWIRED_PATIENT_APP') + '/meetexpert')
        )
    )

    try:
        response = device.send_message(notification)
        PushNotifications.objects.create(UserId=user_id, NotificationTime=current_time,
                                         Title='Welcome new doctor/researcher/influencer',
                                         Body=f'Hi {user.name}, Let us welcome {new_user_name}!',
                                         Link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/meetexpert')
        return response
    except Exception as e:
        print(f"Failed to send push notification: {e}")


def SendPushRejected(email, status, name, reason, role):
    user = CuUser.objects.filter(email=email).first()
    if not user:
        print(f"No user found with email {email}")
        return

    device = FCMDevice.objects.filter(user_id=user.id).first()
    if not device:
        print(f"No FCM device found for user {user.id}")
        return

    host = os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP") if role == "patient" else os.getenv(
        "NEXT_CANCER_UNWIRED_DOCTOR_APP")
    profile_url = "/profilesettings" if role == "patient" else "/profile-Setting"

    if status == "self_deactivation":
        verify_url = host + "/reactivateProfile"
        status_message = "rejected for reactivation"
    else:
        verify_url = host + profile_url
        status_message = status

    notification = Message(
        notification=Notification(
            title='Admin action on profile!',
            body=f'Hi {name}, Your profile has been {status_message} by admin.'
        ),
        webpush=WebpushConfig(
            fcm_options=WebpushFCMOptions(link=verify_url)
        )
    )

    try:
        response = device.send_message(notification)
        PushNotifications.objects.create(UserId=user.id, NotificationTime=timezone.now(),
                                         Title='Admin action on profile!',
                                         Body=f'Your profile has been {status_message} by admin.',
                                         Link=verify_url)
        return response
    except Exception as e:
        print(f"Failed to send push notification: {e}")


def SendEmailProfileAction(u_email, status, name, reason, u_role):
    host = ''
    r_r_r = ''
    if reason != "":
        reason = " due to " + reason
    else:
        pass
    if u_role == "patient":
        host = os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP")
        r_r_r = "/profilesettings"
    elif u_role in ['doctor', 'researcher', 'influencer']:
        host = os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP")
        r_r_r = "/profile-Setting"
    else:
        pass

    if status in ["Rejected", "Deactivated", "approved", "updated"]:
        verify_url1 = host + r_r_r
        to_do = "go to your profile page"
    elif status in ["self_deactivation"]:
        verify_url1 = host + "/reactivateProfile"
        to_do = "request for reactivation again"
        status = "rejected for reactivation"
    else:
        return None

    payload = {
        "template_key": "2518b.41c485fda42f6e5f.k1.a21c8df0-2944-11ef-8359-525400ab18e6.190100a344f",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": u_email,
                    "name": u_email,
                }
            }

        ],
        "merge_info": {
            "u_name": name,
            "link": verify_url1,
            "status": status,
            "reason": reason,
            "to_do": to_do,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }

    headers = {
        'Authorization': f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
    try:
        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload))
        print(
            f"pw mail send-----------{response.status_code}-------{response.content}--------")
        response_status = True if response.status_code == 200 else True if response.status_code in [
            201, 202] else False
        return response_status
    except HTTPError as e:
        print(f"exception-----------{e.to_dict}")
        return response_status


class UpdateExpertStatus(generics.UpdateAPIView):

    permission_classes = [IsCuAdminPermission]
    queryset = get_user_model().objects.all()
    lookup_field = 'id'
    serializer_class = CuUserSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()

        print(
            f"update exp statussssssssssss--------{self.kwargs.get('id')}---------{request.data}")

        if request.data['approval'] == "Approved":
            if instance.approval == "Approval_requested":
                instance.approval = request.data['approval']
                instance.save(update_fields=['approval'])
                if get_cu_user_type(self.kwargs['id']) in ['doctor', 'researcher', 'influencer']:
                    doctor_details = DoctorDetails.objects.get(
                        DoctorId_id__exact=self.kwargs['id'])
                    doctor_details.DateOfActivation = timezone.now()
                    doctor_details.save()
                    if ExpertWallet.objects.filter(ExpertId_id__exact=self.kwargs['id']).exists():
                        pass
                    else:
                        ExpertWallet.objects.create(ExpertId=instance)
                else:
                    pass
            else:
                return "Only users requesting approval can be approved or rejected"

        elif request.data['approval'] == "Rejected":
            if instance.approval == "Approval_requested":
                d_r = StatusReason.objects.filter(ExpertId=get_user_model().objects.get(
                    id__exact=self.kwargs.get('id')), ReasonType__exact="Deactivation")
                r_r = StatusReason.objects.filter(ExpertId=get_user_model().objects.get(
                    id__exact=self.kwargs.get('id')), ReasonType__exact="Reactivation")
                if d_r.exists() and r_r.exists():
                    print(f"-------sone")
                    d_r = d_r.order_by('-CurrentTime')[0]
                    r_r = r_r.order_by('-CurrentTime')[0]
                    if d_r.CurrentTime > r_r.CurrentTime:
                        StatusReason.objects.create(ExpertId=get_user_model().objects.get(id__exact=self.kwargs.get(
                            'id')), Reason=request.data['StatusChangeReason'], ReasonType="Reactivation_Rejection", ReasonCategory=request.data['ReasonCategory'])  # request.data['ReasonType']
                        # Deactivation_Rejection_Email
                        instance.approval = "Deactivated"
                    #    request.data['approval']="Deactivated"
                        instance.save(update_fields=['approval'])
                    else:
                        # Patient deactivation rejection
                        user_role = get_cu_user_type(self.kwargs['id'])
                        if user_role == "patient":
                            StatusReason.objects.create(ExpertId=get_user_model().objects.get(id__exact=self.kwargs.get(
                                'id')), Reason=request.data['StatusChangeReason'], ReasonType="Reactivation_Rejection", ReasonCategory=request.data['ReasonCategory'])
                            instance.approval = "Deactivated"
                        elif user_role in ['doctor', 'influencer', 'researcher']:
                            StatusReason.objects.create(ExpertId=get_user_model().objects.get(id__exact=self.kwargs.get(
                                'id')), Reason=request.data['StatusChangeReason'], ReasonType="Self_Reactivation_Rejection", ReasonCategory=request.data['ReasonCategory'])
                            instance.approval = "self_deactivation"
                        #    request.data['approval']="self_deactivation"
                        instance.save(update_fields=['approval'])
                elif d_r.exists():
                    print("ffffffffffffffffffffffffffff")
                    StatusReason.objects.create(ExpertId=get_user_model().objects.get(id__exact=self.kwargs.get(
                        'id')), Reason=request.data['StatusChangeReason'], ReasonType="Reactivation_Rejection", ReasonCategory=request.data['ReasonCategory'])

                    # request.data['approval']
                    instance.approval = "Deactivated"
                #    request.data['approval']="Deactivated"
                    instance.save(update_fields=['approval'])
                elif r_r.exists():
                    print("rrrrrrrrrrrrrrrrrrrrrrrrrrrrr")
                    # Patient deactivation rejection
                    user_role = get_cu_user_type(self.kwargs['id'])
                    if user_role == "patient":
                        StatusReason.objects.create(ExpertId=get_user_model().objects.get(id__exact=self.kwargs.get(
                            'id')), Reason=request.data['StatusChangeReason'], ReasonType="Reactivation_Rejection", ReasonCategory=request.data['ReasonCategory'])
                        instance.approval = "Deactivated"
                    elif user_role in ['doctor', 'influencer', 'researcher']:
                        StatusReason.objects.create(ExpertId=get_user_model().objects.get(id__exact=self.kwargs.get(
                            'id')), Reason=request.data['StatusChangeReason'], ReasonType="Self_Reactivation_Rejection", ReasonCategory=request.data['ReasonCategory'])
                        instance.approval = "self_deactivation"
                #    request.data['approval']="self_deactivation"
                    instance.save(update_fields=['approval'])

                else:
                    print("zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz")
                    StatusReason.objects.create(ExpertId=get_user_model().objects.get(id__exact=self.kwargs.get(
                        'id')), Reason=request.data['StatusChangeReason'], ReasonType=request.data['ReasonType'], ReasonCategory=request.data['ReasonCategory'])

                    instance.approval = request.data['approval']
                    instance.save(update_fields=['approval'])

            else:
                return "Only users requesting approval can be approved or rejected"

        elif request.data['approval'] == "Deactivated":
            if instance.approval == "Approved":
                print("dddddddddddddddddddddddddddddddd")
                StatusReason.objects.create(ExpertId=get_user_model().objects.get(id__exact=self.kwargs.get(
                    'id')), Reason=request.data['StatusChangeReason'], ReasonType=request.data['ReasonType'], ReasonCategory=request.data['ReasonCategory'])
                instance.approval = request.data['approval']
                instance.save(update_fields=['approval'])
            else:
                return "Only approved users can be deactivated"
        else:
            return "Invalid request"

        return Response(instance, status=200)

    def put(self, request, *args, **kwargs):
        logger.info(
            f"Expert approval request received for user ID: {self.kwargs.get('id')}")

        try:
            # Validate the 'approval' field
            approval = request.data.get('approval')
            if approval not in ["Approved", "Rejected", "Deactivated"]:
                return Response({"error": "Invalid approval status"}, status=status.HTTP_400_BAD_REQUEST)

            # Partial update of the user
            a = self.partial_update(request, *args, **kwargs)

            if isinstance(a, Response) and a.status_code == 200:
                user_id = self.kwargs.get('id')
                u_data = CuUser.objects.filter(id=user_id).first()
                if not u_data:
                    return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)

                user_role = get_cu_user_type(user_id)
                if not user_role:
                    return Response({"error": "User role not found"}, status=status.HTTP_404_NOT_FOUND)

                if approval == "Approved":
                    if user_role in ['doctor', 'researcher', 'influencer']:
                        current_date = timezone.now()

                        # Add user data to Zoho accounts
                        acc_zoho = AddToZoho(
                            CuUser.objects.get(email=u_data.email))

                        # Send email and push notification
                        if SendEmailProfileAction(u_data.email, "approved", u_data.name, "", user_role):
                            logger.info("Approval email sent successfully")
                        if SendPushRejected(u_data.email, "approved", u_data.name, "", user_role):
                            logger.info(
                                "Approval push notification sent successfully")

                        # Notify patients about new doctor/researcher/influencer
                        patient_data = PatientDetails.objects.all().values()
                        for x in patient_data:
                            noti_check = NotificationType.objects.filter(
                                NotificationName='Welcome new doctor/researcher/influencer', CategoryType="N"
                            ).first()

                            if noti_check and noti_check.ActiveStatus == 1:
                                fcm_a = SendPushNewDRIRegister(
                                    x['PatientId_id'], u_data.name, user_role, current_date)
                                logger.info(f"Notification response: {fcm_a}")
                            else:
                                NotificationType.objects.create(
                                    NotificationName='Welcome new doctor/researcher/influencer', CategoryType="N"
                                )
                                fcm_a = SendPushNewDRIRegister(
                                    x['PatientId_id'], u_data.name, user_role, current_date)
                                logger.info(f"Notification response: {fcm_a}")

                    elif user_role == "patient":
                        # Add patient data to Zoho contacts
                        acc_zoho = AddToZohoContacts(
                            CuUser.objects.get(email=u_data.email))

                        # Send email and push notification
                        if SendEmailProfileAction(u_data.email, "approved", u_data.name, "", user_role):
                            logger.info("Approval email sent successfully")
                        if SendPushRejected(u_data.email, "approved", u_data.name, "", user_role):
                            logger.info(
                                "Approval push notification sent successfully")

                elif approval == "Rejected":
                    if user_role in ['doctor', 'researcher', 'influencer']:
                        acc_zoho = AddToZoho(
                            CuUser.objects.get(email=u_data.email))
                    elif user_role == "patient":
                        acc_zoho = AddToZohoContacts(
                            CuUser.objects.get(email=u_data.email))

                    # Handle rejection reasons and notifications
                    a = CuUser.objects.get(id=user_id)
                    if a.approval == "self_deactivation":
                        r_r = StatusReason.objects.filter(
                            ExpertId_id=user_id).order_by('-CurrentTime').first()
                        if r_r:
                            if SendEmailProfileAction(u_data.email, "self_deactivation", u_data.name, r_r.Reason, user_role):
                                logger.info(
                                    "Self-deactivation rejection email sent successfully")
                            if SendPushRejected(u_data.email, "self_deactivation", u_data.name, r_r.Reason, user_role):
                                logger.info(
                                    "Self-deactivation rejection push notification sent successfully")
                    elif a.approval in ["Deactivated", "Rejected"]:
                        d_r = StatusReason.objects.filter(
                            ExpertId_id=user_id).order_by('-CurrentTime').first()
                        if d_r:
                            if SendEmailProfileAction(a.email, "Rejected", u_data.name, d_r.Reason, user_role):
                                logger.info(
                                    "Rejection email sent successfully")
                            if SendPushRejected(a.email, "Rejected", u_data.name, d_r.Reason, user_role):
                                logger.info(
                                    "Rejection push notification sent successfully")

                elif approval == "Deactivated":
                    if user_role in ['doctor', 'researcher', 'influencer']:
                        acc_zoho = AddToZoho(
                            CuUser.objects.get(email=u_data.email))
                    elif user_role == "patient":
                        acc_zoho = AddToZohoContacts(
                            CuUser.objects.get(email=u_data.email))

                    # Handle deactivation reasons and notifications
                    d_r = StatusReason.objects.filter(
                        ExpertId=user_id,
                        Reason=request.data.get('StatusChangeReason'),
                        ReasonType=request.data.get('ReasonType'),
                        ReasonCategory=request.data.get('ReasonCategory')
                    ).order_by('-CurrentTime').first()

                    if d_r:
                        if SendEmailProfileAction(u_data.email, "Deactivated", u_data.name, d_r.Reason, user_role):
                            logger.info("Deactivation email sent successfully")
                        if SendPushRejected(u_data.email, "Deactivated", u_data.name, d_r.Reason, user_role):
                            logger.info(
                                "Deactivation push notification sent successfully")

                # Return success response
                return Response({"update_status": "success", "data": serialize_model(u_data, CuUserFilterSerializer)})

            else:
                return Response({"update_status": "failed", "data": a.data if hasattr(a, 'data') else str(a)}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error in UpdateExpertStatus: {str(e)}")
            return Response({"error": "Internal server error"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class GetPatientsView(generics.ListAPIView):
    permission_classes = [IsCuAdminPermission]
    queryset = get_user_model().objects.all()
    serializer_class = CuUserFilterSerializer

    def get_queryset(self):
        # added
        user_role = get_cu_user_type(self.kwargs['id'])
        if user_role in ['doctor', 'researcher', 'influencer']:
            exp_appointments = Appointments.objects.filter(
                slot_id__doctor__exact=self.kwargs['id'])
            patients_list = exp_appointments.values('patient').distinct()
            p_list = []
            for x in patients_list:
                p_list.append(x['patient'])

            patient_data = get_user_model().objects.filter(id__in=p_list)
            print(patient_data)
            return patient_data

        elif user_role == 'patient':
            exp_appointments = Appointments.objects.filter(
                patient_id__exact=self.kwargs['id'])
            slot_id_list = exp_appointments.values('slot_id')
            print(f'---------slot id list------{slot_id_list}--------')
            s_list = []
            for x in slot_id_list:
                s_list.append(x['slot_id'])

            d_list = []
            slot_data = SchedulerSlots.objects.filter(
                id__in=s_list).values('doctor_id').distinct()
            for d_dd in slot_data:
                d_list.append(d_dd['doctor_id'])

            doctor_data = get_user_model().objects.filter(id__in=d_list)
            return doctor_data

        else:
            return "No data"

   # added

    def get(self, request, *args, **kwargs):
        res = self.list(request, *args, **kwargs)
        data_list = []
        y = json.loads(json.dumps(res.data))
        # added
        p_list = []
        for x in y:
            p_list.append(x['id'])
        y = get_user_model().objects.filter(id__in=p_list)
        user_role = get_cu_user_type(self.kwargs['id'])
        if user_role in ['doctor', 'researcher', 'influencer']:
            for x in request.GET:
                if x == 'name' and request.GET['name'] != '':
                    y = y.filter(name__icontains=request.GET['name'])
                if x == 'id' and request.GET['id'] != '':
                    y = y.filter(id__exact=request.GET['id'])
                if x == 'start_date' and request.GET['start_date'] != '':
                    start_date = timezone.make_aware(parse_datetime(request.GET['start_date']),
                                                     timezone.get_current_timezone())
                    y = y.filter(DateOfRegistration__gte=start_date)
                if x == 'end_date' and request.GET['end_date'] != '':
                    end_date = timezone.make_aware(parse_datetime(request.GET['end_date']),
                                                   timezone.get_current_timezone())
                    y = y.filter(DateOfRegistration__lte=end_date)
                if x == 'status' and request.GET['status'] != '':
                    y = y.filter(approval__exact=request.GET['status'])
            if 'page' in request.GET and request.GET['page'] != "":

                # Get the page number from the request
                page_number = request.GET.get('page', 1)
                # Get the number of items per page from the request
                items_per_page = request.GET.get('per_page', 10)
                total_items = y.count()
                paginator = Paginator(y, items_per_page)
                print(paginator)
                if int(page_number) not in range(1, int(paginator.num_pages)+1):
                    return HttpResponse("Not a valid page number", status=400)
                y = paginator.page(page_number)
            for x in y:
                data = dict()
                a = get_user_model().objects.get(id=x.id).patientdetails
                data['details'] = serialize_model(x, CuUserFilterSerializer)
                data['other_details'] = serialize_model(
                    a, PatientDetailsSerializer)
                if data['other_details']['ProfilePhoto'] is not None:
                    new_obj_url = get_s3_signed_url_bykey(
                        data['other_details']['ProfilePhoto'])
                    data['other_details']['ProfilePhoto'] = new_obj_url
                if data['other_details']['Signature'] is not None:
                    new_obj_url = get_s3_signed_url_bykey(
                        data['other_details']['Signature'])
                    data['other_details']['Signature'] = new_obj_url
                app_data = Appointments.objects.filter(patient_id__exact=x.id)
                date2 = ''
                slot_id_latest = ''
                for zz in app_data:
                    date1 = SchedulerSlots.objects.filter(id__exact=zz.slot_id_id)[
                        0].schedule_start_time
                    if date2 == '' or date1 >= date2:
                        date2 = date1
                        slot_id_latest = zz.slot_id_id

                data['latest_appointment_date'] = str(
                    str(date2.date()) + ' ' + str(date2.time()))
                data['latest_appointment_data'] = serialize_model(Appointments.objects.get(
                    slot_id__exact=slot_id_latest), AppointmentsSerializer)
                data_list.append(data)
            patient_by_expert = dict()
            exp_appointments = Appointments.objects.filter(
                slot_id__doctor__exact=self.kwargs['id'])
            patients_number = exp_appointments.values(
                'patient').distinct().count()
            s_days = timezone.now() - timedelta(days=7)
            r_c = exp_appointments.filter(slot_id_id__schedule_start_time__lte=timezone.now(
            ), slot_id_id__schedule_end_time__gte=s_days).count()
            patient_by_expert.update({"total_patients": patients_number, "recent_consultations": r_c, "total_consultations": MeetingSession.objects.filter(
                AppointmentId__slot_id__doctor__exact=self.kwargs['id'], IsSuccess=1).count()})
            app_req_data = dict()
            app_req_data.update({"total_upcoming_consultations": Appointments.objects.filter(slot_id__doctor__exact=self.kwargs['id'], status__in=['B', 'P', 'R'], slot_id_id__schedule_start_time__gte=timezone.now()).count(),
                                 "total_completed_consultations": MeetingSession.objects.filter(AppointmentId__slot_id__doctor__exact=self.kwargs['id'], IsSuccess=1).count(),
                                 "total_cancelled_consultations": Appointments.objects.filter(slot_id__doctor__exact=self.kwargs['id'], status="C").count(),
                                 "total_rescheduled_consultations": Appointments.objects.filter(slot_id__doctor__exact=self.kwargs['id'], status="R").count(),
                                 "total_unattended_consultations": MeetingSession.objects.filter(AppointmentId__slot_id__doctor__exact=self.kwargs['id'], AppointmentId__slot_id_id__schedule_start_time__lte=timezone.now(), AppointmentId__status__in=['B', 'P', 'R'], IsSuccess__in=['0', '2']).count()
                                 })
            if 'page' in request.GET and request.GET['page'] != "":
                response_data = {
                    'total_items': total_items,
                    'total_pages': paginator.num_pages,
                    'items': data_list,
                    "patient_data_by_expert": patient_by_expert, "total_appointment_request_rate": app_req_data}
                return Response(response_data)
            else:
                return JsonResponse({"patient_data": data_list, "patient_data_by_expert": patient_by_expert, "total_appointment_request_rate": app_req_data})
        elif user_role == "patient":
            for x in y:
                data = dict()
                a = get_user_model().objects.get(id=x['id']).doctordetails
                data['details'] = x
                data['other_details'] = serialize_model(
                    a, DoctorDetailsSerializer)
                if data['other_details']['ProfilePhoto'] is not None:
                    new_obj_url = get_s3_signed_url_bykey(
                        data['other_details']['ProfilePhoto'])
                    data['other_details']['ProfilePhoto'] = new_obj_url
                if data['other_details']['Signature'] is not None:
                    new_obj_url = get_s3_signed_url_bykey(
                        data['other_details']['Signature'])
                    data['other_details']['Signature'] = new_obj_url
                app_data = Appointments.objects.filter(
                    slot_id__doctor__exact=x['id'])
                date2 = ''
                slot_id_latest = ''
                for zz in app_data:
                    date1 = SchedulerSlots.objects.filter(id__exact=zz.slot_id_id)[
                        0].schedule_start_time
                    if (date2 == '' or date1 >= date2) and zz.patient_id == self.kwargs['id']:
                        date2 = date1
                        slot_id_latest = zz.slot_id_id

                data['latest_appointment_date'] = str(
                    str(date2.date()) + ' ' + str(date2.time()))
                data['latest_appointment_data'] = serialize_model(Appointments.objects.get(
                    slot_id__exact=slot_id_latest), AppointmentsSerializer)
                data_list.append(data)
# added
            return JsonResponse({"doctor_data": data_list})

        else:
            return JsonResponse({"user_data": "No data"})


class GetPatientsByTypeView(generics.ListAPIView):
    permission_classes = [IsCuAdminPermission]
    serializer_class = CuUserFilterSerializer

    def get_queryset(self):
        y = PatientDetails.objects.all()
        if 'role' in self.request.GET and self.request.GET['role'] != '':
            y = y.filter(
                PatientId_id__appointments__slot_id__doctor__groups__name__exact=self.request.GET['role']).distinct()
            print(f"--------------role filter-----{y}")
        if 'start_date' in self.request.GET and self.request.GET['start_date'] != '':
            start_date = timezone.make_aware(parse_datetime(
                self.request.GET['start_date']), timezone.get_current_timezone())
            y = y.filter(PatientId_id__DateOfRegistration__gte=start_date)
            print(f"--------------start_date filter-------{y}")
        if 'end_date' in self.request.GET and self.request.GET['end_date'] != '':
            end_date = timezone.make_aware(parse_datetime(
                self.request.GET['end_date']), timezone.get_current_timezone())
            y = y.filter(PatientId_id__DateOfRegistration__lte=end_date)
            print(f"--------------end_date filter-------{y}")
        if 'status' in self.request.GET and self.request.GET['status'] != '':
            y = y.filter(
                PatientId_id__approval__exact=self.request.GET['status'])
            print(f"--------------status filter-------{y}")
        if 'name' in self.request.GET and self.request.GET['name'] != '':
            try:
                name_id = int(self.request.GET['name'])
                y = y.filter(PatientId_id__exact=name_id) | y.filter(
                    PatientId_id__name__icontains=self.request.GET['name'])
            except ValueError:
                y = y.filter(
                    PatientId_id__name__icontains=self.request.GET['name'])
            print(f"--------------name filter-------{y}")
        else:
            print(f"----------------invalid filter----------")
        y = y.values("PatientId_id")
        z = CuUser.objects.filter(id__in=y)
        return z

    def get(self, r, *args, **kwargs):
        y = self.list(self, r, *args, **kwargs)
        print(y.data)
        data_list = []
        t_p = PatientDetails.objects.all().count()
        t_dp = PatientDetails.objects.filter(
            PatientId_id__appointments__slot_id__doctor__groups__name__exact='doctor').distinct().count()
        t_rp = PatientDetails.objects.filter(
            PatientId_id__appointments__slot_id__doctor__groups__name__exact='researcher').distinct().count()
        t_ip = PatientDetails.objects.filter(
            PatientId_id__appointments__slot_id__doctor__groups__name__exact='influencer').distinct().count()
        total_items = len(y.data)
        y = json.loads(json.dumps(y.data))
        # Get the page number from the request
        page_number = r.GET.get('page', 1)
        # Get the number of items per page from the request
        items_per_page = r.GET.get('per_page', 10)
        paginator = Paginator(y, items_per_page)
        if int(page_number) not in range(1, int(paginator.num_pages)+1):
            return HttpResponse("Not a valid page number", status=400)
        y = paginator.page(page_number)
        for x in y:
            data = dict()
            print(x)
            a = get_user_model().objects.get(id=x['id'])
            data['patient details'] = serialize_model(
                a, CuUserFilterSerializer)
            data['other_details'] = serialize_model(
                a.patientdetails, PatientDetailsSerializer)
            if data['other_details']['ProfilePhoto'] is not None:
                new_obj_url = get_s3_signed_url_bykey(
                    data['other_details']['ProfilePhoto'])
                data['other_details']['ProfilePhoto'] = new_obj_url

            if data['other_details']['Signature'] is not None:
                new_obj_url = get_s3_signed_url_bykey(
                    data['other_details']['Signature'])
                data['other_details']['Signature'] = new_obj_url
            if 'role' in r.GET and r.GET['role'] != '':
                app_data = Appointments.objects.filter(
                    patient_id__exact=x['id'], slot_id__doctor__groups__name__exact=r.GET['role'])
            else:
                app_data = Appointments.objects.filter(
                    patient_id__exact=x['id'])
            if a.approval == "Deactivated":
                s_r = dict()
                dd = StatusReason.objects.filter(ExpertId_id__exact=a.id, ReasonType__in=[
                    "Deactivation", "Reactivation_Rejection"])
                if dd.exists():
                    dd = dd.order_by('-CurrentTime')[0]
                    s_r.update({'approval': "Deactivated",
                                "deactivated_reason": dd.Reason,
                                "deactivated_time": dd.CurrentTime,
                                "deactivated_category": dd.ReasonCategory})
                    data['approval_status_reason'] = s_r
            elif a.approval == "Approval_requested":
                s_r = dict()
                dd = StatusReason.objects.filter(
                    ExpertId_id__exact=a.id, ReasonType__exact="Reactivation")
                if dd.exists():
                    dd = dd.order_by('-CurrentTime')[0]
                    s_r.update({'approval': "Approval_requested",
                                "reactivation_reason": dd.Reason,
                                "reactivation_time": dd.CurrentTime})
                    data['approval_status_reason'] = s_r
            else:
                pass

            date2 = ''
            slot_id_latest = ''
            for zz in app_data:
                date1 = SchedulerSlots.objects.filter(id__exact=zz.slot_id_id)[
                    0].schedule_start_time
                if (date2 == '' or date1 >= date2) and zz.patient_id == x['id']:
                    date2 = date1
                    slot_id_latest = zz.slot_id_id
            if slot_id_latest != '':
                d_id = SchedulerSlots.objects.filter(
                    id__exact=slot_id_latest)[0].doctor_id
                d_data = get_user_model().objects.get(id=d_id)
                d_d = serialize_model(d_data, CuUserFilterSerializer)
                data['expert_name'] = d_d['name']
                data['expert_email'] = d_d['email']
                data['expert_id'] = d_d['id']
                data['expert_role'] = get_cu_user_type(d_id)
                d_dataaa = serialize_model(
                    d_data.doctordetails, DoctorDetailsSerializer)
                if d_dataaa['ProfilePhoto'] is not None:
                    new_obj_url = get_s3_signed_url_bykey(
                        d_dataaa['ProfilePhoto'])
                    data['expert_ProfilePhoto'] = new_obj_url

                data_list.append(data)
            else:
                data['booked_appoinement'] = "No appointment booked"
                data_list.append(data)

        appointments_data = dict()
        appointments_data.update({"total_upcoming_consultations": Appointments.objects.filter(status__in=['B', 'P', 'R'], slot_id_id__schedule_start_time__gte=timezone.now()).count(),
                                  "total_completed_consultations": MeetingSession.objects.filter(IsSuccess=1).count(),
                                  "total_cancelled_consultations": Appointments.objects.filter(status="C").count(),
                                  "total_rescheduled_consultations": Appointments.objects.filter(status="R").count(),
                                  "total_unattended_consultations": MeetingSession.objects.filter(AppointmentId__slot_id_id__schedule_start_time__lte=timezone.now(), AppointmentId__status__in=['B', 'P', 'R'], IsSuccess__in=['0', '2']).count()
                                  })

        return JsonResponse({"total_items": total_items,
                             "total_pages": paginator.num_pages,
                             "total_patients": t_p,
                             "total_doctor_patient": t_dp,
                             "total_influencer_patient": t_ip,
                             "total_researcher_patient": t_rp,
                             "patient_data": data_list,
                             "appointments_data": appointments_data})


class ApproveBlogView(generics.UpdateAPIView):
    serializer_class = ExpertBlogsSerializer
    permission_classes = [IsCuAdminPermission]
    lookup_field = 'id'
    queryset = ExpertBlogs.objects.all()

    def put(self, request, *args, **kwargs):
        print(f"blog approval-----------")
        a = self.partial_update(request, *args, **kwargs)
        print(f"a-------------{a}-------{type(a)}")
        if a is not None and a.status_code == 200:
            expert_id = ExpertBlogs.objects.filter(
                id=self.kwargs['id'])[0].ExpertId_id
            if request.data['BlogStatus'] == 2:
                expert = CuUser.objects.get(id__exact=expert_id)
                category = f"{self.kwargs['id']}_expert_blog_rejection"
                aa = StatusReason.objects.create(Reason=request.data['Reason'], CurrentTime=timezone.now(
                ), ExpertId_id=expert_id, ReasonCategory=category, ReasonType="Blog_Rejection")
            else:
                pass
            noti_check = notification_check('Blog approval!')
            e_check = email_check("Blog approval!")
            if noti_check == True:
                r_p = AdminApprovePushNotification(
                    'Blog approval!', expert_id, request.data['BlogStatus'], request.GET['user_id'], "N")
                print(
                    f'----------- admin approval status push response-------{r_p}------------ ')
            else:
                pass
            if e_check == True:
                r_p = AdminApprovePushNotification(
                    'Blog approval!', expert_id, request.data['BlogStatus'], request.GET['user_id'], "E")
                print(
                    f'-----------admin approval status email response-------{r_p}------------')
            else:
                pass
            return JsonResponse({"message": a.data})
        else:
            return JsonResponse({"message": "blog approval failed"})

# added


class GetApproveDoctorConsentView(generics.RetrieveUpdateAPIView):
    serializer_class = DoctorConsentSerializer
    permission_classes = [IsCuAdminPermission]
    lookup_field = 'DoctorId_id'
    queryset = DoctorConsent.objects.all()

    def get(self, request, *args, **kwargs):
        if self.kwargs['DoctorId_id'] == 'all':
            doctor_consent_form = DoctorConsent.objects.filter(Status__in=[
                '2', '3'])
        else:
            doctor_consent_form = DoctorConsent.objects.filter(
                DoctorId_id__exact=self.kwargs['DoctorId_id'], Status__in=['2', '3'])
        res = []
        # Get the page number from the request
        page_number = request.GET.get('page', 1)
        # Get the number of items per page from the request
        items_per_page = request.GET.get('per_page', 10)
        total_items = doctor_consent_form.count()
        paginator = Paginator(doctor_consent_form, items_per_page)
        if int(page_number) not in range(1, int(paginator.num_pages)+1):
            return HttpResponse("Not a valid page number", status=400)
        doctor_consent_form = paginator.page(page_number)
        for x in doctor_consent_form:
            if x.DateOfConsentForm is not None:
                x.DateOfConsentForm = timezone.localtime(
                    x.DateOfConsentForm, timezone.get_current_timezone())
            else:
                pass
            z = serialize_model(x, DoctorConsentSerializer)
            z['expert_role'] = get_cu_user_type(x.DoctorId_id)
            exp_data = DoctorDetails.objects.get(
                DoctorId_id__exact=x.DoctorId_id)
            if exp_data.ProfilePhoto is not None:
                z['expert_photo'] = get_s3_signed_url_bykey(
                    exp_data.ProfilePhoto)
            else:
                z['expert_photo'] = None
            if exp_data.Signature is not None:
                z['expert_sign'] = get_s3_signed_url_bykey(exp_data.Signature)
            else:
                z['expert_sign'] = None
            z['expert_name'] = exp_data.DoctorId.name
            res.append(z)
        return JsonResponse({'total_items': total_items,
                             'total_pages': paginator.num_pages,
                             'data': res})

    def put(self, request, *args, **kwargs):
        print("this is the doctor id ((((((((((((((()))))))))))))))",
              self.kwargs['DoctorId_id'])
        a = self.partial_update(request, *args, **kwargs)
        print(f"a-------------{a}-------{type(a)}")
        if a is not None and a.status_code == 200:
            if request.data['Status'] == 0:
                # add
                category = None
                if "ReasonCategory" in request.data:
                    category = request.data['ReasonCategory']
                else:
                    pass
                # ----------------------------------------------------------------
                aa = StatusReason.objects.create(Reason=request.data['Reason'], CurrentTime=timezone.now(
                ), ExpertId_id=self.kwargs['DoctorId_id'], ReasonCategory=category, ReasonType="Doctor_Consent_Rejection")
            else:
                pass
            noti_check = notification_check('Consent form approval!')
            e_check = email_check('Consent form approval!')
            if noti_check == True:
                r_p = AdminApprovePushNotification(
                    'Consent form approval!', self.kwargs['DoctorId_id'], request.data['Status'], request.GET['user_id'], "N")
                print(
                    f'-----------admin approval status push response-------{r_p}------------')
            else:
                pass
            if e_check == True:
                r_p = AdminApprovePushNotification(
                    'Consent form approval!', self.kwargs['DoctorId_id'], request.data['Status'], request.GET['user_id'], "E")
                print(
                    f'-----------admin approval status email response-------{r_p}------------')
            else:
                pass
            return JsonResponse({"message": a.data})
        else:
            return JsonResponse({"message": "Doctor consent approval failed"})


@method_decorator(csrf_exempt, name="dispatch")
class PushNotificTestView(View):
    def post(self, r):
        device = FCMDevice.objects.filter(
            user__groups__name__in=['doctor', 'reseracher', 'influencer', 'patient'])
        user_ids = set([x.user_id for x in device])
        print(f"deviceeeeeeeeeeee{user_ids}----------")
        title = "test notification "
        body = "This is test notification  for all users"
        link = os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP')
        a = device.send_message(Message(notification=Notification(title=title, body=body),
                                        webpush=WebpushConfig(fcm_options=WebpushFCMOptions(link=link))))
        print(f"push----{a}")
        print(f"push----{a[1]}")
        for x in user_ids:

            PushNotifications.objects.create(
                UserId=x, Title=title, Body=body, Link=link)

        return JsonResponse("notification sent", safe=False)


@method_decorator(csrf_exempt, name="dispatch")
class PushNotificTestView1(View):
    def post(self, r):
        device = FCMDevice.objects.filter(
            user__groups__name__in=['doctor', 'reseracher', 'influencer', 'patient'])

        user_ids = set([x.user_id for x in device])
        print(f"deviceeeeeeeeeeee{user_ids}----------")
        title = "test notification 2"
        body = "This is test notification 2 for all users"
        link = os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP')
        a = device.send_message(Message(notification=Notification(
            title=title, body=body), webpush=WebpushConfig(fcm_options=WebpushFCMOptions(link=link))))
        # a=device.send_message(Message(notification=Notification(title='title', body=notif_body)))

        print(f"push22----{a}")
        print(f"push22----{a[1]}")
        for x in user_ids:
            # fd=FCMDevice.objects.get(registration_id__exact=x)
            PushNotifications.objects.create(
                UserId=x, Title=title, Body=body, Link=link)

        return JsonResponse("notification sent", safe=False)


class UpdateIntroVideoStatus(generics.RetrieveUpdateAPIView):
    serializer_class = DoctorDetailsSerializer
    permission_classes = [IsCuAdminPermission]
    lookup_field = 'DoctorId'
    queryset = DoctorDetails.objects.all()

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.IntroVideoStatus == 1:
            if request.data['IntroVideoStatus'] == 3:
                category = None
                if "ReasonCategory" in request.data:
                    category = request.data["ReasonCategory"]
                aa = StatusReason.objects.create(Reason=request.data['IntroVideo_Reason'], CurrentTime=timezone.now(
                ), ExpertId_id=self.kwargs['DoctorId'], ReasonCategory=category, ReasonType="Intro_Video_Rejection")
                print(
                    f"--------------------intro_video_status_rejection---------------------{aa}--------------------------")
            elif request.data['IntroVideoStatus'] == 2 and len(instance.IntVideoUrl) > 1:
                print(
                    f"--------------------intro_video_status_2nd_video-------------------------")
                b = []
                b.append(instance.IntVideoUrl[1])
                instance.IntVideoUrl = b
                instance.save(update_fields=['IntVideoUrl'])
            else:
                pass
            return super().partial_update(request, *args, **kwargs)
        else:
            return HttpResponse("Only pending videos can be approved or rejected", status=500)

    def put(self, request, *args, **kwargs):
        print(f"video approval-----------")
        a = self.partial_update(request, *args, **kwargs)
        print(f"a-------------{a}-------{type(a)}")
        if a is not None and a.status_code == 200:
            # -------push notification-----------------
            noti_check = notification_check('Introduction video approval!')
            if noti_check == True:
                r_p = AdminApprovePushNotification(
                    'Introduction video approval!', self.kwargs['DoctorId'], request.data['IntroVideoStatus'], request.GET['user_id'], "N")
                print(
                    f'-----------admin approval status push response-------{r_p}------------')
            else:
                pass
            e_check = email_check('Introduction video approval!')
            if e_check == True:
                r_p = AdminApprovePushNotification(
                    'Introduction video approval!', self.kwargs['DoctorId'], request.data['IntroVideoStatus'], request.GET['user_id'], "E")
                print(
                    f'-----------admin approval status email response-------{r_p}------------')
            else:
                pass
            return JsonResponse({"message": a.data})
        else:
            return HttpResponse(a)

# added for admin approval page
    def get(self, request, *args, **kwargs):
        if self.kwargs['DoctorId'] == "all":
            intro_data = [serialize_model(t, DoctorDetailsSerializer)
                          for t in DoctorDetails.objects.filter(IntroVideoStatus__exact=1)]
            doctor_data = []
            total_items = 0
            # Get the page number from the request
            page_number = request.GET.get('page', 1)
            # Get the number of items per page from the request
            items_per_page = request.GET.get('per_page', 10)
            paginator = Paginator(intro_data, items_per_page)
            if int(page_number) not in range(1, int(paginator.num_pages)+1):
                return HttpResponse("Not a valid page number", status=400)
            intro_data = paginator.page(page_number)
            for x in intro_data:
                if x['IntVideoUrl'] is None:
                    pass
                else:
                    total_items += 1
                    x['expert_name'] = CuUser.objects.filter(
                        id__exact=x['DoctorId'])[0].name
                    x['expert_role'] = get_cu_user_type(x['DoctorId'])
                    a_i = []
                    b = x['IntVideoUrl']
                    for i in b:
                        new_obj_url = get_s3_signed_url_bykey(i)
                        a_i.append(new_obj_url)
                        print(f"ddd details video--------{new_obj_url}")
                    x['IntVideoUrl'] = a_i
                    if x['ProfilePhoto'] is not None:
                        new_obj_url = get_s3_signed_url_bykey(
                            x['ProfilePhoto'])
                        x['ProfilePhoto'] = new_obj_url
                    else:
                        pass
                    doctor_data.append(x)
            n_pages = int(total_items/items_per_page) + \
                1 if total_items % items_per_page != 0 else int(
                total_items/items_per_page)
            return JsonResponse({'total_items': total_items,
                                'total_pages': n_pages,
                                 'items': doctor_data})
        else:
            return Response("Not a valid request", status=404)

# added

# def patient_refund(AppointmentId_id,partial_status):
#         serializer_class = PatientPayments
#         app_id = AppointmentId_id
#         stripe.api_key = os.getenv("STRIPE_API_KEY")
#         patient_payment_details = PatientPayments.objects.filter(AppointmentId_id__exact=app_id)
#         if PatientPayments.objects.filter(AppointmentId_id__exact=app_id).exists():
#             patient_payment_details = PatientPayments.objects.filter(AppointmentId_id__exact=app_id)[0]

#             p_int=stripe.PaymentIntent.retrieve(patient_payment_details.PaymentIntent)
#             amount= int(p_int.amount)

#             if partial_status ==True:
#                 start_time = Appointments.objects.filter(id__exact=AppointmentId_id)[0].slot_id.schedule_start_time
#                 start_time = timezone.localtime(start_time,timezone.get_current_timezone())
#                 cancel_time = AppointmentMgmt.objects.filter(AppId_id__exact=AppointmentId_id,EventType__exact="Cancelled")[0].EventDate
#                 cancel_time = timezone.localtime(cancel_time,timezone.get_current_timezone())
#                 time_diff = start_time - cancel_time
#                 time_diff = time_diff.days * 24
#                 print(f'------this is the time difference {time_diff}')
#                 cancel_p=AdminContentManagement.objects.filter(Category__exact="Cancellation Refund Policy")
#                 great_t=1000
#                 less_t=0
#                 p_r=0
#                 r_id=0
#                 r_id_id=0
#                 for x in cancel_p:
#                     if x.Content['Time']==time_diff:
#                         p_r=x.Content['Refund']
#                         break
#                     elif x.Content['Time'] > time_diff:
#                         if x.Content['Time'] < great_t:
#                            great_t=x.Content['Time']
#                            r_id=x.id
#                     elif x.Content['Time'] > less_t:
#                         r_id_id=x.id
#                         less_t=x.Content['Time']

#                     else:
#                         pass


#                 if p_r==0:
#                    if great_t==1000:
#                         p_r=cancel_p.filter(id__exact=r_id_id)[0].Content['Refund']
#                         print(f"-----------patinet refund----------{p_r}--------------")
#                    else:
#                        p_r=cancel_p.filter(id__exact=r_id)[0].Content['Refund']
#                        print(f"-----------patinet refund----------{p_r}--------------")

#                 if RefundedPayments.objects.filter(PaymentIntent=patient_payment_details.PaymentIntent).exists():
#                     actual_amount=amount - RefundedPayments.objects.filter(PaymentIntent=patient_payment_details.PaymentIntent)[0].AmountRefunded
#                     amount = int(int(actual_amount) * float(p_r*0.01))
#                     r_p=stripe.Refund.create(payment_intent=p_int,amount=amount)
#                     print(f"in paymentsssssssss-------------{r_p}")
#                 else:
#                     amount = int(int(amount) * float(p_r*0.01))
#                     r_p=stripe.Refund.create(payment_intent=p_int,amount=amount)
#                     print(f"in paymentsssssssss-------------{r_p}")

#             else:
#                 r_p=stripe.Refund.create(payment_intent=p_int)
#                 print(f"in paymentsssssssss-------------{r_p}")
#         else:
#             r_p="No payment done yet"

#         return r_p

AIRWALLEX_API_BASE = "https://api.airwallex.com/v1"
AIRWALLEX_API_KEY = os.getenv("AIRWALLEX_API_KEY")


def retrieve_airwallex_payment_intent(payment_intent_id):
    """
    Retrieve Payment Intent details from Airwallex.
    """
    url = f"{AIRWALLEX_API_BASE}/payment_intents/{payment_intent_id}"
    headers = {
        "Authorization": f"Bearer {AIRWALLEX_API_KEY}",
        "Content-Type": "application/json",
    }
    response = requests.get(url, headers=headers)
    if response.status_code != 200:
        raise Exception(f"Error retrieving Payment Intent: {response.json()}")
    return response.json()


def create_airwallex_refund(payment_intent_id, amount=None):
    """
    Create a refund in Airwallex.
    """
    url = f"{AIRWALLEX_API_BASE}/refunds/create"
    headers = {
        "Authorization": f"Bearer {AIRWALLEX_API_KEY}",
        "Content-Type": "application/json",
    }
    payload = {
        "payment_intent_id": payment_intent_id,
        "amount": amount if amount else None,  # Refund full if no amount specified
    }
    response = requests.post(url, json=payload, headers=headers)
    if response.status_code != 200:
        raise Exception(f"Error creating Refund: {response.json()}")
    return response.json()


def patient_refund(AppointmentId_id, partial_status):
    """
    Process patient refund using Airwallex.
    """
    app_id = AppointmentId_id
    patient_payment_details = PatientPayment.objects.filter(
        AppointmentId_id__exact=app_id)

    if patient_payment_details.exists():
        patient_payment_details = patient_payment_details[0]
        payment_intent_id = patient_payment_details.PaymentIntent

        # Retrieve payment intent details
        payment_intent = retrieve_airwallex_payment_intent(payment_intent_id)
        # Airwallex uses the smallest currency unit
        amount = int(payment_intent["amount"])

        if partial_status:
            # Calculate time difference and determine refund percentage
            start_time = Appointments.objects.filter(id__exact=AppointmentId_id)[
                0].slot_id.schedule_start_time
            start_time = timezone.localtime(
                start_time, timezone.get_current_timezone())
            cancel_time = AppointmentMgmt.objects.filter(
                AppId_id__exact=AppointmentId_id, EventType__exact="Cancelled")[0].EventDate
            cancel_time = timezone.localtime(
                cancel_time, timezone.get_current_timezone())
            time_diff = (start_time - cancel_time).days * 24

            # Fetch refund policy
            cancel_policies = AdminContentManagement.objects.filter(
                Category__exact="Cancellation Refund Policy")
            refund_percentage = 0
            closest_policy = None

            for policy in cancel_policies:
                policy_time = policy.Content["Time"]
                if policy_time == time_diff:
                    refund_percentage = policy.Content["Refund"]
                    break
                elif policy_time > time_diff and (closest_policy is None or policy_time < closest_policy.Content["Time"]):
                    closest_policy = policy

            if refund_percentage == 0 and closest_policy:
                refund_percentage = closest_policy.Content["Refund"]

            # Calculate refund amount
            if RefundedPayments.objects.filter(PaymentIntent=payment_intent_id).exists():
                previous_refund = RefundedPayments.objects.filter(
                    PaymentIntent=payment_intent_id)[0]
                actual_amount = amount - previous_refund.AmountRefunded
                refund_amount = int(actual_amount * (refund_percentage / 100))
            else:
                refund_amount = int(amount * (refund_percentage / 100))

            # Create partial refund
            refund_response = create_airwallex_refund(
                payment_intent_id, refund_amount)
            print(f"Partial refund processed: {refund_response}")

        else:
            # Create full refund
            refund_response = create_airwallex_refund(payment_intent_id)
            print(f"Full refund processed: {refund_response}")

    else:
        refund_response = "No payment found for this appointment"

    return refund_response


class UpdateCancelAppointmentStatus(generics.UpdateAPIView):
    serializer_class = AppointmentsSerializer
    permission_classes = [IsCuAdminPermission]
    lookup_field = 'id'
    queryset = Appointments.objects.all()

    def put(self, request, *args, **kwargs):
        try:
            # Step 1: Retrieve and validate appointment
            appointment = self.get_object()
            new_status = request.data.get('status')

            # Step 1.1: If status is C_R, require rejection reason
            if new_status == "C_R":
                rejection_reason = request.data.get('cancellation_rejection_reason')
                if not rejection_reason:
                    return JsonResponse(
                        {"message": "Rejection reason is required when rejecting a cancellation."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                # Save the rejection reason in AppointmentMgmt
                appointment_mgmt, _ = AppointmentMgmt.objects.get_or_create(AppId=appointment)
                appointment_mgmt.RejectionReason = rejection_reason
                appointment_mgmt.EventDate = timezone.now()
                appointment_mgmt.AppId = appointment
                appointment_mgmt.save()

                self._send_rejection_notifications_and_emails(appointment,rejection_reason)

            # Step 2: Validate status transition
            if not self._is_valid_status_change(appointment, new_status):
                return JsonResponse(
                    {"message": "Invalid status transition"},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Step 3: Update appointment status
            response = self.partial_update(request, *args, **kwargs)

            # Step 4: Process refund if approved
            if new_status == "C" and response.status_code == status.HTTP_200_OK:
                refund_result = self._process_refund_and_notifications(
                    appointment)

                if not refund_result.get('success'):
                    return JsonResponse(
                        {"message": f"Status updated but refund failed: {refund_result.get('message')}"},
                        status=status.HTTP_207_MULTI_STATUS
                    )

            return response

        except Exception as e:
            logger.error(f"Error in cancellation approval: {str(e)}")
            return JsonResponse(
                {"message": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _send_rejection_notifications_and_emails(self, appointment, rejection_reason):
        patient = appointment.patient
        patient_url = os.getenv(
            'NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments'
        if notification_check('Appointment cancellation'):
            send_generic_push_notification(
                user_id=patient.id,
                title="Cancellation Request Rejected",
                body=f"Hi {patient.name}, your cancellation request was rejected by the admin. Reason: {rejection_reason}",
                link=patient_url
            )

        if email_check('Appointment cancellation'):
            email_service.cancellation_rejected_email_patient(
                patient.email,
                patient.name,
                rejection_reason,
                patient_url
            )
        return "Rejection notifications and emails sent."

    def _is_valid_status_change(self, appointment, new_status):
        """Validate the cancellation status transition"""
        valid_statuses = ["C", "C_R"]
        return (
            new_status in valid_statuses and
            appointment.status == "C_P"  # Must be pending cancellation
        )

    def _process_refund_and_notifications(self, appointment):
        try:
            appointment_details = AppointmentMgmt.objects.get(
                AppId_id=appointment.id)
            cancellation_reason = appointment_details.AppointmentReason
            # 1. Process refund
            refund_result = self._process_refund(
                appointment.id, cancellation_reason)

            # 2. Send notifications
            # appointment_id, patient_id, doctor_id ,slot_id
            self._send_notifications_and_emails(
                appointment,
                cancellation_reason
                # refund_amount=refund_result.get('refunded_amount'),
                # refund_status=refund_result.get('status')
            )

            return {
                "success": True,
                "message": "Refund processed successfully",
                "details": refund_result
            }

        except Exception as e:
            logger.error(f"Refund processing failed: {str(e)}")
            return {
                "success": False,
                "message": str(e)
            }

    def _process_refund(self, appointment_id, cancellation_reason):
        try:
            # 1. Get appointment and cancellation records
            appointment = Appointments.objects.select_related(
                'slot_id').get(id=appointment_id)
            cancellation = AppointmentMgmt.objects.get(
                AppId_id=appointment_id, EventType="Cancelled")

            # 2. Get payment information
            payment = PatientPayment.objects.get(AppointmentId=appointment)
            airwallex = AirwallexService()
            payment_intent = airwallex.get_payment_intent(
                payment.PaymentIntent)

            # 3. Calculate refund percentage
            refund_percent = calculate_refund_percentage(
                cancellation.EventDate,
                appointment.slot_id.schedule_start_time
            )
            original_amount = Decimal(payment_intent['amount'])
            refund_amount = (original_amount *
                             refund_percent).quantize(Decimal('0.00'))

            # 4. Process refund only if amount > 0
            refund_status = "no_refund_due"
            refund_response = None
            if refund_amount > 0:
                refund_response = airwallex.create_refund(
                    payment_intent_id=payment.PaymentIntent,
                    amount=float(refund_amount),
                    currency=payment_intent['currency'],
                    reason=cancellation_reason
                    # reason=f"Refund ({refund_percent*100}%) per cancellation policy"
                )
                refund_status = refund_response['status']
                refund_id = refund_response['id']

            # 5. Record refund transaction
            if refund_amount > 0:
                local_tz = timezone.get_current_timezone()
                hours_before = ((appointment.slot_id.schedule_start_time -
                                cancellation.EventDate).total_seconds() / 3600)
                RefundedPayments.objects.create(
                    PaymentIntent=payment.PaymentIntent,
                    RefundId=refund_id,
                    AmountRefunded=refund_amount,
                    RefundStatus=refund_status,
                    RefundCurrency=payment_intent['currency'],
                    AppId=appointment,
                    RefundDate=timezone.localtime(timezone.now(), local_tz),
                    RefundObject={
                        "cancellation_reason": cancellation_reason,
                        "original_amount": float(original_amount),
                        "refund_percentage": float(refund_percent),
                        "hours_before": hours_before,
                        "policy_applied": float(refund_percent * 100),
                        "refund_timestamp": timezone.localtime(timezone.now(), local_tz).isoformat(),
                        "refund_response": refund_response
                    }
                )

            return {
                "success": True,
                "refunded_amount": float(refund_amount),
                "currency": payment_intent['currency'],
                "status": refund_status,
                "refund_percentage": float(refund_percent * 100)
            }

        except Exception as e:
            return {
                "success": False,
                "message": str(e)
            }

    def _send_notifications_and_emails(self, appointment, cancellation_reason, rejected=False):
        # Get patient and doctor details
        patient = appointment.patient
        slot_id = appointment.slot_id_id
        slot = appointment.slot_id
        doctor = slot.doctor

        doctor_url = os.getenv(
            'NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment'
        patient_url = os.getenv(
            'NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments'
        admin_url = os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar'

        if notification_check('Appointment cancellation'):
            send_generic_push_notification(
                user_id=patient.id,
                title="Appointment cancellation!",
                body=f"Hi {patient.name}, Your appointment cancellation request has been approved by admin.",
                link=patient_url
            )
            send_generic_push_notification(
                user_id=doctor.id,
                title="Appointment cancellation!",
                body=f"Hi {doctor.name}, Your upcoming appointment with {patient.name} is cancelled by patient.",
                link=doctor_url
            )
            send_generic_push_notification(
                user_id=None,
                title="Appointment cancellation!",
                body=f"You have approved the Appointment cancellation with id -{appointment.id}",
                link=admin_url,
                send_to_admins=True
            )

        details = get_appointment_details(slot_id=slot_id, doctor_id=doctor.id)

        appointment_timings = details["appointment_timings"]
        expert_role = details["expert_role"]

        if email_check('Appointment cancellation'):
            email_service.cancellation_email_patient(
                patient.email, patient.name, patient_url, patient.name, appointment_timings)
            email_service.cancellation_email_expert(
                doctor.email, doctor.name, doctor_url, patient.name, appointment_timings, expert_role)

        return "Notifications and emails sent successfully."


# previous code of appointment cancellation update


class ListUpdateNotificationStatus(generics.RetrieveUpdateAPIView):
    serializer_class = NotificationTypeSerializer
    permission_classes = [IsCuAdminPermission]
    lookup_field = 'id'
    queryset = NotificationType.objects.all()

    def get(self, request, *args, **kwargs):
        noti_id = self.kwargs['id']
        if noti_id == "N" or noti_id == "E":
            res = []
            res_data = NotificationType.objects.filter(
                CategoryType__exact=noti_id)
            for y in res_data:
                ress = serialize_model(y, NotificationTypeSerializer)
                res.append(ress)
            print(f"---------------aa{res}")
        elif noti_id == "all":
            res = []
            res_data = NotificationType.objects.all()
            kk = ["Appointment schedule"]
            for y in res_data:
                if kk is not None and y.NotificationName in kk:
                    pass
                else:
                    kk.append(y.NotificationName)
                    zz = NotificationType.objects.filter(
                        NotificationName__exact=y.NotificationName)
                    ttt = []
                    for x in zz:
                        ress = serialize_model(x, NotificationTypeSerializer)
                        ttt.append(ress)
                    res.append({y.NotificationName: ttt})
            print(f"---------------aa{res}")
        else:
            res_data = NotificationType.objects.filter(id__exact=noti_id)[0]
            ress = serialize_model(res_data, NotificationTypeSerializer)
            res = json.loads(json.dumps(ress))
            print(res_data)
        return JsonResponse(res, safe=False)

    def put(self, request, *args, **kwargs):
        print(f"notification status-----------")
        a = self.partial_update(request, *args, **kwargs)
        print(f"a-------------{a}-------{type(a)}")
        if a is not None and a.status_code == 200:
            return JsonResponse({"message": a.data})
        else:
            return JsonResponse({"message": "status change failed"})
# added


def AddAdminUser(a, password):
    print("data inside add admin user", a)
    try:
        a.update({"password": password})
        print(f"tfwqeyuw----------{a}--{type(a)}")
        serializer = CuUserRegisterSerializer(data=a)
        # model field validation automatically applied
        serializer.is_valid(raise_exception=True)
        b = serializer.save()
        return b

    except Exception as e:
        print("add adminnnn user exceptionsss", e)

        return str(e)


def AddAdminUserRole(a, role, perms):
    try:
        g1 = Group.objects.get_or_create(name=role)
        ct = ContentType.objects.get_for_model(get_user_model())
        for x in perms:
            if x[2] == 1:

                p1 = Permission.objects.get(codename=x[0], name=x[1],
                                            content_type=ct)
                a.user_permissions.add(p1)

        if len(a.groups.all()) > 1:
            return False
        else:
            a.groups.add(g1[0])
        return a
    except Exception as e:
        print("add role exceptionsss", e)
        try:
            us = get_user_model().objects.get(email=a.email).delete()
            print(f"user deleted role...{us}---{get_user_model()}")
        except Exception as f:
            print("delete user role exceptionsss", f)
        return str(e)

# added


def AddAdminDetails(a):
    d1 = AdminDetails(AdminId=a)
    d1.save()
    print(f"admin__details------------{d1}")
    return d1

# added


def cu_sendpwmail(r, u_email, code, u_name, u_password, host, user_role=None):

    verify_url1 = host+"/pw-reset/?verify_code="+code+"&email="+u_email
    payload = {
        "template_key": "2518b.41c485fda42f6e5f.k1.5b188870-0f66-11ef-aca0-525400ab18e6.18f66827277",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": u_email,
                    "name": u_email,
                }
            }

        ],
        "merge_info": {
            "u_email": u_email,
            "u_name": u_name,
            "u_link": host+"/auth/login",
            "user_role": user_role,
            "verify_url1": verify_url1,
            "u_password": u_password,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }

    headers = {
        'Authorization': f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
    try:
        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload))
        print(
            f"pw mail send-----------{response.status_code}-------{response.content}--------")
        response_status = True if response.status_code == 200 else True if response.status_code in [
            201, 202] else False
        return response_status
    except HTTPError as e:
        print(f"exception-----------{e.to_dict}")
        return response_status


def AddDoctorDetails(a, b, from_admin=False):

    try:
        d1 = DoctorDetails(DoctorId=a, NDAConsent=b, EmailVerified=from_admin)
        d1.save()
        print(f"Saved doctor details: {d1}")
        return d1
    except Exception as e:
        print(f"❌ Error saving DoctorDetails: {e}")
        raise


class AddChildAdmin(views.APIView):
    serializer_class = CuUserSerializer
    permission_classes = [IsCuAdminPermission]

    def post(self, request, *args, **kwargs):
        try:
            u_data = json.loads(request.body.decode("utf-8"))
            user_role = u_data.get('user_role')
            assert user_role in ["child_admin", "doctor",
                                 "researcher", "influencer"], "Invalid user role"

            # Check if email or phone already exists before attempting creation
            email = u_data.get('email')
            phone = u_data.get('phone')

            if email and CuUser.objects.filter(email=email).exists():
                return Response(
                    {"message": "This email address is already registered."},
                    status=400
                )

            if phone and CuUser.objects.filter(phone=phone).exists():
                return Response(
                    {"message": "This phone number is already registered."},
                    status=400
                )

            user = request.data
            password = ''.join(random.choice('0123456789ABCDEF')
                               for _ in range(8))
            res = AddAdminUser(user, password)

            if not isinstance(res, get_user_model()):
                return Response(res, status=400)

            # Assign permissions and role
            role_assignment = AddAdminUserRole(
                res, user_role, u_data.get('perms', []))
            if not isinstance(role_assignment, get_user_model()):
                return Response(role_assignment, status=400)

            # Add details based on role
            if user_role == "child_admin":
                AddAdminDetails(res)
            elif user_role in ["doctor", "researcher", "influencer"]:
                NDAConsent = int(u_data.get("NDAConsent", 0))
                AddDoctorDetails(res, NDAConsent, from_admin=True)

            if "Designation" in request.data and user_role == "child_admin":
                try:
                    a_in = AdminDetails.objects.get(
                        AdminId__email__exact=u_data['email'])
                    a_in.Designation = u_data['Designation']
                    a_in.save()
                except Exception as e:
                    print(f"Error setting designation: {e}")

            # Filter data for response
            filtered_data = filter_user_data(res)
            response = Response(filtered_data, status=status.HTTP_201_CREATED)

            # Send password mail
            pw_code = ''.join(random.choice('0123456789ABCDEF')
                              for _ in range(16))
            instance = CuUser.objects.get(email__exact=u_data['email'])
            app_url_user = os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP")
            app_url_admin = os.getenv("NEXT_CANCER_UNWIRED_ADMIN_APP")
            app_url = app_url_user if user_role in [
                "doctor", "researcher", "influencer"] else app_url_admin
            mail_res = cu_sendpwmail(
                request, instance.email, pw_code, instance.name, password, app_url, user_role)

            if mail_res:
                instance.PWVerifyCode = pw_code
                instance.PWCodeGentime = timezone.now()
                instance.save()

            return response

        except AssertionError as ae:
            return Response({"message": str(ae)}, status=400)
        except Exception as e:
            print(f"Unexpected error in AddUserByAdmin: {e}")
            return Response({"message": "Something went wrong"}, status=500)

    def get(self, request, *args, **kwargs):
        u_data = get_user_model().objects.filter(groups__name__exact="child_admin")
        print(u_data.count())
        res = []
        for x in request.GET:

            if x == 'start_date' and request.GET['start_date'] != '':
                start_date = timezone.make_aware(parse_datetime(request.GET['start_date']),
                                                 timezone.get_current_timezone())
                u_data = u_data.filter(DateOfRegistration__gte=start_date)
            elif x == 'end_date' and request.GET['end_date'] != '':
                end_date = timezone.make_aware(parse_datetime(
                    request.GET['end_date']), timezone.get_current_timezone())
                u_data = u_data.filter(DateOfRegistration__lte=end_date)
            elif x == 'name' and request.GET['name'] != '':
                u_data = u_data.filter(name__icontains=request.GET['name'])
            elif x == 'designation' and request.GET['designation'] != '':
                u_data = u_data.filter(
                    admindetails__Designation__icontains=request.GET['designation'])
                print(u_data.count())
            else:
                print("Invalid filter")
        # Get the page number from the request
        page_number = request.GET.get('page', 1)
        # Get the number of items per page from the request
        items_per_page = request.GET.get('per_page', 10)
        total_items = u_data.count()
        paginator = Paginator(u_data, items_per_page)
        if int(page_number) not in range(1, int(paginator.num_pages)+1):
            return Response({"error": "Not a valid page number"}, status=status.HTTP_400_BAD_REQUEST)
        u_data = paginator.page(page_number)
        for a in u_data:
            b = AdminDetails.objects.filter(AdminId__exact=a.id)
            if b.exists():
                b = AdminDetails.objects.get(AdminId__exact=a.id)
                print(f'------------bbbbb{a.id}-----------{b}')
                b = serialize_model(b, AdminDetailsSerializer)
            else:
                b = []
            res.append({"admin_details": serialize_model(
                a, CuUserFilterSerializer), "admin_other_details": b})

        return JsonResponse({'total_items': total_items,
                             'total_pages': paginator.num_pages,
                             "child_admin_data": res,
                             "total_child_admin": total_items})


# added
class AppointmentsFilter(views.APIView):
    permission_classes = [IsCuAdminPermission]

    def get(self, r):
        print(f"requestssssssssss{r.GET}")
        exp_data = []
        f = Appointments.objects.all()

        for x in r.GET:
            if x == "app_id" and r.GET['app_id'] != '':

                f = f.filter(id__exact=r.GET['app_id'])
                print(f'------appointment_id_filter_data----{f}')

            elif x == "app_status" and r.GET['app_status'] != '':
                if r.GET['app_status'] == "Ongoing":
                    meeting_d = MeetingSession.objects.filter(
                        MeetingStatus__exact=1, AppointmentId__status__in=['B', 'R', 'P'])
                    appoint_ids = meeting_d.values("AppointmentId")
                    f = f.filter(id__in=appoint_ids)
                # added
                elif r.GET['app_status'] == "Unattended":
                    meeting_d = MeetingSession.objects.filter(
                        MeetingStatus__exact=3, AppointmentId__status__in=['B', 'R', 'P'])
                    appoint_ids = meeting_d.values("AppointmentId")
                    f = f.filter(id__in=appoint_ids)
                # ----------------------------------------------------------------
                elif r.GET['app_status'] == "Expired":
                    meeting_d = MeetingSession.objects.filter(
                        MeetingStatus__exact=0, AppointmentId__status__in=['B', 'R', 'P'])
                    appoint_ids = meeting_d.values("AppointmentId")
                    f = f.filter(id__in=appoint_ids,
                                 slot_id__schedule_end_time__lte=timezone.now())

                elif r.GET['app_status'] == "Upcoming":
                    f = f.filter(
                        status__in=["B", "R"], slot_id__schedule_start_time__gte=timezone.now())

                elif r.GET['app_status'] == "Completed":
                    meeting_d = MeetingSession.objects.filter(
                        MeetingStatus__exact=2, AppointmentId__status__in=['B', 'R', 'P'])
                    appoint_ids = meeting_d.values("AppointmentId")
                    f = f.filter(id__in=appoint_ids)

                elif r.GET['app_status'] == "Rescheduled":
                    f = f.filter(
                        status__exact="R", slot_id__schedule_start_time__gte=timezone.now())
                elif r.GET['app_status'] == "Cancelled":
                    f = f.filter(status__exact="C")
                elif r.GET['app_status'] == "Cancellation Rejected":
                    f = f.filter(status__exact="C_R")
                elif r.GET['app_status'] == "Cancellation Pending":
                    f = f.filter(status__exact="C_P")
                else:
                    print("-----------not a valid appointment status----------")
                print(f'-----appointment_status_filter{f}')

            elif x == "start_date" and r.GET['start_date'] != '':
                start_date = timezone.make_aware(parse_datetime(r.GET['start_date']),
                                                 timezone.get_current_timezone())
                f = f.filter(slot_id__schedule_start_time__gte=start_date)
                print(f'-----------app_start_date_filter-----{f}')

            elif x == "end_date" and r.GET['end_date'] != '':
                end_date = timezone.make_aware(parse_datetime(r.GET['end_date']),
                                               timezone.get_current_timezone())
                f = f.filter(slot_id__schedule_end_time__lte=end_date)
                print(f'-----------app_end_date_filter-----{f}')

            elif x == "id" and r.GET['id'] != "":
                f = f.filter(Q(patient_id__exact=r.GET['id']) | Q(
                    slot_id__doctor_id__exact=r.GET['id']))
                print(f'-----------user id-----{f}')

            elif x == "name" and r.GET['name'] != '':
                f = f.filter(Q(slot_id__doctor__name__icontains=r.GET['name']) | Q(
                    patient__name__icontains=r.GET['name']))
                print(f'------app_name_filter-----{f}')

            elif x == "role" and r.GET['role'] != '':
                if r.GET['role'] == "patient":
                    f = f.filter(patient_id__groups__name__exact=r.GET['role'])
                    print(f'-----appointment_role_filter-----{f}')
                elif r.GET['role'] in ['doctor', 'researcher', 'influencer']:
                    f = f.filter(
                        slot_id__doctor__groups__name__exact=r.GET['role'])
                    print(f'-----appointment_role_filter----{f}')

            else:

                print({"message": "invalid filter"})
        f = f.order_by('-id')
        # Get the page number from the request
        page_number = r.GET.get('page', 1)
        # Get the number of items per page from the request
        items_per_page = r.GET.get('per_page', 10)
        total_items = f.count()
        paginator = Paginator(f, items_per_page)
        print(paginator)
        if int(page_number) not in range(1, int(paginator.num_pages)+1):
            return HttpResponse("Not a valid page number", status=400)
        f = paginator.page(page_number)
        app_data = []
        for x in f:
            l1 = dict()
            meeting_session_details = Appointments.objects.filter(
                id__exact=x.id)[0].meetingsession_set.all()
            consent_details = Appointments.objects.filter(
                id__exact=x.id)[0].appointmentconsent_set.all()
            doctor_id = SlotSerializer(x.slot_id).data
            sch = SchedulerSlots.objects.filter(id__exact=x.slot_id_id)[0]
            if x.status in ['B', 'R']:
                if timezone.localtime(timezone.now(), timezone.get_current_timezone()).timestamp() >= timezone.localtime(sch.schedule_end_time, timezone.get_current_timezone()).timestamp():
                    if meeting_session_details[0].MeetingStatus == 0:
                        x.status = "Expired"
                    elif meeting_session_details[0].MeetingStatus == 2:
                        x.status = "Completed"
                    elif meeting_session_details[0].MeetingStatus == 1:
                        x.status = "Ongoing"
                    # added
                    elif meeting_session_details[0].MeetingStatus == 3:
                        x.status = "Unattended"
                    # ----------------------------------------------------------------
                    else:
                        pass
                else:
                    if x.status == "R":
                        x.status = 'Rescheduled'
                    # added
                    elif meeting_session_details[0].MeetingStatus == 3:
                        current_status = "Unattended"
                    elif meeting_session_details[0].MeetingStatus == 2:
                        current_status = "Completed"
                    # --------------------------------------------------------------
                    else:
                        x.status = "Upcoming"
            else:
                if x.status == "C":
                    x.status = "Cancelled"
                elif x.status == "C_R":
                    x.status = "Cancellation Rejected"
                elif x.status == "C_P":
                    x.status = "Cancellation Pending"
                else:
                    pass
            l1.update({"app_data": serialize_model(x, AppointmentsSerializer)})
            c_reason = AppointmentMgmt.objects.filter(
                AppId_id__exact=x.id, EventType__exact="Cancelled")
            if c_reason.exists():
                c_reason = c_reason[0].AppointmentReason
                l1.update({"appointment_cancel_reason": c_reason})
            l1.update({'slot_details': doctor_id})
            l1.update(
                {"patient_details": CuUserFilterSerializer(x.patient).data})
            doc = CuUser.objects.get(id__exact=doctor_id['doctor'])
            l1.update({"doctor_details": serialize_model(CuUser.objects.get(
                id__exact=doctor_id['doctor']), CuUserFilterSerializer)})
            d_photo = DoctorDetails.objects.filter(
                DoctorId__exact=doctor_id['doctor'])[0].Signature
            if d_photo is not None:
                new_obj_url = get_s3_signed_url_bykey(d_photo)
                d_photo = new_obj_url
            l1.update({"doctor_signature": d_photo})
            expert_role = get_cu_user_type(doctor_id['doctor'])
            l1.update({"expert_role": expert_role})
            p_photo = PatientDetails.objects.filter(
                PatientId__exact=x.patient_id)[0]
            if p_photo.Signature is not None:
                new_obj_url = get_s3_signed_url_bykey(p_photo.Signature)
                p_sign = new_obj_url
                l1.update({"patient_signature": p_sign})
            else:
                l1.update({"patient_signature": None})
            if p_photo.ProfilePhoto is not None:
                new_obj_url = get_s3_signed_url_bykey(p_photo.ProfilePhoto)
                p_photo = new_obj_url
                l1.update({"patient_photo": p_photo})
            else:
                l1.update({"patient_photo": None})
            l1.update({'meeting_session_details': serialize_model(
                t, MeetingSessionSerializer) for t in meeting_session_details})

            l1.update({'consent_details': serialize_model(
                t, AppointmentConsentSerializer) for t in consent_details})
            pres = ''
            if get_cu_user_type(doctor_id['doctor']) in ['researcher', 'influencer']:
                pres = Appointments.objects.filter(id__exact=x.id)[
                    0].irprescription_set.all()
            elif get_cu_user_type(doctor_id['doctor']) in ['doctor']:
                pres = Appointments.objects.filter(id__exact=x.id)[
                    0].prescription_set.all()
            else:
                pass
            pres_ids = []
            for z in pres:
                pres_ids.append(z.id)
            l1.update({'prescriptions': pres_ids})

            app_data.append(l1)
        response_data = {
            'total_items': total_items,
            'total_pages': paginator.num_pages,
            'items': app_data
        }
        return JsonResponse(response_data)


# added

def CuSendOTP(phone, message, c_code):
    sns_client = boto3.client(
        "sns",
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        region_name=settings.AWS_REGION
    )

    sns_res = sns_client.publish(
        # PhoneNumber="+91"+phone,
        PhoneNumber=c_code + phone,
        Message=message,

    )

    print("resss----", sns_res)
    return sns_res


class VerifyMobileAPIView(views.APIView):
    permission_classes = [IsCuAdminPermission]

    def post(self, request, *args, **kwargs):
        try:
            u_data = json.loads(request.body.decode("utf-8"))
            phone = u_data.get("phone")
            c_code = u_data.get("country_code")
            email = u_data.get("email") or request.data.get("email")

            if not phone or not c_code or not email:
                return JsonResponse({"message": "Missing required fields"}, status=400)

            # OTP rate-limiting check
            recent_otp_exists = CuUserPhoneOTP.objects.filter(
                UserEmail=u_data["email"],
                PhoneOTPVerified=False,
                PhoneOTPGenTime__gte=timezone.now() - timedelta(seconds=60)
            ).exists()
            if recent_otp_exists:
                return JsonResponse({"message": "Please wait at least 1 minute before requesting another OTP."}, status=429)

            otp_code = "".join(random.choice("0123456789") for _ in range(6))

            obj1, _ = CuUserPhoneOTP.objects.update_or_create(
                UserEmail=email,
                PhoneOTPVerified=False,
                OTPExpired=False,
                defaults={
                    "PhoneOTP": otp_code,
                    "PhoneOTPGenTime": timezone.now(),
                    "TempPhone": phone,
                    "TempCountryCode": c_code,
                    "TempUserData": json.dumps(u_data),
                }
            )

            otp_body = f"Use {otp_code} to verify your Health Unwired account. This code is valid for 2 mins."
            otp_res = CuSendOTP(phone, otp_body, c_code)

            if otp_res.get("MessageId"):
                return JsonResponse({
                    "message": "OTP sent successfully.",
                    "otp": otp_code
                })
            else:
                return JsonResponse({"message": "Failed to send OTP"}, status=500)

        except KeyError as e:
            print("Exception in VerifyMobileAPIView:", str(e))
            return JsonResponse({"message": f"Missing key: {str(e)}"}, status=400)

        except Exception as e:
            return JsonResponse({"message": "An unexpected error occurred."}, status=500)


class PublishPodcastView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = PodcastSerializer
    queryset = Podcast.objects.all()
    lookup_field = 'id'
    permission_classes = [IsCuAdminPermission]

    def partial_update(self, r, *args, **kwargs):
        instance = self.get_object()
        instance.PodcastStatus = 2
        instance.save()
    # -----------added the thumbnail image--------------------------------
        if "ThumbnailImage" in r.FILES:
            b1 = handle_uploaded_file(r.FILES["ThumbnailImage"])
            f_name = b1.split('/')[2]
            file_url = settings.PROJECT_ROOT + b1

            file_urls_res = handle_s3_uploaded_file(f_name, file_url)
            print(f"urlsssssssssss{type(file_urls_res)}")
            r.data['ThumbnailImage'] = file_urls_res
        else:
            pass
    # ---------------------------------------------------------------------
        a = super().partial_update(r, *args, **kwargs)
        return a

    def put(self, r, *args, **kwargs):
        try:
            a = self.partial_update(r, *args, **kwargs)
            print(f'outside the try notification')
            try:
                print(f'Inside the try notification')
                if notification_check('Podcast approval!'):
                    expert_id = Podcast.objects.get(
                        id=self.kwargs['id']).ExpertId_id
                    r_p = AdminApprovePushNotification(
                        'Podcast approval!', expert_id, '', r.GET.get(
                            'user_id', ''), "N"
                    )
                    print(f'Admin approval status push response: {r_p}')
            except Podcast.DoesNotExist:
                print("Error: Podcast with the given ID does not exist.")
            except Exception as e:
                print(f"Error while sending push notification: {e}")

            # Check if email should be sent
            try:
                if email_check('Podcast approval!'):
                    expert_id = Podcast.objects.get(
                        id=self.kwargs['id']).ExpertId_id
                    r_p = AdminApprovePushNotification(
                        'Podcast approval!', expert_id, '', r.GET.get(
                            'user_id', ''), "E"
                    )
                    print(f'Admin approval status email response: {r_p}')
            except Podcast.DoesNotExist:
                print("Error: Podcast with the given ID does not exist.")
            except Exception as e:
                print(f"Error while sending email notification: {e}")

            return a  # Return response from partial update

        except Exception as e:
            print(f"Unexpected error in put method: {e}")
            return Response({"error": "An error occurred while processing the request."}, status=500)

    # def put(self, r, *args, **kwargs):
    #     a = self.partial_update(r, *args, **kwargs)
    #     noti_check = notification_check('Podcast approval!')
    #     if noti_check == True:
    #         expert_id = Podcast.objects.get(
    #             id__exact=self.kwargs['id']).ExpertId_id
    #         r_p = AdminApprovePushNotification(
    #             'Podcast approval!', expert_id, '', r.GET['user_id'], "N")
    #         print(
    #             f'-----------admin approval status push response-------{r_p}------------')
    #     else:
    #         pass
    #     e_check = email_check('Podcast approval!')
    #     if e_check == True:
    #         expert_id = Podcast.objects.get(
    #             id__exact=self.kwargs['id']).ExpertId_id
    #         r_p = AdminApprovePushNotification(
    #             'Podcast approval!', expert_id, '', r.GET['user_id'], "E")
    #         print(
    #             f'-----------admin approval status email response-------{r_p}------------')
    #     else:
    #         pass
    #     return a

    def delete(self, r, *args, **kwargs):
        p_id = Podcast.objects.filter(id__exact=self.kwargs['id'])
        if p_id.exists():
            p_id = Podcast.objects.get(id__exact=self.kwargs['id'])
            a = ContentRemoval(p_id, "podcast")
            return Response(a)
        else:
            return Response("Item doesn't exist", status=404)


class HomepageView(views.APIView):
    permission_classes = [IsCuAdminPermission]

    def get(self, r, *args, **kwargs):
        appointment_data = Appointments.objects.all()
        user_data = CuUser.objects.all()
        user_information = dict()
        appointment_info = dict()
        registrations_info = dict()
        # -------------------------user_information_section-------------------------
        total_active_users = user_data.filter(
            approval__exact="approved").count()
        total_active_patients = user_data.filter(
            approval__exact="approved", groups__name__exact="patient").count()
        total_active_experts = user_data.filter(approval__exact="approved", groups__name__in=[
            'doctor', 'researcher', 'influencer']).count()
        total_active_admin = user_data.filter(
            approval__exact="approved", groups__name__in=['admin', 'child_admin']).count()
        user_information.update({"total_active_users": total_active_users,
                                 "total_active_patients": total_active_patients,
                                 "total_active_experts": total_active_experts,
                                 "total_active_admin": total_active_admin})
        # -------------------------appointments_information_section-------------------------
        meeting_r = MeetingSession.objects.filter(AppointmentId__slot_id_id__schedule_start_time__lte=timezone.now(
        ), AppointmentId__status__in=['B', 'P', 'R'], MeetingStatus__exact=2)
        appointment_r = meeting_r.values("AppointmentId")
        r_app = appointment_data.filter(id__in=appointment_r).order_by(
            '-slot_id__schedule_start_time')
        r_app = r_app[:10]
        recent_appointments = []
        prescription_data = []
        for x in r_app:
            l1 = dict()
            pres_dict = dict()
            p_profile = PatientDetails.objects.filter(
                PatientId__exact=x.patient.id)[0].ProfilePhoto
            d_profile = DoctorDetails.objects.filter(
                DoctorId__exact=x.slot_id.doctor_id)[0].ProfilePhoto
            p_dataa = CuUser.objects.filter(id=x.patient_id)[0]
            app_date = x.slot_id.schedule_start_time
            meeting_info = MeetingSession.objects.filter(
                AppointmentId__exact=x.id)[0]
            meeting_start_time = meeting_info.SessionStartTime
            meeting_end_time = meeting_info.SessionEndTime
            if p_profile is not None:
                new_obj_url = get_s3_signed_url_bykey(p_profile)
                p_profile = new_obj_url

            if d_profile is not None:
                new_obj_url = get_s3_signed_url_bykey(d_profile)
                d_profile = new_obj_url
            expert_role = get_cu_user_type(x.slot_id.doctor_id)
            l1.update({"patient_data": {"id": x.patient_id, "name": x.patient.name, "email": x.patient.email, "approval": x.patient.approval, "ProfilePhoto": p_profile},
                       "doctor_data": {"id": x.slot_id.doctor_id, "name": x.slot_id.doctor.name, "email": x.slot_id.doctor.email, "approval": x.slot_id.doctor.approval, "expert_role": expert_role, "ProfilePhoto": d_profile},
                       "appointmenent_date": app_date,
                       "meeting_start_time": meeting_start_time,
                       "meeting_end_time": meeting_end_time})
            pres_details = []
            if expert_role == "doctor" and Prescription.objects.filter(AppointmentId_id__exact=x.id).exists():
                pres_details = serialize_model(Prescription.objects.filter(
                    AppointmentId_id__exact=x.id), PrescriptionSerializer)
            elif IRPrescription.objects.filter(AppointmentId_id__exact=x.id).exists():
                pres_details = serialize_model(IRPrescription.objects.filter(
                    AppointmentId__exact=x.id), IRPrescriptionSerializer)
            else:
                pres_details = None
            pres_dict.update({"prescription_details": pres_details,
                              "patient_data": {"id": x.patient_id, "name": x.patient.name, "email": x.patient.email, "approval": x.patient.approval, "ProfilePhoto": p_profile},
                              "doctor_data": {"id": x.slot_id.doctor_id, "name": x.slot_id.doctor.name, "email": x.slot_id.doctor.email, "approval": x.slot_id.doctor.approval, "expert_role": expert_role, "ProfilePhoto": d_profile},
                              "appointmenent_date": app_date,
                              "meeting_start_time": meeting_start_time,
                              "meeting_end_time": meeting_end_time})
            recent_appointments.append(l1)
            prescription_data.append(pres_dict)

        # -------------------recent_appointment_data----------------------
        recent_apps = dict()
        recent_apps.update({"recent_appointments": recent_appointments})

        # ---------------------registration_section_data---------------------
        new_patients = user_data.filter(DateOfRegistration__gte=(
            timezone.now()-timedelta(days=30)), groups__name__exact="patients").count()
        new_experts = user_data.filter(DateOfRegistration__gte=(timezone.now(
        )-timedelta(days=30)), groups__name__in=['doctor', 'influencer', 'researcher']).count()
        new_admin = user_data.filter(DateOfRegistration__gte=(timezone.now(
        )-timedelta(days=30)), groups__name__in=['admin', 'child_admin']).count()
        d_experts = StatusReason.objects.filter(ReasonType__exact="Deactivation", CurrentTime__gte=(
            timezone.now()-timedelta(days=30))).count()

        registrations_info.update({"new_patients": new_patients,
                                   "new_experts": new_experts,
                                   "new_admin": new_admin,
                                   "experts_deactivated": d_experts})

        return Response({"user_information": user_information,
                         "recent_appointments": recent_apps,
                         "registration_info": registrations_info,
                         "prescription_data": prescription_data})


class HomepageSearchBarUser(views.APIView):
    permission_classes = [IsCuAdminPermission]

    def get(self, r, *args, **kwargs):
        user_data = CuUser.objects.all()
        # --------------------user_search_data-----------------------------------
        search_user_data = []
        if 'name' in r.GET and r.GET['name'] != '':
            try:
                name_id = int(r.GET['name'])
                y = user_data.filter(id__exact=name_id) | user_data.filter(
                    name__icontains=r.GET['name']) | user_data.filter(email__icontains=r.GET['name'])
            except ValueError:
                y = user_data.filter(name__icontains=r.GET['name']) | user_data.filter(
                    email__icontains=r.GET['name'])
                print(f"--------------name filter-------{y}")

            search_user_data = [serialize_model(t, CuUserFilterSerializer) | {
                "user_role": get_cu_user_type(t.id)} for t in y]
        return JsonResponse(search_user_data, safe=False)
# added

# added


class HomepageFilterAppointmentUser(views.APIView):
    permission_classes = [IsCuAdminPermission]

    def get(self, r, *args, **kwargs):
        appointment_data = Appointments.objects.all()
        appointment_info = dict()

        if 'start_date' in r.GET and r.GET['start_date'] != '':
            start_date = timezone.make_aware(parse_datetime(r.GET['start_date']),
                                             timezone.get_current_timezone())
            appointment_data = appointment_data.filter(
                slot_id__schedule_start_time__gte=start_date)

        if 'end_date' in r.GET and r.GET['end_date'] != '':
            end_date = timezone.make_aware(parse_datetime(r.GET['end_date']),
                                           timezone.get_current_timezone())
            appointment_data = appointment_data.filter(
                slot_id__schedule_end_time__lte=end_date)

        meeting_c = MeetingSession.objects.filter(MeetingStatus__exact=2)
        appoint_ids = meeting_c.values("AppointmentId")
        total_completed_apps = appointment_data.filter(
            id__in=appoint_ids, slot_id__schedule_start_time__lte=timezone.now()).count()
        meeting_u = MeetingSession.objects.filter(AppointmentId__slot_id_id__schedule_start_time__lte=timezone.now(
        ), AppointmentId__status__in=['B', 'P', 'R'], MeetingStatus__in=['0', '1', '3'])
        appointment_ids = meeting_u.values("AppointmentId")
        total_unattended_apps = appointment_data.filter(
            id__in=appointment_ids).count()
        appointment_info.update({"total_upcoming_consultations": appointment_data.filter(status__in=['B', 'P', 'R'], slot_id_id__schedule_start_time__gte=timezone.now()).count(),
                                 "total_completed_consultations": total_completed_apps,
                                 "total_cancelled_consultations": appointment_data.filter(status="C").count(),
                                 "total_rescheduled_consultations": appointment_data.filter(status="R").count(),
                                 "total_unattended_consultations": total_unattended_apps})

        return JsonResponse(appointment_info)
# added


def EditAdminPerms(a, perms):
    ct = ContentType.objects.get_for_model(get_user_model())
    for x in perms:
        if x[2] == 1:

            p1 = Permission.objects.get(codename=x[0], name=x[1],
                                        content_type=ct)
            a.user_permissions.add(p1)

        if x[2] == 0:
            p1 = Permission.objects.get(codename=x[0], name=x[1],
                                        content_type=ct)
            print(p1)
            a.user_permissions.remove(p1)
    perm_list = []
    l1 = dict()
    l1.update({'permissions': a.get_all_permissions()})
    perm_list.append(l1)
    return perm_list


class EditPermissionView(generics.UpdateAPIView, generics.DestroyAPIView):
    queryset = Permission.objects.all()
    serializer_class = PermissionSerializer
    permission_classes = [IsCuAdminPermission]  # Restrict access to admins
    lookup_field = 'id'

    def delete(self, request, *args, **kwargs):
        try:
            if 'id' not in kwargs:
                return generate_response(
                    success=False,
                    message="The 'id' parameter is required for deletion.",
                    status_code=status.HTTP_400_BAD_REQUEST,
                )
            instance = self.get_object()
            self.destroy(instance)

            return generate_response(
                success=True,
                message="Permission deleted successfully.",
                status_code=status.HTTP_200_OK,
            )

        except Exception as e:
            return generate_response(
                success=False,
                message=str(e),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def update(self, request, *args, **kwargs):
        try:
            permission_instance = self.get_object()
            serializer = self.get_serializer(
                permission_instance, data=request.data, partial=True)

            if serializer.is_valid():
                serializer.save()
                return generate_response(
                    success=True,
                    data=serializer.data,
                    status_code=status.HTTP_200_OK,
                    message="Permission updated successfully"
                )

            return generate_response(
                success=False,
                message=serializer.errors,
                status_code=status.HTTP_400_BAD_REQUEST
            )

        except Permission.DoesNotExist:
            return generate_response(
                success=False,
                message="Permission not found"
            )

        except Exception as e:
            return generate_response(
                success=False,
                message=str(e),
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CreateListPermissionsView(generics.ListCreateAPIView):
    permission_classes = [IsCuAdminPermission]

    def get(self, r, *args, **kwargs):
        all_permissions = Permission.objects.filter(content_type__exact=6)
        perm_list = []
        for permission in all_permissions:
            l1 = dict()
            l1 = {
                "id": permission.id,
                "Name": permission.name,  # Permission Name
                "Codename": permission.codename  # Permission Code Name
            }
            perm_list.append(l1)
        return Response(perm_list)

    def post(self, r, *args, **kwargs):
        # get the content type of the user model
        ct = ContentType.objects.get_for_model(get_user_model())
        perms = r.data['perms']
        p1 = Permission.objects.filter(
            codename=perms[0], name=perms[1], content_type=ct)
        if p1.exists():
            return Response({"message": "Permission already exists"})
        p1 = Permission.objects.create(
            codename=perms[0], name=perms[1], content_type=ct)
        return Response({"message": "Successfully created permission"})


class GetEditChildAdminPermissionView(generics.RetrieveUpdateAPIView):
    permission_classes = [IsCuAdminPermission]
    lookup_field = 'AdminId_id'

    def get(self, r, *args, **kwargs):
        perm_list = []
        user_id = self.kwargs['AdminId_id']
        user_details = CuUser.objects.get(id__exact=user_id)
        all_permissions = Permission.objects.filter(content_type__exact=6)
        perm_list = []
        perm_s = user_details.get_all_permissions()
        for permission in all_permissions:
            stri_p = str("cu_app."+permission.codename)
            if stri_p in perm_s:
                l1 = dict()
                l1.update({"Name": permission.name,
                           "Codename": permission.codename,
                           "Status": 1})
                perm_list.append(l1)
            else:
                l1 = dict()
                l1.update({"Name": permission.name,
                           "Codename": permission.codename,
                           "Status": 0})
                perm_list.append(l1)
        return Response(perm_list)

    def put(self, r, *args, **kwargs):
        user_id = self.kwargs['AdminId_id']
        l1 = dict()
        user_details = CuUser.objects.get(id__exact=user_id)
        zz = EditAdminPerms(user_details, r.data['perms'])
        return Response(zz)


class ExpertPaymentPage(generics.RetrieveAPIView):
    permission_classes = [IsCuAdminPermission]
    serializer_class = DoctorDetailsSerializer
    queryset = DoctorDetails.objects.all()
    lookup_field = 'DoctorId'

    def get(self, r, *args, **kwargs):
        doctor_obj = self.retrieve(r, *args, **kwargs)
        app_data = Appointments.objects.filter(meetingsession__MeetingStatus__in=[
            1, 2, 3], slot_id__doctor_id__exact=doctor_obj.data['DoctorId'], slot_id__schedule_end_time__lt=timezone.now()-timedelta(days=3))
        stripe.api_key = os.getenv("STRIPE_API_KEY")
        if 'page' in r.GET and r.GET['page'] != "":

            # Get the page number from the request
            page_number = r.GET.get('page', 1)
            # Get the number of items per page from the request
            items_per_page = r.GET.get('per_page', 10)
            total_items = app_data.count()
            paginator = Paginator(app_data, items_per_page)
            print(paginator)
            if int(page_number) not in range(1, int(paginator.num_pages)+1):
                return HttpResponse("Not a valid page number", status=400)
            app_data = paginator.page(page_number)
            expert_dues = []
            for i in app_data:
                to_be_paid = "N"
                q_a = PatientQueries.objects.filter(ApptId_id__exact=i.id)
                start_date = i.slot_id.schedule_start_time
                end_date = i.slot_id.schedule_end_time
                fees_factor = end_date - start_date
                fees_factor = fees_factor.total_seconds() / 1800
                start_date = timezone.localtime(start_date).date()
                if q_a.exists():
                    q_r = q_a.filter(ReplyTo__exact=q_a[0].id)
                    if q_r.exists():
                        to_be_paid = "Y"
                else:
                    to_be_paid = "Y"
                payment_details = PatientPayment.objects.filter(
                    AppointmentId_id__exact=i.id)[0]
                p_int = stripe.PaymentIntent.retrieve(
                    payment_details.PaymentIntent)
                l1 = dict()
                l1.update({"appointmentId": i.id,
                           "appointment_date": start_date,
                           "appointment_amount": f"{p_int.currency} {p_int.amount}",
                           "doctor_consultation_fees": fees_factor*doctor_obj.data['ConsultationFees'],
                           "to_be_paid": to_be_paid})
                expert_dues.append(l1)

            response_data = {
                'total_items': total_items,
                'total_pages': paginator.num_pages,
                'items': expert_dues
            }
            return JsonResponse(response_data)
        else:
            f_due_amount = 0
            for i in app_data:
                to_be_paid = "N"
                q_a = PatientQueries.objects.filter(ApptId_id__exact=i.id)
                start_date = i.slot_id.schedule_start_time
                end_date = i.slot_id.schedule_end_time
                fees_factor = end_date - start_date
                fees_factor = fees_factor.total_seconds() / 1800
                start_date = timezone.localtime(start_date).date()
                if q_a.exists():
                    q_r = q_a.filter(ReplyTo__exact=q_a[0].id)
                    if q_r.exists():
                        f_due_amount += fees_factor * \
                            doctor_obj.data['ConsultationFees']
                    else:
                        pass
                else:
                    f_due_amount += fees_factor * \
                        doctor_obj.data['ConsultationFees']
            return JsonResponse({"final_due_amount": f_due_amount})

# added


class CreateReportTypeView(generics.CreateAPIView):
    permission_classes = [IsCuAdminPermission]
    serializer_class = ReportTypeSerializer
    queryset = ReportType.objects.all()

    def post(self, r, *args, **kwargs):
        resp = self.create(r, *args, **kwargs)
        print(f"-----report_type------{resp}")
        return resp


class GetUpdateReportTypeView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsCuAdminPermission]
    serializer_class = ReportTypeSerializer
    queryset = ReportType.objects.all()
    lookup_field = 'id'

    def get(self, r, *args, **kwargs):
        if self.kwargs['id'] == "all":
            r_t = ReportType.objects.all()
            rt = [serialize_model(t, ReportTypeSerializer) for t in r_t]
            return JsonResponse(rt, safe=False)
        a = self.retrieve(r, *args, **kwargs)
        return a

    def put(self, request, *args, **kwargs):
        a = self.partial_update(request, *args, **kwargs)
        return a

    def delete(self, request, *args, **kwargs):
        res = self.destroy(request, *args, **kwargs)
        return res

# added


class GetApproveExpertReviewViews(generics.RetrieveUpdateAPIView):
    serializer_class = DoctorReviewsSerializer
    permission_classes = [IsCuAdminPermission]
    lookup_field = 'id'
    queryset = DoctorReviews.objects.all()

    def get(self, request, *args, **kwargs):
        # -----added to get all expert reviews approval----------------------------------------------------------
        if self.kwargs['id'] == 'all':
            doctor_reviews = DoctorReviews.objects.filter(
                ReviewStatus__exact=1)
            res = []
            # Get the page number from the request
            page_number = request.GET.get('page', 1)
            # Get the number of items per page from the request
            items_per_page = request.GET.get('per_page', 10)
            total_items = doctor_reviews.count()
            paginator = Paginator(doctor_reviews, items_per_page)
            if int(page_number) not in range(1, int(paginator.num_pages)+1):
                return HttpResponse("Not a valid page number", status=400)
            doctor_reviews = paginator.page(page_number)
            for x in doctor_reviews:
                z = serialize_model(x, DoctorReviewsSerializer)
                z['expert_role'] = get_cu_user_type(x.ExpertId_id)
                res.append(z)
            return JsonResponse({'total_items': total_items,
                                 'total_pages': paginator.num_pages,
                                 'items': res})
        # ---------------------------------------------------------------------------------------------------------
        else:
            doctor_reviews = DoctorReviews.objects.filter(
                ExpertId_id__exact=self.kwargs['id'], ReviewStatus__exact=1)
            res = []
            for x in doctor_reviews:
                res.append(serialize_model(x, DoctorReviewsSerializer))
            return JsonResponse(res, safe=False)

    def put(self, request, *args, **kwargs):
        print(f"doctor consent approval-----------")
        a = self.partial_update(request, *args, **kwargs)
        print(f"a-------------{a}-------{type(a)}")
        if a is not None and a.status_code == 200:
            doctor_reviews = DoctorReviews.objects.filter(
                id__exact=self.kwargs['id'])[0]
            if request.data['ReviewStatus'] == 3:
                aa = StatusReason.objects.create(Reason=request.data['Reason'], CurrentTime=timezone.now(
                ), ExpertId_id=doctor_reviews.ExpertId_id, ReasonCategory=f"{a.data['id']}_review_rejection", ReasonType="Review_Rejection")
            else:
                pass
            noti_check = notification_check('Review approval!')
            if noti_check == True:
                r_p = AdminApprovePushNotification(
                    'Review approval!', doctor_reviews.ExpertId_id, request.data['ReviewStatus'], request.GET['user_id'], "N")
                print(
                    f'-----------admin approval status push response-------{r_p}------------')
            else:
                pass
            e_check = email_check('Review approval!')
            if e_check == True:
                r_p = AdminApprovePushNotification(
                    'Review approval!', doctor_reviews.ExpertId_id, request.data['ReviewStatus'], request.GET['user_id'], "E")
                print(
                    f'-----------admin approval status email response-------{r_p}------------')
            else:
                pass
            return JsonResponse({"message": a.data})
        else:
            return JsonResponse({"message": "Doctor review approval failed"})

    def delete(self, r, *args, **kwargs):
        r_id = DoctorReviews.objects.filter(id__exact=self.kwargs['id'])
        if r_id.exists():
            r_id = DoctorReviews.objects.get(id__exact=self.kwargs['id'])
            a = ContentRemoval(r_id, "doctor_reviews")
            return Response(a)
        else:
            return Response("Item doesn't exist", status=404)


# added

def ProfileUpdateNotification(user_id, admin_id):
    current_time = timezone.now()
    u_name = get_user_model().objects.get(id__exact=user_id).name
    a_name = get_user_model().objects.get(id__exact=admin_id).name
    print(f"in user nameeeeeeeeeeeeeeeeeeeee{u_name}")
    device = FCMDevice.objects.filter(user_id__exact=user_id)
    print(f"in reminderrrrrrrrrrr{device}")
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    a = device.send_message(Message(notification=Notification(title='Profile updated by admin!',
                                                              body=f'Hi {u_name}, Your profile has been updated by admin {a_name}.'),
                                    webpush=WebpushConfig(fcm_options=WebpushFCMOptions(
                                        link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/profile-Setting'))))
    res = PushNotifications.objects.create(UserId=user_id, NotificationTime=current_time,
                                           Title='Profile updated by admin!',
                                           Body=f'Hi {u_name}, Your profile has been updated by admin {a_name}.', Link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/profile-Setting')

    device = FCMDevice.objects.filter(user_id__exact=admin_id)
    print(f"in reminderrrrrrrrrrr{device}")
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    a = device.send_message(Message(notification=Notification(title='User profile updated!',
                                                              body=f'Hi {a_name}, You have updated {u_name}\'s profile.'),
                                    webpush=WebpushConfig(fcm_options=WebpushFCMOptions(
                                        link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP') + '/usermanagement'))))
    res = PushNotifications.objects.create(UserId=admin_id, NotificationTime=current_time,
                                           Title='User profile updated!',
                                           Body=f'Hi {a_name}, You have updated {u_name}\'s profile.', Link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP') + '/usermanagement')

    return a


# def ProfileUpdateEmail(user_id, admin_id):
#     pass

# added
class UserUpdate(generics.RetrieveUpdateAPIView):
    permission_classes = [IsCuAdminPermission]
    queryset = CuUser.objects.all()
    lookup_field = 'email'
    serializer_class = CuUserRegisterSerializer

    def partial_update(self, request, *args, **kwargs):

        instance = self.get_object()
        if get_cu_user_type(instance.id) == 'patient':
            if 'cancer_type_main' in request.data and 'cancer_type_sub' in request.data and 'cancer_type_level' in request.data:
                t1 = request.data['cancer_type_main'], request.data['cancer_type_sub'], request.data['cancer_type_level']
                l1 = list(t1)
                instance.cancer_type = l1
                instance.save()
                return super().partial_update(request, *args, **kwargs)
            else:
                return super().partial_update(request, *args, **kwargs)

        return super().partial_update(request, *args, **kwargs)

    def put(self, request, *args, **kwargs):
        print(f"{self.kwargs.get('email')}")
        a = self.partial_update(request, *args, **kwargs)
        id_val = CuUser.objects.filter(
            email__exact=self.kwargs.get('email'))[0].id
        user_role = get_cu_user_type(id_val)
        print(f"updateeee----------{user_role}")
        if a.status_code == 200:
            if user_role == "patient":
                upd_status = UpdatePatientData(
                    request.data, kwargs, request.FILES, request.META['HTTP_HOST'])
                if upd_status:
                    dict1 = {}
                    dict1['user_update_status'] = True
                    # notification_check
                    n_check = notification_check("Profile updated by admin")
                    if n_check == True:
                        u_na = ProfileUpdateNotification(
                            id_val, request.GET['user_id'])
                        print(f"-----profile_update_notification{u_na}")
                    # email_check
                    e_check = email_check("Profile updated by admin")
                    if e_check == True:
                        u_ea = SendEmailProfileAction(
                            a.data['email'], "updated", a.data['name'], "", user_role)
                        print(f"-----profile_update_notification{u_ea}")
                    return Response(json.dumps(dict1))
                else:
                    return Response(json.dumps({"message": "could not update"}))
            # added
            elif user_role in ['admin', 'child_admin']:
                upd_status = UpdateAdminData(
                    request.data, kwargs, request.FILES, request.META['HTTP_HOST'])
                if upd_status:
                    dict1 = {}
                    dict1['user_update_status'] = True
                    # notification_check
                    # n_check = notification_check("Profile updated by admin")
                    # if n_check == True:
                    #     u_na = ProfileUpdateNotification(
                    #         id_val, request.GET['user_id'])
                    #     print(f"-----profile_update_notification{u_na}")
                    # # email_check
                    # e_check = email_check("Profile updated by admin")
                    # if e_check == True:
                    #     u_ea = SendEmailProfileAction(
                    #         a.data['email'], "updated", a.data['name'], "", user_role)
                    #     print(f"-----profile_update_notification{u_ea}")
                    return Response(json.dumps(dict1))
                else:
                    return HttpResponse("incorrect current password", status=403)
            # added
            elif user_role == "doctor" or user_role == "researcher" or user_role == "influencer":
                upd_status = UpdateDoctorData(
                    request.data, kwargs, request.FILES, request.META['HTTP_HOST'])
                if upd_status:
                    # update expertise data of expert
                    b = get_user_model().objects.get(email=a.data['email'])

                    exp_Arr = []
                    i = 0
                    while True:

                        a = request.data.get(f"expertise_name[{i}]", 'na')
                        print(f"------{i}------------{a}")
                        if a == 'na':
                            break
                        exp_Arr.append(request.data[f"expertise_name[{i}]"])
                        i += 1

                    print(f"arrayyyyy---{exp_Arr}")
                    dict1 = {}
                    for x in exp_Arr:
                        e1 = ExpertiseCancertype.objects.filter(name=x).first()
                        if e1:
                            b.expertise.add(e1)
                            res = b.save()
                            print(f"ressss---{res}--{type(res)}")

                        else:
                            dict1['message'] = "updated but no expertise data found"
                            print(f"not foundddddddddddd")
                            break
                    dict1['user_update_status'] = True
                    # notification_check
                    n_check = notification_check("Profile updated by admin")
                    if n_check == True:
                        u_na = ProfileUpdateNotification(
                            id_val, request.GET['user_id'])
                        print(f"-----profile_update_notification{u_na}")
                    # email_check
                    e_check = email_check("Profile updated by admin")
                    if e_check == True:
                        u_ea = SendEmailProfileAction(
                            b.email, "updated", b.name, "", user_role)
                        print(f"-----profile_update_notification{u_ea}")
                    return Response(json.dumps(dict1))

                else:
                    return Response(json.dumps({"message": "could not update"}))

        else:

            return Response(json.dumps({"message": "could'nt update"}))


def UpdatePatientData(a, b, c, d):
    print(f"files patients------{c}----{type(c)}")

    d_id = CuUser.objects.get(email=b['email'])  # get user object
    patient_obj = PatientDetails.objects.filter(
        PatientId=d_id)[0]  # get patient details object
    # update the relevant fields using loop
    for x in a:

        if not isinstance(a[x], InMemoryUploadedFile) and not x in ["phone", "date_of_birth", "name", "email", "sex", "age", "TimeZone", "City", "Country", "ProfilePhoto", "Signature"]:
            setattr(patient_obj, x, a[x])

    res = patient_obj.save()

    for x in c:
        print(f"in loop patient files---{x}--{c[x]}")
        if x == "ProfilePhoto":
            print(f"yessss-----------{x}")
            b1 = handle_uploaded_file(c[x])
            f_name = b1.split('/')[2]
            file_url = settings.PROJECT_ROOT + b1
            file_urls_res = handle_s3_uploaded_file(f_name, file_url)
            patient_obj.ProfilePhoto = file_urls_res
        elif x == "Signature":
            b1 = handle_uploaded_file(c[x])
            f_name = b1.split('/')[2]
            file_url = settings.PROJECT_ROOT + b1
            file_urls_res = handle_s3_uploaded_file(f_name, file_url)
            patient_obj.Signature = file_urls_res
        else:
            file_urls_res = []

        print(f"patienttttttt-------filesulrs---{file_urls_res}")

    res = patient_obj.save()

    return True

# added


def UpdateAdminData(a, b, c, d):
    print(f"files admin------{c}----{type(c)}")

    d_id = CuUser.objects.get(email=b['email'])  # get user object
    admin_obj = AdminDetails.objects.filter(
        AdminId=d_id)[0]  # get patient details object
    # update the relevant fields using loop
    for x in a:

        if not isinstance(a[x], InMemoryUploadedFile) and not x in ["phone", "date_of_birth", "name", "email", "sex", "age", "TimeZone", "City", "Country", "ProfilePhoto"]:
            setattr(admin_obj, x, a[x])

    res = admin_obj.save()
    # added
    if "current_password" in a and "new_password" in a:
        password_match = check_password(a["current_password"], d_id.password)
        if password_match:
            hashed_password = make_password(a['new_password'])
            d_id.password = hashed_password
            d_id.save()
        else:
            return False
    # added
    for x in c:
        print(f"in loop patient files---{x}--{c[x]}")
        if x == "ProfilePhoto":
            print(f"yessss-----------{x}")
            b1 = handle_uploaded_file(c[x])
            f_name = b1.split('/')[2]
            file_url = settings.PROJECT_ROOT + b1
            file_urls_res = handle_s3_uploaded_file(f_name, file_url)
            admin_obj.ProfilePhoto = file_urls_res
        # elif x == "Signature":
        #     b1 = handle_uploaded_file(c[x])
        #     f_name = b1.split('/')[2]
        #     file_url = settings.PROJECT_ROOT + b1
        #     file_urls_res = handle_s3_uploaded_file(f_name, file_url)
        #     patient_obj.Signature = file_urls_res
        else:
            file_urls_res = []

        print(f"patienttttttt-------filesulrs---{file_urls_res}")

    res = admin_obj.save()

    return True
# added


def AddUserRole(a, role):
    try:
        ct = ContentType.objects.get_for_model(cu_permissions)
        p1 = Permission.objects.get_or_create(codename='cu_patient_permissions', name='CU patient permissions',
                                              content_type=ct)
        g1 = Group.objects.get_or_create(name=role)
        g1[0].permissions.add(p1[0])
        if len(a.groups.all()) > 1:
            return False
        else:
            a.groups.add(g1[0])
        return a
    except Exception as e:
        print("add role exceptionsss", e)
        try:
            us = get_user_model().objects.get(email=a['email']).delete()
            print(f"user deleted role...{us}---{get_user_model()}")
        except Exception as f:
            print("delete user role exceptionsss", f)
        return str(e)


def UpdateDoctorData(a, b, c, d):
    print(f"files doctors----------{c}----{type(c)}")
    d_id = CuUser.objects.get(email=b['email'])  # get user object
    doctor_obj = DoctorDetails.objects.filter(
        DoctorId=d_id)[0]  # get details object
    # update the relevant fields using loop
    for x in a:
        if not isinstance(a[x], InMemoryUploadedFile) and not x in ["expertise_name", "phone", "date_of_birth", "name", "email", "sex", "age", "TimeZone", "cancer_type", "City", "Country", "ProfilePhoto", "Signature", "SocialLinks"]:
            setattr(doctor_obj, x, a[x])
        if x in ['ExperienceSummary', 'ResearchPapers', 'OtherAchievements', 'SocialLinks']:
            setattr(doctor_obj, x, json.loads(a[x]))

    if 'Reason' in a:
        doctor_obj.ReasonDate = timezone.now()
    # user role set
    if 'user_role' in a and d_id.approval in ['pending']:
        azz = AddUserRole(d_id, a['user_role'])
        print(f'-------------------user_role-----------{azz}')
    res = doctor_obj.save()

    # add multiple certs
    r = re.compile(r"^Certificates")
    file_list_keys = list(filter(r.match, c.keys()))
    file_list_values = [y for x, y in c.items() if x in file_list_keys]
    data_list_keys = list(filter(r.match, a.keys()))
    data_list_values = [y for x, y in a.items(
    ) if x in data_list_keys and x not in file_list_keys]

    print(f"regerxxxxxxxxxxxxxxxxxx{data_list_values}--")

    certs_len = len(file_list_values)
    certs = []

    i = 0
    print(f"length---{certs_len}")

    for x in file_list_values:

        b1 = handle_uploaded_file(x)
        f_name = b1.split('/')[2]
        file_url = settings.PROJECT_ROOT + b1
        file_urls_res = handle_s3_uploaded_file(f_name, file_url)
        certs.append(file_urls_res)

    for x in data_list_values:
        val1 = x.split('/')[3]
        val2 = val1.split('?')[0]
        certs.append(val2)

    print(f"certificates---{certs}")
    doctor_obj.Certificates = certs
    # add multiple certs ends

    for x in c:
        print(f"in loop doctor files---{x}--{c[x]}")
        if x == "ProfilePhoto":
            b1 = handle_uploaded_file(c[x])
            f_name = b1.split('/')[2]
            file_url = settings.PROJECT_ROOT + b1

            file_urls_res = handle_s3_uploaded_file(f_name, file_url)
            print(f"urlsssssssssss{file_urls_res}")
            doctor_obj.ProfilePhoto = file_urls_res

        elif x == "Signature":
            b1 = handle_uploaded_file(c[x])
            f_name = b1.split('/')[2]
            file_url = settings.PROJECT_ROOT + b1
            file_urls_res = handle_s3_uploaded_file(f_name, file_url)
            doctor_obj.Signature = file_urls_res
        else:
            file_urls_res = []
    res = doctor_obj.save()
    return True

# added


def send_deleted_email(u_email, u_name, host, reason):
    verify_url1 = host+"/deletedAccount"
    payload = {
        "template_key": "2518b.41c485fda42f6e5f.k1.6b13c4b0-287f-11ef-8dec-525400ab18e6.1900afdbc7b",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": u_email,
                    "name": u_email,
                }
            }

        ],
        "merge_info": {
            "u_name": u_name,
            "delete_link": verify_url1,
            "reason": reason,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }

    headers = {
        'Authorization': f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
    try:
        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload))
        print(
            f"pw mail send-----------{response.status_code}-------{response.content}--------")
        response_status = True if response.status_code == 200 else True if response.status_code in [
            201, 202] else False
        return response_status
    except HTTPError as e:
        print(f"exception-----------{e.to_dict}")
        return response_status

# added


class UserDeleteAPIView(generics.UpdateAPIView):
    queryset = CuUser.objects.all()
    permission_classes = [IsCuAdminPermission]
    lookup_field = 'email'
    serializer_class = CuUserFilterSerializer

    def put(self, request, *args, **kwargs):
        u_data = CuUser.objects.filter(email__exact=self.kwargs['email'])[0]
        u_name = u_data.name
        res = self.partial_update(request, *args, **kwargs)
        if res.status_code == 200:
            if get_cu_user_type(u_data.id) in ['doctor', 'researcher', 'influencer']:
                app_url = os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP")
                zz = send_deleted_email(
                    self.kwargs['email'], u_name, app_url, "by admin")
                # inserting doctor data into zoho accounts
                acc_zoho = AddToZoho(CuUser.objects.get(
                    email__exact=self.kwargs['email']))
                # ---------------------------------------------------------------------
            # elif get_cu_user_type(u_data.id) =="patient":
            #     app_url = os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP")
            #     zz=send_deleted_email(self.kwargs['email'], u_name,app_url,"by admin")
            #     acc_zoho=AddToZohoContacts(CuUser.objects.get(email__exact=self.kwargs['email']))
            elif get_cu_user_type(u_data.id) == "child_admin":
                app_url = os.getenv("NEXT_CANCER_UNWIRED_ADMIN_APP")
                zz = send_deleted_email(
                    self.kwargs['email'], u_name, app_url, "by admin")
            else:
                pass
            return res
        else:
            return Response("Account deletion failed")
    # ----------------------------------------------------------------------------------

# added


class CreateContentTypeView(generics.CreateAPIView):
    permission_classes = [IsCuAdminPermission]
    serializer_class = ContentTypeSerializer
    queryset = AdminContentManagement.objects.all()

    def post(self, r, *args, **kwargs):
        r_t = AdminContentManagement.objects.filter(
            Category__exact=r.data['Category'], Content__exact=r.data['Content'])
        if r_t.exists():
            return Response("Data already exists")

        elif r.data['Category'] == "Cancellation Refund Policy":
            r_t = AdminContentManagement.objects.filter(
                Category__exact=r.data['Category'], Content__icontains=r.data['Content']['Time'])
            dup = False
            for x in r_t:
                if x.Content['Time'] == r.data['Content']['Time']:
                    dup = True
                    break
                else:
                    pass
            if dup == True:
                return Response("Data already exists")
            else:
                resp = self.create(r, *args, **kwargs)
                print(f"-----content_type------{resp}")
                return resp
        elif r.data['Category'] == 'Reschedule':
            r_t = AdminContentManagement.objects.filter(
                Category__exact=r.data['Category'])
            if r_t.exists():
                return Response("Data already exists")
            else:
                resp = self.create(r, *args, **kwargs)
                print(f"-----content_type------{resp}")
                return resp
        else:
            resp = self.create(r, *args, **kwargs)
            print(f"-----content_type------{resp}")
            return resp


class GetUpdateContentTypeView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsCuAdminPermission]
    serializer_class = ContentTypeSerializer
    queryset = AdminContentManagement.objects.all()
    lookup_field = 'id'

    def get(self, r, *args, **kwargs):
        if self.kwargs['id'] == "all":
            r_t = AdminContentManagement.objects.filter(
                Category__exact=r.GET['Category'])
            rt = [serialize_model(t, ContentTypeSerializer) for t in r_t]
            return JsonResponse(rt, safe=False)
        a = self.retrieve(r, *args, **kwargs)
        return a

    def put(self, request, *args, **kwargs):
        a = self.partial_update(request, *args, **kwargs)
        return a

    def delete(self, request, *args, **kwargs):
        res = self.destroy(request, *args, **kwargs)
        return res

# added Get,update,delete expertise


class GetUpdateExpertiseView(generics.RetrieveUpdateDestroyAPIView):
    permission_classes = [IsCuAdminPermission]
    queryset = ExpertiseCancertype.objects.all()
    lookup_field = 'id'
    serializer_class = ExpertiseCancertypeSerializer

    def get(self, r, *args, **kwargs):
        if self.kwargs['id'] == "all":
            doctor_fees = ExpertiseCancertype.objects.all()
            a = ExpertiseCancertypeSerializer(doctor_fees, many=True)
            return Response(a.data)
        a = self.retrieve(r, *args, **kwargs)
        return a

    def put(self, r, *args, **kwargs):
        a = self.partial_update(r, *args, **kwargs)
        return a

    def delete(self, request, *args, **kwargs):
        res = self.destroy(request, *args, **kwargs)
        return res

# added for expert wallet transactions


# class GetUpdateExpertPaymentView(views.APIView):
#     serializer_class = ExpertWalletTransactionsSerializer
#     queryset = ExpertWalletTransactions.objects.all()

#     def get(self, request, *args, **kwargs):
#         if self.kwargs['expert_id'] == "pending":
#             exp_obj = ExpertWalletTransactions.objects.filter(
#                 PaymentStatus=0).order_by('-TransactionDate')
#             exp_list = []
#             for x in request.GET:
#                 if x == "role":
#                     exp_obj = exp_obj.filter(
#                         WalletId__ExpertId__groups__name=request.GET['role'])
#                 if x == "start_date":
#                     start_date = timezone.make_aware(parse_datetime(request.GET['start_date']),
#                                                      timezone.get_current_timezone())
#                     exp_obj = exp_obj.filter(TransactionDate__gte=start_date)
#                 if x == "end_date":
#                     end_date = timezone.make_aware(parse_datetime(request.GET['end_date']),
#                                                    timezone.get_current_timezone())
#                     exp_obj = exp_obj.filter(TransactionDate__lte=end_date)
#                 if x == "search":
#                     exp_obj = exp_obj.filter(
#                         WalletId__ExpertId__name__icontains=request.GET['search'])
#             if 'page' in request.GET and request.GET['page'] != "":

#                 # Get the page number from the request
#                 page_number = request.GET.get('page', 1)
#                 # Get the number of items per page from the request
#                 items_per_page = request.GET.get('per_page', 10)
#                 total_items = exp_obj.count()
#                 paginator = Paginator(exp_obj, items_per_page)
#                 print(paginator)
#                 if int(page_number) not in range(1, int(paginator.num_pages)+1):
#                     return HttpResponse("Not a valid page number", status=400)
#                 exp_obj = paginator.page(page_number)
#             for x in exp_obj:
#                 exp_wal_ser = serialize_model(
#                     x, ExpertWalletTransactionsSerializer)
#                 exp_wal_ser['total_expert_fees'] = exp_wal_ser['TransactionAmount'] + \
#                     exp_wal_ser['CommissionAmount']
#                 if exp_wal_ser['Invoice'] is not None:
#                     new_obj_url = get_s3_signed_url_bykey(
#                         exp_wal_ser['Invoice'])
#                     exp_wal_ser['Invoice'] = new_obj_url
#                 expert_id = ExpertWallet.objects.get(
#                     id__exact=exp_wal_ser['WalletId']).ExpertId_id
#                 dict1 = dict()
#                 expert_data = CuUser.objects.get(id__exact=expert_id)
#                 exp_p = DoctorDetails.objects.get(
#                     DoctorId_id__exact=expert_id).ProfilePhoto
#                 dict1.update({'expert_id': expert_id,
#                               'expert_name': expert_data.name,
#                               'expert_email': expert_data.email,
#                               'expert_approval': expert_data.approval,
#                               'expert_role': get_cu_user_type(expert_id),
#                               'expert_profile_photo': get_s3_signed_url_bykey(exp_p) if exp_p is not None else None,
#                               "expert_member_code": DoctorDetails.objects.get(DoctorId_id__exact=expert_id).MemberCode})
#                 exp_wal_ser['expert_details'] = dict1
#                 exp_list.append(exp_wal_ser)
#             # if 'page' in request.GET and request.GET['page'] != "":
#             response_data = {
#                 'total_items': total_items,
#                 'total_pages': paginator.num_pages,
#                 'items': exp_list
#             }
#             return Response(response_data)
#             # else:
#             #     return Response(exp_list)
#         elif self.kwargs['expert_id'] == "cleared":
#             exp_obj = ExpertWalletTransactions.objects.filter(
#                 PaymentStatus=1).order_by('-TransactionDate')
#             for x in request.GET:
#                 if x == "role":
#                     exp_obj = exp_obj.filter(
#                         WalletId__ExpertId__groups__name=request.GET['role'])
#                 if x == "start_date":
#                     start_date = timezone.make_aware(parse_datetime(request.GET['start_date']),
#                                                      timezone.get_current_timezone())
#                     exp_obj = exp_obj.filter(TransactionDate__gte=start_date)
#                 if x == "end_date":
#                     end_date = timezone.make_aware(parse_datetime(request.GET['end_date']),
#                                                    timezone.get_current_timezone())
#                     exp_obj = exp_obj.filter(TransactionDate__lte=end_date)
#                 if x == "search":
#                     exp_obj = exp_obj.filter(
#                         WalletId__ExpertId__name__icontains=request.GET['search'])
#             wallet_ids = list(exp_obj.values_list('WalletId', flat=True))
#             wallet_ids = list(set(wallet_ids))
#             print(wallet_ids)
#             exp_list = []
#             if 'page' in request.GET and request.GET['page'] != "":

#                 # Get the page number from the request
#                 page_number = request.GET.get('page', 1)
#                 # Get the number of items per page from the request
#                 items_per_page = request.GET.get('per_page', 10)
#                 total_items = len(wallet_ids)
#                 paginator = Paginator(wallet_ids, items_per_page)
#                 print(paginator)
#                 if int(page_number) not in range(1, int(paginator.num_pages)+1):
#                     return HttpResponse("Not a valid page number", status=400)
#                 wallet_ids = paginator.page(page_number)
#             for i in wallet_ids:
#                 x = ExpertWalletTransactions.objects.filter(
#                     PaymentStatus=1, WalletId__exact=i).order_by("-ClearedDate")[0]
#                 y = ExpertWalletTransactions.objects.filter(
#                     WalletId__exact=i).order_by("-ClearedDate")[0]
#                 exp_wal_ser = serialize_model(
#                     x, ExpertWalletTransactionsSerializer)
#                 exp_wal_ser['total_expert_fees'] = exp_wal_ser['TransactionAmount'] + \
#                     exp_wal_ser['CommissionAmount']
#                 exp_wal_ser['LatestBalanceAmount'] = y.BalanceAmount
#                 if exp_wal_ser['Invoice'] is not None:
#                     new_obj_url = get_s3_signed_url_bykey(
#                         exp_wal_ser['Invoice'])
#                     exp_wal_ser['Invoice'] = new_obj_url
#                 expert_id = ExpertWallet.objects.get(
#                     id__exact=exp_wal_ser['WalletId']).ExpertId_id
#                 dict1 = dict()
#                 expert_data = CuUser.objects.get(id__exact=expert_id)
#                 exp_p = DoctorDetails.objects.get(
#                     DoctorId_id__exact=expert_id).ProfilePhoto
#                 dict1.update({'expert_id': expert_id,
#                               'expert_name': expert_data.name,
#                               'expert_email': expert_data.email,
#                               'expert_approval': expert_data.approval,
#                               'expert_role': get_cu_user_type(expert_id),
#                               'expert_profile_photo': get_s3_signed_url_bykey(exp_p) if exp_p is not None else None,
#                               "expert_member_code": DoctorDetails.objects.get(DoctorId_id__exact=expert_id).MemberCode})
#                 exp_wal_ser['expert_details'] = dict1
#                 exp_list.append(exp_wal_ser)
#             # if 'page' in request.GET and request.GET['page'] != "":
#             response_data = {
#                 'total_items': total_items,
#                 'total_pages': paginator.num_pages,
#                 'items': exp_list
#             }
#             return Response(response_data)
#             # else:
#             #     return Response(exp_list)
#         else:
#             exp_wal = ExpertWalletTransactions.objects.filter(
#                 WalletId__ExpertId_id__exact=self.kwargs["expert_id"]).order_by('-TransactionDate')
#             if exp_wal.exists():
#                 exp_list = []
#                 if 'page' in request.GET and request.GET['page'] != "":

#                     # Get the page number from the request
#                     page_number = request.GET.get('page', 1)
#                     # Get the number of items per page from the request
#                     items_per_page = request.GET.get('per_page', 10)
#                     total_items = exp_wal.count()
#                     paginator = Paginator(exp_wal, items_per_page)
#                     print(paginator)
#                     if int(page_number) not in range(1, int(paginator.num_pages)+1):
#                         return HttpResponse("Not a valid page number", status=400)
#                     exp_wal = paginator.page(page_number)
#                 for x in exp_wal:
#                     exp_wal_ser = serialize_model(
#                         x, ExpertWalletTransactionsSerializer)
#                     exp_wal_ser['total_expert_fees'] = exp_wal_ser['TransactionAmount'] + \
#                         exp_wal_ser['CommissionAmount']
#                     exp_wal_ser['platform_percentage'] = (
#                         exp_wal_ser['CommissionAmount']/exp_wal_ser['total_expert_fees'])*100
#                     exp_wal_ser['platform_charge'] = exp_wal_ser['CommissionAmount']
#                     if exp_wal_ser['Invoice'] is not None:
#                         new_obj_url = get_s3_signed_url_bykey(
#                             exp_wal_ser['Invoice'])
#                         exp_wal_ser['Invoice'] = new_obj_url
#                     expert_id = ExpertWallet.objects.get(
#                         id__exact=exp_wal_ser['WalletId']).ExpertId_id
#                     dict1 = dict()
#                     dict2 = dict()
#                     expert_data = CuUser.objects.get(id__exact=expert_id)
#                     exp_p = DoctorDetails.objects.get(
#                         DoctorId_id__exact=expert_id).ProfilePhoto
#                     dict1.update({'expert_id': expert_id,
#                                   'expert_name': expert_data.name,
#                                   'expert_email': expert_data.email,
#                                   'expert_approval': expert_data.approval,
#                                   'expert_role': get_cu_user_type(expert_id),
#                                   'expert_profile_photo': get_s3_signed_url_bykey(exp_p) if exp_p is not None else None,
#                                   "expert_member_code": DoctorDetails.objects.get(DoctorId_id__exact=expert_id).MemberCode})
#                     exp_wal_ser['expert_details'] = dict1
#                     if exp_wal_ser['TransactionType'] == 2:
#                         app_id = AppointmentMgmt.objects.filter(
#                             By__exact=self.kwargs['expert_id'], EventType__exact="Expert_unpaid_appointment", EventDate__lte=exp_wal_ser['TransactionDate']).order_by('-EventDate')[0]
#                         dict2.update({'appointment_id': app_id.AppId_id,
#                                       'schedule_start_time': app_id.AppId.slot_id.schedule_start_time,
#                                       'schedule_end_time': app_id.AppId.slot_id.schedule_end_time,
#                                       'patient_name': app_id.AppId.patient.name,
#                                       'patient_query_id': PatientQueries.objects.get(ApptId_id=app_id.AppId_id).id,
#                                       'patient_query_ask_time': PatientQueries.objects.get(ApptId_id=app_id.AppId_id).QueryTime})
#                         exp_wal_ser['appointment_details'] = dict2
#                         exp_list.append(exp_wal_ser)
#                     else:
#                         exp_list.append(exp_wal_ser)
#                 # if 'page' in request.GET and request.GET['page'] != "":
#                 response_data = {
#                     'total_items': total_items,
#                     'total_pages': paginator.num_pages,
#                     'items': exp_list
#                 }
#                 return Response(response_data)
#                 # else:
#                 #     return Response(exp_list)
#             else:
#                 return Response("No transaction has been done yet.")

#     def put(self, request, *args, **kwargs):
#         instance_obj = ExpertWalletTransactions.objects.get(
#             id=self.kwargs['transaction_id'])
#         exp_pay = ExpertWalletTransactions.objects.all()
#         bal_amount = 0
#         tran_obj = exp_pay.order_by('-TransactionDate')[0]
#         clr_obj = exp_pay.order_by('-ClearedDate')[0]
#         if tran_obj.TransactionDate >= clr_obj.ClearedDate:
#             bal_amount = tran_obj.BalanceAmount
#         elif clr_obj.ClearedDate >= tran_obj.TransactionDate:
#             bal_amount = clr_obj.BalanceAmount
#         else:
#             bal_amount = instance_obj.BalanceAmount
#         if int(request.data['PaymentStatus']) == 1:
#             instance_obj.ClearedDate = timezone.now()
#             instance_obj.BalanceAmount = bal_amount
#             instance_obj.PaymentStatus = 1
#             if "invoice" in request.data:
#                 bb1 = handle_uploaded_file(request.data["invoice"])
#                 ff_name = bb1.split('/')[2]
#                 file_url_b = settings.PROJECT_ROOT + bb1

#                 file_urls_res_b = handle_s3_uploaded_file(ff_name, file_url_b)
#                 instance_obj.Invoice = file_urls_res_b
#             instance_obj.save()
#             return Response("successfully approved", status=200)
#         else:
#             return Response("Not a valid status", status=404)

class GetUpdateExpertPaymentView(views.APIView):
    serializer_class = ExpertWalletTransactionsSerializer
    queryset = ExpertWalletTransactions.objects.all()

    def get(self, request, *args, **kwargs):

        try:
            expert_id = kwargs.get('expert_id')
            transaction_id = kwargs.get('transaction_id')

            # Handle special cases first
            if expert_id == "pending":
                print("Handling pending payments")
                return self._handle_pending_payments(request)
            elif expert_id == "cleared":
                print("Handling cleared payments")
                return self._handle_cleared_payments(request)
            elif expert_id == "all":
                print("Handling all payments")
                return self._handle_all_payments(request)
            # elif transaction_id:
            #     print("Handling specific transaction")
            #     return self._handle_single_transaction(request, expert_id, transaction_id)
            else:
                print("Handling expert's transactions")
                return self._handle_expert_transactions(request, expert_id)

        except Exception as e:
            print(f"\n!!!!! ERROR !!!!!\n{str(e)}\n")
            import traceback
            traceback.print_exc()
            return Response(
                {"error": "Internal server error", "details": str(e)},
                status=500
            )

    def _handle_pending_payments(self, request):
        """Handle pending payments case"""
        exp_obj = ExpertWalletTransactions.objects.filter(
            PaymentStatus=0).order_by('-TransactionDate')
        return self._process_payment_list(request, exp_obj)

    def _handle_cleared_payments(self, request):
        """Handle cleared payments case"""
        exp_obj = ExpertWalletTransactions.objects.filter(
            PaymentStatus=1).order_by('-TransactionDate')
        return self._process_payment_list(request, exp_obj)

    def _handle_all_payments(self, request):
        """Handle all payments case"""
        exp_obj = ExpertWalletTransactions.objects.all().order_by('-TransactionDate')
        return self._process_payment_list(request, exp_obj)

    def _handle_expert_transactions(self, request, expert_id):
        """Handle specific expert's transactions"""
        exp_wal = ExpertWalletTransactions.objects.filter(
            WalletId__ExpertId_id=expert_id).order_by('-TransactionDate')

        if not exp_wal.exists():
            return Response({"message": "No transactions found for this expert"}, status=200)

        return self._process_payment_list(request, exp_wal, include_appointment_details=True)

    def _process_payment_list(self, request, queryset, include_appointment_details=False):
        """Common processing for all list-type responses"""
        # Apply filters
        queryset = self._apply_filters(request, queryset)

        # Pagination
        page_number = request.GET.get('page', 1)
        items_per_page = request.GET.get('per_page', 10)

        paginator = Paginator(queryset, items_per_page)

        try:
            page_obj = paginator.page(page_number)
        except EmptyPage:
            return Response({"error": "Invalid page number"}, status=400)

        exp_list = []
        for x in page_obj:
            exp_wal_ser = serialize_model(
                x, ExpertWalletTransactionsSerializer)

            # Calculate fees
            exp_wal_ser['total_expert_fees'] = exp_wal_ser['TransactionAmount'] + \
                exp_wal_ser['CommissionAmount']
            exp_wal_ser['platform_percentage'] = (
                exp_wal_ser['CommissionAmount']/exp_wal_ser['total_expert_fees'])*100 if exp_wal_ser['total_expert_fees'] else 0
            exp_wal_ser['platform_charge'] = exp_wal_ser['CommissionAmount']

            # Handle invoice URL
            if exp_wal_ser['Invoice']:
                exp_wal_ser['Invoice'] = get_s3_signed_url_bykey(
                    exp_wal_ser['Invoice'])

            # Add expert details
            exp_wal_ser['expert_details'] = self._get_expert_details(
                exp_wal_ser['WalletId'])

            # Add appointment details if needed
            if include_appointment_details and exp_wal_ser['TransactionType'] == 2:
                exp_wal_ser['appointment_details'] = self._get_appointment_details(
                    exp_wal_ser)

            exp_list.append(exp_wal_ser)

        return Response({
            'total_items': paginator.count,
            'total_pages': paginator.num_pages,
            'items': exp_list
        })

    def _apply_filters(self, request, queryset):
        """Apply filters from request parameters"""
        for param in request.GET:
            if param == "role":
                queryset = queryset.filter(
                    WalletId__ExpertId__groups__name=request.GET['role'])
            elif param == "start_date":
                start_date = timezone.make_aware(
                    parse_datetime(request.GET['start_date']),
                    timezone.get_current_timezone()
                )
                queryset = queryset.filter(TransactionDate__gte=start_date)
            elif param == "end_date":
                end_date = timezone.make_aware(
                    parse_datetime(request.GET['end_date']),
                    timezone.get_current_timezone()
                )
                queryset = queryset.filter(TransactionDate__lte=end_date)
            elif param == "search":
                queryset = queryset.filter(
                    WalletId__ExpertId__name__icontains=request.GET['search'])
        return queryset

    def _get_expert_details(self, wallet_id):
        """Get expert details for a wallet"""
        wallet = ExpertWallet.objects.get(id=wallet_id)
        expert_id = wallet.ExpertId_id
        expert_data = CuUser.objects.get(id=expert_id)

        try:
            doctor_details = DoctorDetails.objects.get(DoctorId_id=expert_id)
            profile_photo = get_s3_signed_url_bykey(
                doctor_details.ProfilePhoto) if doctor_details.ProfilePhoto else None
            member_code = doctor_details.MemberCode
        except DoctorDetails.DoesNotExist:
            profile_photo = None
            member_code = None

        return {
            'expert_id': expert_id,
            'expert_name': expert_data.name,
            'expert_email': expert_data.email,
            'expert_approval': expert_data.approval,
            'expert_role': get_cu_user_type(expert_id),
            'expert_profile_photo': profile_photo,
            'expert_member_code': member_code
        }

    def _get_appointment_details(self, transaction_data):
        """Get appointment details for a transaction"""
        try:
            app_id = AppointmentMgmt.objects.filter(
                By=transaction_data['WalletId__ExpertId_id'],
                EventType="Expert_unpaid_appointment",
                EventDate__lte=transaction_data['TransactionDate']
            ).order_by('-EventDate').first()

            if not app_id:
                return None

            patient_query = PatientQueries.objects.filter(
                ApptId_id=app_id.AppId_id).first()

            return {
                'appointment_id': app_id.AppId_id,
                'schedule_start_time': app_id.AppId.slot_id.schedule_start_time,
                'schedule_end_time': app_id.AppId.slot_id.schedule_end_time,
                'patient_name': app_id.AppId.patient.name,
                'patient_query_id': patient_query.id if patient_query else None,
                'patient_query_ask_time': patient_query.QueryTime if patient_query else None
            }
        except Exception as e:
            print(f"Error getting appointment details: {str(e)}")
            return None

    def put(self, request, *args, **kwargs):
        """Handle updating a transaction"""
        try:
            instance_obj = ExpertWalletTransactions.objects.get(
                id=self.kwargs['transaction_id'])

            if int(request.data.get('PaymentStatus', 0)) == 1:
                # Get latest balance
                latest_txn = ExpertWalletTransactions.objects.filter(
                    WalletId=instance_obj.WalletId
                ).order_by('-TransactionDate').first()

                instance_obj.ClearedDate = timezone.now()
                instance_obj.BalanceAmount = latest_txn.BalanceAmount if latest_txn else 0
                instance_obj.PaymentStatus = 1

                if "invoice" in request.data:
                    bb1 = handle_uploaded_file(request.data["invoice"])
                    ff_name = bb1.split('/')[2]
                    file_url_b = settings.PROJECT_ROOT + bb1
                    file_urls_res_b = handle_s3_uploaded_file(
                        ff_name, file_url_b)
                    instance_obj.Invoice = file_urls_res_b

                instance_obj.save()
                return Response({"status": "successfully approved"}, status=200)

            return Response({"error": "Invalid status"}, status=400)

        except ExpertWalletTransactions.DoesNotExist:
            return Response({"error": "Transaction not found"}, status=404)
        except Exception as e:
            return Response({"error": str(e)}, status=500)
# added RetrieveUpdateDeleteFAQs


class GetUpdateDeleteFAQsView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = FAQsSerializer
    permission_classes = [IsCuAdminPermission]
    queryset = FAQs.objects.all()
    lookup_field = 'id'

    def get(self, request, *args, **kwargs):
        a = self.retrieve(request, *args, **kwargs)
        return a

    def put(self, request, *args, **kwargs):
        a = self.partial_update(request, *args, **kwargs)
        return a

    def delete(self, request, *args, **kwargs):
        a = self.destroy(request, *args, **kwargs)
        return a

# added for edit/delete blog/podcast category


class GetUpdateBlogCategoryView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = BlogCategorySerializer
    permission_classes = [IsCuAdminPermission]
    queryset = BlogCategory.objects.all()
    lookup_field = 'id'

    def get(self, request, *args, **kwargs):
        a = self.retrieve(request, *args, **kwargs)
        return a

    def put(self, request, *args, **kwargs):
        a = self.partial_update(request, *args, **kwargs)
        return a

    def delete(self, request, *args, **kwargs):
        a = self.destroy(request, *args, **kwargs)
        return a


class GetUpdatePodcastCategoryView(generics.RetrieveUpdateDestroyAPIView):
    serializer_class = PodcastCategorySerializer
    permission_classes = [IsCuAdminPermission]
    queryset = PodcastCategory.objects.all()
    lookup_field = 'id'

    def get(self, request, *args, **kwargs):
        a = self.retrieve(request, *args, **kwargs)
        return a

    def put(self, request, *args, **kwargs):
        a = self.partial_update(request, *args, **kwargs)
        return a

    def delete(self, request, *args, **kwargs):
        a = self.destroy(request, *args, **kwargs)
        return a


class UpdateRankingBlogPodcastView(views.APIView):
    permission_classes = [IsCuAdminPermission]

    def put(self, request, *args, **kwargs):
        # type '0'
        if "blog_section" in request.data:
            blog_obj = ExpertBlogs.objects.filter(
                BlogRanking__exact=request.data['ranking'], BlogSectionVal__SectionName__exact=request.data['blog_section'])
            if blog_obj.exists():
                return Response("Already have this ranking blog", status=200)
            else:
                if request.data['blog_section'] == "Featured" and request.data['ranking'] in range(1, 4):
                    pass
                elif request.data['blog_section'] == "Top" and request.data['ranking'] in range(1, 8):
                    pass
                elif request.data['blog_section'] == "Trending" and request.data['ranking'] in range(1, 5):
                    pass
                elif request.data['blog_section'] == "Similar" and request.data['ranking'] in range(1, 5):
                    pass
                else:
                    return Response("Please select a valid rank")
                ins = ExpertBlogs.objects.get(id=self.kwargs['id'])
                ins.BlogRanking = request.data['ranking']
                ins.BlogSectionVal = BlogSection.objects.get(
                    SectionName__exact=request.data['blog_section'])
                ins.save()
        # type '1'
        elif "podcast_section" in request.data:
            pod_obj = Podcast.objects.filter(
                PodcastRanking__exact=request.data['ranking'], PodcastSectionVal__SectionName__exact=request.data['podcast_section'])
            if pod_obj.exists():
                return Response("Already have this ranking podcast", status=200)
            else:
                if request.data['podcast_section'] == "Featured" and request.data['ranking'] in range(1, 4):
                    pass
                elif request.data['podcast_section'] == "Top" and request.data['ranking'] in range(1, 7):
                    pass
                elif request.data['podcast_section'] == "Similar" and request.data['ranking'] in range(1, 5):
                    pass
                else:
                    return Response("Please select a valid rank")
                ins = Podcast.objects.get(id=self.kwargs['id'])
                ins.PodcastRanking = request.data['ranking']
                ins.PodcastSectionVal = PodcastSection.objects.get(
                    SectionName__exact=request.data['podcast_section'])
                ins.save()
        else:
            return Response("Not a valid status", status=404)
        return Response("successfully ranked", status=200)

    def delete(self, request, *args, **kwargs):
        if "blog_rank" in request.data and request.data['blog_rank'] == 0:

            ins = ExpertBlogs.objects.get(id=self.kwargs['id'])
            ins.BlogRanking = None
            ins.BlogSectionVal = None
            ins.save()

        # type '1'
        elif "podcast_rank" in request.data and request.data['podcast_rank'] == 0:

            ins = Podcast.objects.get(id=self.kwargs['id'])
            ins.PodcastRanking = None
            ins.PodcastSectionVal = None
            ins.save()

        else:
            return Response("Not a valid status", status=404)
        return Response("successfully deleted", status=200)

# expert commission update


class ExpertCommission(generics.UpdateAPIView):
    queryset = DoctorDetails.objects.all()
    permission_classes = [IsCuAdminPermission]
    lookup_field = 'DoctorId_id'
    serializer_class = DoctorDetailsSerializer

    def put(self, request, *args, **kwargs):
        a = self.partial_update(request, *args, **kwargs)
        return a

# -------------------expert ranking----------


class CreateTopAuthorView(generics.CreateAPIView):
    permission_classes = [IsCuAdminPermission]
    serializer_class = ExpertRankSerializer

    def post(self, request, *args, **kwargs):
        a = ExpertRank.objects.filter(rank__exact=request.data['rank'])
        b = ExpertRank.objects.filter(
            ExpertId_id__exact=request.data['ExpertId'])
        if a.exists():
            return Response("Rank already exists", status=200)
        elif b.exists():
            return Response("Expert has been already assigned the rank.", status=200)
        else:
            a = self.create(request, *args, **kwargs)
            return Response("Successfully created the rank", status=200)


class ListUpdateTopAuthorView(views.APIView):
    permission_classes = [IsCuAdminPermission]

    def put(self, request, *args, **kwargs):
        a = ExpertRank.objects.filter(rank__exact=request.data['rank'])
        if a.exists():
            return Response("Rank already exists", status=200)
        a = ExpertRank.objects.get(ExpertId_id__exact=self.kwargs['id'])
        if 'rank' in request.data:
            a.rank = request.data['rank']
        if 'ExpertId' in request.data:
            a.ExpertId = CuUser.objects.get(id=request.data['ExpertId'])
        a.save()
        return Response("Successfully updated", status=200)

    def get(self, request, *args, **kwargs):
        ins = []
        if self.kwargs['id'] == 'all':
            ins = ExpertRank.objects.all()
        else:
            ins = ExpertRank.objects.filter(
                ExpertId_id__exact=self.kwargs['id'])
        data = []
        for x in ins:
            l1 = dict()
            user_data = filter_user_data(x.ExpertId)
            if user_data['doctor_other_details']['ProfilePhoto'] is not None:
                user_data['doctor_other_details']['ProfilePhoto'] = get_s3_signed_url_bykey(
                    user_data['doctor_other_details']['ProfilePhoto'])
            l1.update({"expert_rank": x.rank, "expert_details": user_data})
            data.append(l1)
        return JsonResponse(data, safe=False)

    def delete(self, request, *args, **kwargs):
        a = ExpertRank.objects.get(ExpertId_id__exact=self.kwargs['id'])
        a = a.delete()
        return Response("Deleted the rank successfully", status=204)

# --------------------------update banner----------------------


class CreateBannerView(generics.CreateAPIView):
    permission_classes = [IsCuAdminPermission]
    serializer_class = UpdateBannerSerializer

    def post(self, request, *args, **kwargs):
        a = self.create(request, *args, **kwargs)
        return a


class ListUpdateBannerView(views.APIView):
    permission_classes = [IsCuAdminPermission]

    def put(self, request, *args, **kwargs):
        a = UpdateBanner.objects.filter(id__exact=self.kwargs['id'])
        if a.exists():
            pass
        else:
            return Response("No update banner exists for this id.", status=404)
        a = UpdateBanner.objects.get(id__exact=self.kwargs['id'])
        if 'updates' in request.data:
            a.updates = request.data['updates']
        if 'updateTime' in request.data:
            a.updateTime = request.data['updateTime']
        a.addedTime = timezone.now()
        a.save()
        return Response("Successfully updated", status=200)

    def get(self, request, *args, **kwargs):
        ins = []
        if self.kwargs['id'] == 'all':
            ins = UpdateBanner.objects.all().order_by('-addedTime')
        elif self.kwargs['id'] == 'latest':
            ins = UpdateBanner.objects.latest('-addedTime')
        else:
            ins = ExpertRank.objects.filter(id__exact=self.kwargs['id'])
        data = [serialize_model(t, UpdateBannerSerializer) for t in ins]
        return JsonResponse(data, safe=False)

    def delete(self, request, *args, **kwargs):
        a = UpdateBanner.objects.get(id__exact=self.kwargs['id'])
        a.delete()
        return Response("Deleted the updateBanner successfully", status=204)

# --------------------random expert selection by admin ----------------------------------------------------


class CreateRandomExpertView(generics.CreateAPIView):
    permission_classes = [IsCuAdminPermission]
    serializer_class = RandomExpertSerializer

    def post(self, request, *args, **kwargs):
        b = RandomExpert.objects.filter(
            ExpertId_id__exact=request.data['ExpertId'])
        if b.exists():
            return Response("Expert has been already selected.", status=200)
        else:
            a = self.create(request, *args, **kwargs)
            return Response("Successfully added the expert to ask ruchika expert_data", status=200)


class ListUpdateRandomExpertView(views.APIView):
    permission_classes = [IsCuAdminPermission]
    # def put(self, request, *args, **kwargs):
    #     a=RandomExpert.objects.get(ExpertId_id__exact=self.kwargs['id'])
    #     if 'ExpertId' in request.data:
    #         a.ExpertId=CuUser.objects.get(id=request.data['ExpertId'])
    #     a.save()
    #     return Response("Successfully updated", status=200)

    def get(self, request, *args, **kwargs):
        ins = []
        if self.kwargs['id'] == 'all':
            ins = RandomExpert.objects.all()
        else:
            ins = RandomExpert.objects.filter(
                ExpertId_id__exact=self.kwargs['id'])
        data = []
        for x in ins:
            l1 = dict()
            user_data = filter_user_data(x.ExpertId)
            if user_data['doctor_other_details']['ProfilePhoto'] is not None:
                user_data['doctor_other_details']['ProfilePhoto'] = get_s3_signed_url_bykey(
                    user_data['doctor_other_details']['ProfilePhoto'])
            l1.update({"expert_details": user_data})
            data.append(l1)
        return JsonResponse(data, safe=False)

    def delete(self, request, *args, **kwargs):
        a = RandomExpert.objects.get(ExpertId_id__exact=self.kwargs['id'])
        a = a.delete()
        return Response("Deleted the expert successfully from general selection to be shown in ask ruchika expert response.", status=204)


# --------------------------Common Topics----------------------
class CreateCommonTopicsView(generics.CreateAPIView):
    permission_classes = [IsCuAdminPermission]
    serializer_class = CommonTopicsSerializer

    def post(self, request, *args, **kwargs):
        a = self.create(request, *args, **kwargs)
        return Response("Common topic added successfully.", status=200)


class ListUpdateCommonTopicsView(views.APIView):
    permission_classes = [IsCuAdminPermission]

    def put(self, request, *args, **kwargs):
        a = CommonTopics.objects.filter(id__exact=self.kwargs['id'])
        if a.exists():
            pass
        else:
            return Response("No common topics exists for this id.", status=404)
        a = CommonTopics.objects.get(id__exact=self.kwargs['id'])
        if 'common_topics' in request.data:
            a.common_topics = request.data['common_topics']
        a.save()
        return Response("Successfully updated", status=200)

    def get(self, request, *args, **kwargs):
        ins = []
        if self.kwargs['id'] == 'all':
            ins = CommonTopics.objects.all().order_by('-id')
        else:
            ins = CommonTopics.objects.filter(id__exact=self.kwargs['id'])
        data = [serialize_model(t, CommonTopicsSerializer) for t in ins]
        return JsonResponse(data, safe=False)

    def delete(self, request, *args, **kwargs):
        a = CommonTopics.objects.get(id__exact=self.kwargs['id'])
        a.delete()
        return Response("Deleted the common topic successfully", status=204)

# --------------------testimonial selection by admin ----------------------------------------------------


class SelectContentView(generics.CreateAPIView):
    permission_classes = [IsCuAdminPermission]
    serializer_class = ContentSelectionSerializer

    def post(self, request, *args, **kwargs):
        b = ContentSelection.objects.filter(c_id__exact=int(
            request.data['c_id']), category__exact=request.data['category'])
        if b.exists():
            return Response("This content has been already selected.", status=200)
        else:
            if int(ContentSelection.objects.filter(category__exact=request.data['category']).count()) <= 10:
                a = self.create(request, *args, **kwargs)
                return Response("Successfully selected the content.", status=200)
            else:
                return Response("You can not select more than 10.", status=200)


class DeleteContentView(generics.DestroyAPIView):
    permission_classes = [IsCuAdminPermission]
    queryset = ContentSelection.objects.all()
    serializer_class = ContentSelectionSerializer
    lookup_field = "id"

    def delete(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.delete()
        return Response("Deleted successfully.", status=200)


class VideosLibraryView(
    generics.CreateAPIView,
    generics.DestroyAPIView,
):
    queryset = VideosLibrary.objects.all()
    serializer_class = VideosLibrarySerializer
    permission_classes = [IsCuAdminPermission]
    lookup_field = "id"

    def post(self, request, *args, **kwargs):
        try:
            is_url = request.data.get("isUrl")
            video_file = request.FILES.get("video_file")
            video_url = request.data.get("video_file")
            thumbnail_image = request.FILES.get("thumbnail_image")

            if str(is_url) == "False":
                if not video_file:
                    raise serializers.ValidationError(
                        {"video_file": "A video file must be uploaded when isUrl is False."}
                    )
                temp_url = handle_uploaded_file(request.FILES["video_file"])
                file_name = temp_url.split("/")[2]
                file_url = settings.PROJECT_ROOT + temp_url
                s3_url = handle_s3_uploaded_file(
                    file_name, file_url)  # Upload to S3
                request.data["video_file"] = s3_url  # Use S3 URL for videoUrl

            elif str(is_url) == "True":
                if not video_url:
                    raise serializers.ValidationError(
                        {"video_file": "A valid URL must be provided when isUrl is True."}
                    )
                request.data["video_file"] = video_url

            else:
                raise serializers.ValidationError(
                    {"isUrl": "isUrl must be either 'True' or 'False'."}
                )

            if thumbnail_image:
                temp_thumbnail_url = handle_uploaded_file(
                    request.FILES["thumbnail_image"])
                thumbnail_name = temp_thumbnail_url.split("/")[2]
                thumbnail_url = settings.PROJECT_ROOT + temp_thumbnail_url
                thumbnail_s3_url = handle_s3_uploaded_file(
                    thumbnail_name, thumbnail_url)
                request.data["thumbnail_image"] = thumbnail_s3_url

            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            serializer.save()

            return Response(
                {"isSuccess": True, "message": "Video uploaded successfully"},
                status=status.HTTP_200_OK,
            )

        except serializers.ValidationError as e:
            return Response(
                {"isSuccess": False, "message": "Validation Error", "details": e.detail},
                status=status.HTTP_400_BAD_REQUEST,
            )

        except Exception as e:
            print("exception", e)
            return Response(
                {"isSuccess": False, "message": "An error occurred",
                    "details": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )

    def delete(self, request, *args, **kwargs):
        try:
            if "id" in kwargs:
                response = self.destroy(request, *args, **kwargs)

            return Response(
                {"isSuccess": True, "message": "Item deleted successfully"},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"isSuccess": False, "message": "An error occurred",
                    "details": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class UpdateVideosLibraryView(
    generics.UpdateAPIView,
):
    queryset = VideosLibrary.objects.all()
    serializer_class = VideosLibrarySerializer
    permission_classes = [IsCuAdminPermission]
    lookup_field = "id"

    def update(self, request, *args, **kwargs):
        try:
            partial = kwargs.pop('partial', False)  # Handle partial updates
            instance = self.get_object()

            updated_data = request.data.copy()

            # Extracting data from the request
            is_url = request.data.get("isUrl", instance.isUrl)
            video_file = request.FILES.get("video_file")
            video_url = request.data.get("video_file", instance.video_file)
            thumbnail_image = request.FILES.get("thumbnail_image")

            # Validate and process video file or URL based on `isUrl`
            if str(is_url) == "False":
                if not video_file and not partial:
                    raise serializers.ValidationError(
                        {"video_file": "A video file must be uploaded when isUrl is False."}
                    )
                if video_file:
                    temp_url = handle_uploaded_file(video_file)
                    file_name = temp_url.split("/")[2]
                    file_url = settings.PROJECT_ROOT + temp_url
                    s3_url = handle_s3_uploaded_file(file_name, file_url)
                    updated_data["video_file"] = s3_url

            elif str(is_url) == "True":
                if not video_url and not partial:
                    raise serializers.ValidationError(
                        {"video_file": "A valid URL must be provided when isUrl is True."}
                    )
                updated_data["video_file"] = video_url

            else:
                raise serializers.ValidationError(
                    {"isUrl": "isUrl must be either 'True' or 'False'."}
                )

            # Process and upload the thumbnail image if provided
            if thumbnail_image:
                temp_thumbnail_url = handle_uploaded_file(thumbnail_image)
                thumbnail_name = temp_thumbnail_url.split("/")[2]
                thumbnail_url = settings.PROJECT_ROOT + temp_thumbnail_url
                thumbnail_s3_url = handle_s3_uploaded_file(
                    thumbnail_name, thumbnail_url)
                updated_data["thumbnail_image"] = thumbnail_s3_url

            # Validate and save the serializer
            serializer = self.get_serializer(
                instance, data=updated_data, partial=partial)
            serializer.is_valid(raise_exception=True)
            serializer.save()

            return generate_response(
                success=True,
                message="Video updated successfully.",
                status_code=status.HTTP_200_OK,
                data=serializer.data,
            )

        except serializers.ValidationError as e:
            return generate_response(
                success=False,
                message="Validation Error",
                status_code=status.HTTP_400_BAD_REQUEST,
                data={"details": e.detail},
            )

        except Exception as e:
            return generate_response(
                success=False,
                message="An error occurred while updating the Video.",
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                data={"details": str(e)},
            )


class SubscribedUserGetView(generics.ListAPIView, generics.RetrieveAPIView):
    queryset = UserSubscription.objects.all()
    serializer_class = UserSubscriptionSerializer
    permission_classes = [IsCuAdminPermission]
    lookup_field = 'id'

    def get(self, request, *args, **kwargs):
        try:
            if 'id' in kwargs:
                response = self.retrieve(request, *args, **kwargs)
                return generate_response(
                    success=True,
                    data=response.data,
                    status_code=status.HTTP_200_OK,
                )
            else:
                user_status = request.GET.get('status', None)
                queryset = self.get_queryset()

                if user_status is not None:
                    if user_status.lower() == 'subscribed':
                        queryset = queryset.filter(is_subscribed=True)
                    elif user_status.lower() == 'unsubscribed':
                        queryset = queryset.filter(is_subscribed=False)
                    else:
                        return generate_response(
                            success=False,
                            message="Invalid status. Use 'subscribed' or 'unsubscribed'.",
                            status_code=status.HTTP_400_BAD_REQUEST,
                        )

                queryset = queryset.order_by('-subscribed_at')
                serializer = self.serializer_class(queryset, many=True)
                response_data = serializer.data

                page = int(request.GET.get('page', 1))
                per_page = int(request.GET.get('per_page', 10))
                paginator = Paginator(response_data, per_page)

                try:
                    paginated_subscribers = paginator.page(page)

                except EmptyPage:
                    return generate_response(
                        success=False,
                        message="Page not found.",
                        status_code=status.HTTP_400_BAD_REQUEST,
                    )

                paginated_response = {
                    "isSuccess": True,
                    "total_items": paginator.count,
                    "total_pages": paginator.num_pages,
                    "current_page": page,
                    "per_page": per_page,
                    "items": paginated_subscribers.object_list,
                }

                return Response(
                    paginated_response,
                    status=status.HTTP_200_OK,
                )

        except serializers.ValidationError as e:
            error_messages = "; ".join(
                f"{field}: {', '.join(errors)}" for field, errors in e.detail.items()
            )
            return generate_response(
                success=False,
                message=error_messages,
                status_code=status.HTTP_400_BAD_REQUEST,
            )
