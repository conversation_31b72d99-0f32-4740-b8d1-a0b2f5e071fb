import os
import uuid
import requests
import time
from django.conf import settings
import uuid
from decimal import Decimal


class AirwallexService:
    def __init__(self):
        self.api_key = os.getenv("AIRWALLEX_API_KEY")
        self.client_id = os.getenv("AIRWALLEX_CLIENT_ID")
        # Use settings to toggle between demo/prod
        self.base_url = "https://api-demo.airwallex.com"

    def _authenticate(self):
        """Get authentication token"""
        url = f"{self.base_url}/api/v1/authentication/login"
        headers = {
            "Content-Type": "application/json",
            "x-api-key": self.api_key,
            "x-client-id": self.client_id,
        }
        response = requests.post(url, headers=headers, json={})
        response.raise_for_status()
        return response.json()['token']

    def _make_request(self, method, endpoint, data=None):
        """Generic request method"""
        token = self._authenticate()
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
        }
        url = f"{self.base_url}{endpoint}"
        response = requests.request(method, url, headers=headers, json=data)
        response.raise_for_status()
        return response.json()

    def create_payment_link(self, amount, currency, metadata, customer_email, title, redirect_url, cancel_url):
        endpoint = "/api/v1/pa/payment_links/create"
        payload = {
            "request_id": str(uuid.uuid4()),
            "amount": amount,
            "currency": currency,
            "customer": {"email": customer_email},
            "reusable": False,
            "title": title,
            "metadata": metadata,
            "return_url": {
            "return_url": redirect_url,
            "return_url_trigger": "always"  # Critical parameter
            },
            "cancel_url": cancel_url,
            "auto_redirect": True,  # Enable automatic redirect
            "auto_redirect_delay": 3,  # Redirect after 3 seconds
            "return_url": redirect_url,
            "cancel_url": cancel_url,
       
        }
        print(f"Creating payment link with payload: {payload}")
        response = self._make_request("POST", endpoint, payload)
        print(f"Airwallex response: {response}")
        return response


    def get_payment_intent(self, payment_intent_id, retries=3):
        """Get payment intent with retry logic"""
        endpoint = f"/api/v1/pa/payment_intents/{payment_intent_id}"

        for attempt in range(retries):
            try:
                response = self._make_request("GET", endpoint)
                return response
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 404 and attempt < retries - 1:
                    time.sleep(1)  # Wait before retrying
                    continue
                raise

    def get_refund(self, refund_id, retries=3):
        """Fetch refund details from Airwallex by refund ID with optional retry"""
        endpoint = f"/api/v1/pa/refunds/{refund_id}"

        for attempt in range(retries):
            try:
                response = self._make_request("GET", endpoint)
                print(f"Airwallex refund response: {response}")
                return response
            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 404 and attempt < retries - 1:
                    time.sleep(1)
                    continue
                raise Exception(f"Failed to fetch refund {refund_id}: {str(e)}")

    def get_all_refunds(self):
        endpoint = "/api/v1/pa/refunds"
        response = self._make_request("GET", endpoint)
        
        # Return just the list of refund items
        return response.get("items", [])


    def create_refund(self, payment_intent_id=None, payment_attempt_id=None, 
                    amount=None, currency=None, reason=None):
        endpoint = "/api/v1/pa/refunds/create"
        
        if not payment_intent_id and not payment_attempt_id:
            raise ValueError("Either payment_intent_id or payment_attempt_id must be provided")
        
        payload = {
            "request_id": str(uuid.uuid4()),  # Required unique ID
            "reason": (reason or "Appointment cancellation")[:128],  # Truncate if needed
            "metadata": {
                "system_initiated": True,
                "refund_type": "partial" if amount else "full"
            }
        }
        
        # Add amount if specified (for partial refunds)
        if amount is not None:
            if isinstance(amount, Decimal):
                amount = float(amount.quantize(Decimal('0.00')))
            payload["amount"] = amount
        
        # Add currency if provided
        if currency:
            payload["currency"] = currency
        
        # Add payment reference
        if payment_attempt_id:
            payload["payment_attempt_id"] = payment_attempt_id
        else:
            payload["payment_intent_id"] = payment_intent_id
        
        try:
            response = self._make_request("POST", endpoint, payload)
            print("response of the refund",response)
            return response
            # return self._parse_refund_response(response)
        except Exception as e:
            error_msg = f"Refund creation failed: {str(e)}"
            if hasattr(e, 'response') and e.response:
                error_msg += f" | Response: {e.response.text}"
            raise Exception(error_msg)
    
    def _parse_refund_response(self, response):
        """Standardize refund response format"""
        return {
            "id": response["id"],
            "status": response["status"],
            "amount": Decimal(response["amount"]),
            "currency": response["currency"],
            "payment_intent_id": response.get("payment_intent_id"),
            "payment_attempt_id": response.get("payment_attempt_id"),
            "created_at": response["created_at"],
            "reason": response["reason"],
            "metadata": response.get("metadata", {})
        }

    def get_payments_by_customer(self, customer_email, limit=100):
        """Fetch all payments for a specific customer"""
        endpoint = "/api/v1/pa/payment_intents"
        return self._make_request("GET", endpoint)


