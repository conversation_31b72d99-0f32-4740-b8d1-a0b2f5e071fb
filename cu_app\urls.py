from django.urls import path, include, re_path
from .views.misc_views import *
from .views.doctor_views import *
from .views.patient_views import *
from .views.scheduler_views import *
from .views.payments_views import *
from .views.aws_views import *
from .views.sendgridtest import *

# from .views.admin_views import *
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
    TokenRefreshView,
)

urlpatterns = [
    path('download-invoice/<int:id>/',
         DownloadInvoiceView.as_view(), name='download-invoice'),
    path('token/', TokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('token/refresh/', TokenRefreshView.as_view(), name='token_refresh'),
    path('register/', UserRegisterAPIView.as_view(), name="user-create"),
    path('login/', UserLogin.as_view(), name="user-login"),
    path('update_user_profile/<str:email>/',
         UserUpdate.as_view(), name="doctor-update"),
    path('get_doctors_by_expertise/', GetDoctorsByExpertise.as_view(),
         name="get_doctors_by_expertise_path"),
    path('get_user_data/<str:email>/', GetUser.as_view(), name="get_user_path"),
    path('expertise/', AddExpertise.as_view(), name="add_expertise_path"),
    path('prescription/', PrescriptionView.as_view(), name="prescription_path"),
    path('irprescription/', IRPrescriptionView.as_view(),
         name="irprescription_path"),
    path('get_prescription/<str:id>/', GetPrescriptionView.as_view(),
         name="get_prescription_path"),
    path('get_irprescription/<str:id>/', GetIRPrescriptionView.as_view(),
         name="get_irprescription_path"),
    path('user_test/<str:id>/', UserTest.as_view()),
    path('test_json_req/', TestJson.as_view()),
    path('test_json_req_ret/<str:id>/', TestJsonRet.as_view()),
    path('pricing/', PricingView.as_view(), name="pricing_path"),
    path('password_reset_link/<str:email>/',
         PasswordResetLinkView.as_view(), name="password_reset_link_path"),
    path('verify_password/<str:email>/',
         VerifyPasswordView.as_view(), name="verify_password_path"),
    path('patient_tickets/<str:email>/<str:status>/',
         PatientTicketsView.as_view(), name="tickets_path"),
    path('all_tickets/<str:user_role>/<str:status>/',
         AllTicketsView.as_view(), name="all_tickets_path"),
    path('ticket_details/<str:ticket_id>/',
         TicketDetailsView.as_view(), name="ticket_details_path"),
    path('get_fees/', GetFeesView.as_view(), name="get_fees_path"),


    # scheduler
    # for patient and doctor
    path('get-user-appointments/<int:id>/', GetUserAppointments.as_view(),
         name="get-patient-appointments-path"),
    path('get-day-slots/', GetDaySlots.as_view(), name="get-day-slots-path"),
    # path('update-slot/<str:slotId>/', UpdateSlot.as_view(), name="update-slot-path"),
    path('patient-query/', PatientQuery.as_view(), name="patient-query-path"),
    path('patient-story/<str:user_id>/<str:status>/',
         PatientStory.as_view(), name="patient-story-path"),
    path('update-patient-story/<int:id>/',
         UpdatePatientStoryView.as_view(), name="update-patient-story-path"),
    path('appoint-consent/', AppointmentConsentView.as_view(),
         name="appoint-consent-path"),
    path('cancer-types-main/', CancerTypesMainView.as_view(),
         name='cancer-types-main_path'),
    path('cancer-types-category/', CancerTypesCatView.as_view(),
         name='cancer-types-cat_path'),
    path('cancer-types-level/<int:tumour_level>/',
         CancerTypesLevelView.as_view(), name='cancer-types-level_path'),
    path('appointments/<int:id>/', AppointmentsView.as_view(),
         name='appointments_path'),
    # path('appointments_re/<int:id>/<int:s_id>/', AppointmentsReschedueledView.as_view(), name='appointments_path'),

    # for doctor
    path('create-slot/', CreateSlot.as_view(), name="create-slot-path"),
    path('edit-delete-slot/<int:id>/', EditDeleteSlot.as_view(), name='edit-delete-slot'),
    path('get-doctor-appointments-date-filter/<int:doctor_id>/<str:start_date>/<str:end_date>/',
         GetDoctorAppointmentsDateFilter.as_view(), name="get-doctor-appointments-date-filter-path"),
    path('get-doctor-freeslots-date-filter/<int:doctor_id>/<str:start_date>/<str:end_date>/',
         GetDoctorFreeSlotsDateFilter.as_view(), name="get-doctor-freeslots-date-filter-path"),
    path('get-doctor-appointments-day-filter/<int:doctor_id>/<str:date_value>/',
         GetDoctorAppointmentsDayFilter.as_view(), name="get-doctor-appointments-day-filter-path"),
    path('get-an-appointment_details/<int:app_id>/',
         GetAnAppointmentDetails.as_view(), name="get-an-appointment_details-path"),
    path('reply-patient-query/', ReplyPatientQuery.as_view(),
         name="reply-patient-query-path"),
    path('get-patient-queries/<int:id>/', GetPatientQueries.as_view(),
         name="get-patient-queries-path"),
    path('expert-blog/<str:expert_id>/<str:blog_status>/',
         ExpertBlogView.as_view(), name="add-blog-path"),
    path('update-blogs/<int:id>/', UpdateBlogsView.as_view(),
         name="update-expert-blogs-path"),
    path('upload-intro-video/<int:DoctorId>/',
         UploadIntroVideoView.as_view(), name='upload-intro-video_path'),
    path('submit-intro-video/<str:DoctorId>/',
         SubmitIntroVideoView.as_view(), name='submit-intro-video_path'),
    path('shotstack-test/', ShotStackTest.as_view(), name='shotstack-test_path'),
    # path('reduce-noise/', ReduceNoiseTest.as_view(), name='reduce-noise_path'),
    path('expert-feedback/<str:expert_id>/<str:status>/',
         ExpertFeedbackView.as_view(), name='expert-feedback_path'),
    path('update-expert-feedback/<int:id>/', UpdateExpertFeedbackView.as_view(),
         name='update-expert-feedback_path'),  # my opinions
    path('expert-review/', ExpertReviewView.as_view(), name='expert-review_path'),
    path('submit-expert-review/<str:ReviewCode>/',
         SubmitReviewView.as_view(), name='submit-expert-review_path'),
    path('doctor-consent/', DoctorConsentView.as_view(),
         name='doctor-consent_path'),
    path('retrieve-doctor-consent/<int:DoctorId>/',
         RetrieveDoctorConsentView.as_view(), name='retrieve-doctor-consent_path'),
    path('podcast-request/<str:ExpertId>/',
         PodcastRequestView.as_view(), name='podcast-request_path'),

    path('user-verify-email-request/', UserVerifyEmailReq.as_view(),
         name="user-verify-email-request-path"),
    path('user-verify-email/', UserVerifyEmail.as_view(),
         name="user-verify-email-path"),
    path('user-verify-otp/', UserVerifyOTP.as_view(),
         name="user-verify-otp-path"),
    path('user-resend-otp/', UserResendOTP.as_view(),
         name="user-resend-otp-path"),

    # phone number change otp
    path("send-phone-update-otp/", SendPhoneUpdateOTPAPIView.as_view(),
         name="send_phone_update_otp"),
    path("verify-phone-update-otp/", VerifyPhoneUpdateOTPAPIView.as_view(),
         name="verify_phone_update_otp"),


    path('social-user/', SocialUser.as_view(), name="social-user-path"),

    # meeting apis
    path('start-session/<str:SessionId>/<str:role>/',
         StartMeetingSession.as_view(), name="start-session-path"),
    path('allow-join-session/<str:SessionId>/<str:role>/<int:approval>/',
         AllowJoinMeetingSession.as_view(), name="allow-join-session-path"),
    path('join-session/<str:SessionId>/<str:role>/',
         JoinMeetingSession.as_view(), name="join-session-path"),
    path('leave-session/<str:SessionId>/<str:role>/',
         LeaveMeetingSession.as_view(), name="leave-session-path"),
    path('end-session/<str:SessionId>/<str:role>/',
         EndMeetingSession.as_view(), name="end-session-path"),
    path('patient-request-session/<str:SessionId>/<str:role>/',
         PatientRequestSession.as_view(), name="patient-request-path"),
    path('check-patient-approval/<str:SessionId>/<str:role>/',
         CheckPatientSessionApproval.as_view(), name="patient-request-path"),

    path('validate-meeting-session/', ValidateMeetingSessionView.as_view(), name='validate-meeting-session'),


    # medical records
    path('medical-record-upload/', MedicalRecUpload.as_view(),
         name="medical-record-upload-path"),
    path('send-otp/', SendOtp.as_view(), name="send-otp-path"),
    path('get_medical_recordslist/<str:email>/',
         GetMedicalRecordsList.as_view(), name="get_medical_recordslist_path"),
    path('get_medical_record/<str:id>/', GetMedicalRecord.as_view(),
         name="get_medical_record_path"),
    path('testmail/', SendGridTest.as_view(), name="testmail_path"),
    path('creowiztestmail/', CreowizTestMail.as_view(),
         name="creowiztestmail_path"),
    path('s3test/', S3TestView.as_view(), name="s3test_path"),
    path('download_doctor_prescription/<str:id>/',
         DoctorPrescriptionDownload.as_view(), name="download_doctor_prescription_path"),
    path('download_ir_prescription/<str:id>/',
         IRPrescriptionDownload.as_view(), name="download_ir_prescription_path"),
    path('download_user_appointments/<int:id>/<str:start_date>/<str:end_date>/',
         UserAppointmentsDownload.as_view(), name="new_xlsx"),

    # added
    path('download_medical_record/<str:id>/',
         MedicalRecordDownload.as_view(), name="download_medical_record"),
    path('expert_profile_update/<str:email>/',
         ExpertDeactivateView.as_view(), name="expert_profile_update_path"),
    path('get_report_type/<str:id>/', GetReportTypeView.as_view(),
         name="get_report_type_path"),
    path('get_app_content_type/<str:id>/',
         GetAppContentTypeView.as_view(), name="get_content_type_path"),
    path('delete_user_profile/<str:email>/',
         UserDeleteAPIView.as_view(), name="delete_user_profile_path"),
    path('patient-refund/<str:patient_id>/',
         GetPatientRefundView.as_view(), name='patient-refund_path'),
    path('get_expert_homepage/<int:DoctorId_id>/',
         ExpertHomepageView.as_view(), name='expert_homepageview_path'),
    path('add_view/<str:id>/', BlogPodcastViewsUpdateView.as_view(),
         name='add_view_path'),
    path('get-create-faq/', GetCreateFAQsView.as_view(),
         name="get-create-faq-path"),
    path('get-create-blog-category/', ListCreateBlogCategoryView.as_view(),
         name="get-create-blog-category-path"),
    path('get-create-podcast-category/', ListCreatePodcastCategoryView.as_view(),
         name="get-create-podcast-category-path"),
    # added

    # download invoice





    # payment
    path('payment-processing/', PaymentProcessingView.as_view(),
         name='payment_processing'),

    path('payment_link/', PaymentLinkView.as_view(), name="payment_link_path"),
    # path('payment_link/', CreatePLinkView.as_view(), name="payment_link_path"),
    path('get_invoice/', GetInvoiceView.as_view(), name="get_invoice_path"),
    path('get_airwallex_payments/', PaymentsViewFromAirwallex.as_view(),
         name="get_payments_path"),
    path('get_payments/<str:id>/', PatientPaymentListView.as_view(),
         name="get_payments_path_list"),
    # path('get_payments/<str:id>/', GetPaymentsView.as_view(),
    #      name="get_payments_path"),
    path('total_revenue/<str:year>/<str:month>/',
         TotalRevenue.as_view(), name="total_revenue_path"),
    path('expert_wallet/<str:expert_id>/',
         GetUpdateExpertWalletView.as_view(), name="expert_wallet_path"),
    path('expert_chart/<str:expert_id>/',
         ExpertWalletChart.as_view(), name="expert_chart_path"),
    # added
    # path('get_doctor_payments/<int:id>/',GetDoctorPaymentsView.as_view(),name="get_doctor_payments_path"),
    # path('patient_refund/<int:AppointmentId_id>/',CreateRefundsView.as_view(),name="create_refund_path"),
    # added

    # public apis
    path('get-doctor-details/<str:id>/', GetDoctorDetailsView.as_view(),
         name="get-doctor-details-path"),
    path('get-testimonials/<str:id>/', GetTestimonialsView.as_view(),
         name="get-testimonials-path"),
    path('get-doctor-feedbacks/', GetDoctorFeedbacks.as_view(),
         name="get-doctor-feedbacks-path"),  # my opinions
    path('get-expertise/', GetExpertiseView.as_view(),
         name="get-expertise-path"),  # my opinions
    path('get-expert-blogs/<str:id>/', GetExpertBlogView.as_view(),
         name="get-expert-blogs-path"),
    path('get-expert-podcast/<str:id>/', GetExpertPodcastView.as_view(),
         name="get-expert-podcast-path"),
    path('get-expert-reviews/<str:id>/', GetExpertReviewView.as_view(),
         name="get-expert-reviews-path"),
    path('get-experts-by-filter/', GetExpertsByFilterView.as_view(),
         name="get-experts-by-filter-path"),
    path('expert_profile_reactivation/<str:email>/', ExpertReactivateView.as_view(),
         name="expert_profile_reactivate_path"),  # added
    path('get_app_policy_content_type/', GetAppPolicyContentTypeView.as_view(),
         name="get_app_policy_content_type_path"),  # added
    path('get-single-blog/<str:id>/', GetSingleBlogView.as_view(),
         name="get_single_blog_path"),
    path('get-single-podcast/<str:id>/', GetSinglePodcastView.as_view(),
         name="get_single_podcast_path"),
    path('get-blog-podcast-section/<str:type>/<str:section>/',
         GetBlogsPodcastSectionView.as_view(), name="get_blog_podcast_section_path"),
    path('get-podcast-section/<str:id>/', GetPodcastSectionView.as_view(),
         name="get_podcast_section_path"),
    path('get-blog-section/<str:id>/', GetBlogSectionView.as_view(),
         name="get_blog_section_path"),
    path('get-blog-category/<str:id>/', GetBlogCategoryView.as_view(),
         name="get_blog_category_path"),
    path('get-podcast-category/<str:id>/',
         GetPodcastCategoryView.as_view(), name="get_podcast_category_path"),
    path('get-top-authors/<str:id>/',
         GetTopAuthorView.as_view(), name="get_top_author_path"),
    path('get-random-expert/', GetRandomExpertView.as_view(),
         name="get_random_expert_path"),
    path('get-banner/', GetBanner.as_view(), name="get_banner_path"),
    path('get-common-topics/', GetCommonTopicsView.as_view(),
         name="get_common_topics_path"),
    path('get-select-content/<str:content_type>/',
         GetSelectContentView.as_view(), name="get_select_content-path"),
    # notifications
    path('get-user-notifications/<int:user_id>/',
         GetUserNotifications.as_view(), name="get-user-notification-path"),
    path('read-notifications/<int:id>/',
         ReadNotifications.as_view(), name="read-notification-path"),

    # zoho import data
    path('doctor-data-in-zoho-account/', DoctorDataToZohoAccount.as_view(),
         name="doctor-data-in-zoho-account/"),
    path('patient-data-in-zoho-Contact/', PatientDataToZohoContact.as_view(),
         name="patient-data-in-zoho-contact/"),
    # Videos library

    path(
        "get-videos-library/<str:id>/", VideosLibraryGetView.as_view(), name="get-videos-library"
    ),
    path(
        "get-videos-library/", VideosLibraryGetView.as_view(), name="get-videos-library"
    ),
    path("bank-details/", ExpertBankDetailsView.as_view(), name="bank-details"),
    path("bank-details-params/", GetBankParamsView.as_view(),
         name="get-bank-details-params"),
    path("bank-details/<str:id>/", ExpertBankDetailsView.as_view(),
         name="single-bank-details"),
    path("user/bank-details/<str:user_id>/",
         UserBankAccountsView.as_view(), name="user-bank-details"),

    path("user/update-bank-details/<str:id>/",
         UpdateExpertBankDetailsView.as_view(), name="update-bank-details"),

    path("subscribe/", UserSubscriptionView.as_view(), name="user-subscribe"),
    path("unsubscribe/<str:email>/", UnsubscribeUserView.as_view(),
         name="user-unsubscribe"),
    path("specialties/", MedicalSpecialtyListCreateView.as_view(),
         name="specialties-list-create"),
    path("specialties/<int:pk>/", MedicalSpecialtyRetrieveUpdateDeleteView.as_view(),
         name="specialty-retrieve-update-delete"),

    # test open ai search
    path('search_doctors/', DoctorSearchView.as_view(), name='search_doctors'),
    path('precompute-embeddings/', PrecomputeEmbeddingsView.as_view(),
         name='precompute-embeddings'),
    path('payment_events/', WebhookView.as_view(),
         name="airWallex_events_path"),
]

