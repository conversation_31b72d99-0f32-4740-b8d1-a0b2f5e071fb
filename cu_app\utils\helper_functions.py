from django.contrib.auth import get_user_model
from cu_app.models.doctor_models import DoctorDetails
from cu_app.models.patient_models import SchedulerSlots
from cu_app.models.misc_models import Pricing
from django.forms.models import model_to_dict
from django.utils import timezone


def get_cu_user_type(a):
    try:
        b = get_user_model().objects.get(id=a)
        u_groups = b.groups.all()
        if len(u_groups) >= 1:

            return u_groups[0].name
        else:
            return "no valid user found"
    except Exception as e:
        return str(e)


def calculate_fees(doctor_id, slot_id,amount=None):
    doctor_details = DoctorDetails.objects.get(DoctorId=doctor_id)
    pricing = Pricing.objects.last()

    # print("doctor_details", model_to_dict(doctor_details))

    # Defensive defaults
    doctor_fees = amount if amount is not None else (doctor_details.ConsultationFees or 0)
    platform_charges = pricing.PlatformCharges or 0
    transaction_percentage = pricing.TransactionCharges or 0

    slot = SchedulerSlots.objects.get(doctor_id=doctor_id, id=slot_id)
    duration = (slot.schedule_end_time -
                slot.schedule_start_time).total_seconds() / 1800

    base_charges = duration * doctor_fees + platform_charges
    transaction_charges = base_charges * transaction_percentage / 100
    final_charge = base_charges + transaction_charges

    return {
        "doctor_fees": doctor_fees,
        "platform_charges": platform_charges,
        "transaction_charges": transaction_charges,
        "final_charge": final_charge,
    }


def get_appointment_details(slot_id, doctor_id):
    """
    Returns appointment timing (formatted) and expert role.
    """
    try:
        slot_data = SchedulerSlots.objects.get(id=slot_id)

        # Localize the slot start and end times
        start_time_local = timezone.localtime(slot_data.schedule_start_time, timezone.get_current_timezone())
        end_time_local = timezone.localtime(slot_data.schedule_end_time, timezone.get_current_timezone())

        # Format date and time
        appointment_date = start_time_local.date()
        start_time_str = f"{start_time_local.time().hour:02}:{start_time_local.time().minute:02}"
        end_time_str = f"{end_time_local.time().hour:02}:{end_time_local.time().minute:02}"
        appointment_timings = f"{appointment_date} {start_time_str}-{end_time_str}"

        # Get expert rolename
        expert_role = get_cu_user_type(doctor_id)
        print("appointmnet timings",appointment_timings)

        return {
            "appointment_timings": appointment_timings,
            "expert_role": expert_role
        }

    except SchedulerSlots.DoesNotExist:
        return {
            "appointment_timings": None,
            "expert_role": None
        }
    