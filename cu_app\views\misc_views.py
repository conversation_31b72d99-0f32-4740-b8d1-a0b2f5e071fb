from __future__ import print_function
from django.shortcuts import render
from django.contrib.auth.hashers import check_password, make_password
from rest_framework import generics, status, views
from django.middleware.csrf import get_token
from django.http import *
from cu_project.common.utils import generate_response
from cu_app.utils.helper_functions import *
# from cu_app.services.email_services import EmailService
from cu_app.services.email_services import EmailService
from ..serializers import *
from datetime import *
from rest_framework.response import Response
import json
from django.contrib.auth.models import Group, Permission
from django.views import View
from django.contrib.contenttypes.models import ContentType
from django.contrib.auth import authenticate, login, logout, get_user_model
from ..models import *
import os
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
import datetime
import os.path
import re
from datetime import datetime, date, timedelta
from django.conf import settings
from django.core.files.storage import FileSystemStorage
from ..forms import *
from ..cu_library import *
from rest_framework.exceptions import NotFound
# aws sns
import logging
import time
import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv
from rest_framework.parsers import JSONParser
import json
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework.decorators import permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework import permissions
import sendgrid
from sendgrid.helpers.mail import *
from python_http_client.exceptions import HTTPError
import random
from django.conf import settings


# from pytz import timezone
import base64
from Crypto.Cipher import AES
from Crypto.Random import get_random_bytes
from Crypto.Util.Padding import pad
from firebase_admin.messaging import (
    Message,
    Notification,
    WebpushConfig,
    WebpushFCMOptions,
)
from fcm_django.models import FCMDevice
import requests
from reportlab.pdfgen import canvas
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import ParagraphStyle
from reportlab.platypus import Paragraph
from reportlab.lib import colors
from reportlab.platypus import Table, TableStyle
from reportlab.lib.utils import asUnicode

# added
import img2pdf
from PIL import Image
from io import BytesIO
import PyPDF2
from PyPDF2 import PdfReader

# added
import xlsxwriter
from django.db.models import Q
from django.utils import timezone
import zoneinfo
from django.utils.dateparse import parse_datetime
import fitz
from django.core.paginator import Paginator, EmptyPage
from django.contrib.auth.decorators import permission_required
from rest_framework.views import APIView

email_service = EmailService()


ENV_ROOT = os.path.join(settings.BASE_DIR, ".env")
load_dotenv(ENV_ROOT)

logger = logging.getLogger(__name__)



# Create your views here.
def get_csrf_token(r):
    b = get_token(r)
    return JsonResponse({"csrf_token": b})


def get_access_token(user):
    refresh = RefreshToken.for_user(user)
    print(f"refffffff {str(refresh)} acc {str(refresh.access_token)}")
    return {
        "refresh": str(refresh),
        "access": str(refresh.access_token),
    }


def get_expertise_data(expertise):
    p_data1 = []
    for x in expertise:
        p_data1.append(
            serialize_model(
                ExpertiseCancertype.objects.filter(id__exact=x.id)[0],
                ExpertiseCancertypeSerializer,
            )
        )

    return p_data1


def filter_user_data(a):
    keys = [
        "is_admin",
        "is_active",
        "is_superuser",
        "password",
        "user_permissions",
        "groups",
        "PWVerifyCode",
        "PWCodeGentime",
    ]
    result = {
        k: v for k, v in CuUserRegisterSerializer(a).data.items() if k not in keys
    }
    result["role"] = a.groups.all()[0].name
    if (
        result["role"] == "doctor"
        or result["role"] == "researcher"
        or result["role"] == "influencer"
    ):
        result["prefix"] = a.prefix
        result["expertise"] = get_expertise_data(a.expertise.all())
        result["doctor_other_details"] = serialize_model(
            a.doctordetails, DoctorDetailsSerializer
        )
    elif result["role"] == "patient":
        result["patient_other_details"] = serialize_model(
            a.patientdetails, PatientDetailsSerializer
        )
    # added
    elif result["role"] in ["admin", "child_admin"]:
        result["admin_other_details"] = serialize_model(
            a.admindetails, AdminDetailsSerializer
        )
    # added
    else:
        pass

    return result


def serialize_model(a, serializer):

    result = {k: v for k, v in serializer(a).data.items()}
    print(f"in filter model data   {result}")
    return result


# added
def AddAdminDetails(a):
    d1 = AdminDetails(AdminId=a)
    d1.save()
    print(f"admin__details------------{d1}")
    return d1


# added


def AddPatientDetails(a, b):
    d1 = PatientDetails(PatientId=a, NDAConsent=b)

    d1.save()
    print(f"patientdetails------------{d1}")
    return d1


def AddDoctorDetails(a, b):
    d1 = DoctorDetails(DoctorId=a, NDAConsent=b)
    # d1.EmailVerifyCode = ''.join(random.choice('**********ABCDEF') for i in range(10))
    d1.save()
    print(f"doctor details-----------{d1}")
    return d1


# add user
def AddUser(a, role):
    print("ALL USER DATA ---------------", a)
    try:
        print(f"tfwqeyuw----------{a}--{type(a)}")
        serializer = CuUserRegisterSerializer(data=a)
        serializer.is_valid(
            raise_exception=True
        )  # model field validation automatically applied
        b = serializer.save()
        # b.TimeZone=a['TimeZone']
        # b.save()
        if role == "doctor" or role == "researcher" or role == "influencer":
            b.approval = "pending"
            b.save()
        elif role == "admin":
            b.is_admin = 1
            b.save()
        else:
            pass
        return b
    except Exception as e:
        print("add user exceptionsss", e)

        return str(e)


# add role to user
def AddUserRole(a, role):
    try:
        ct = ContentType.objects.get_for_model(cu_permissions)
        p1 = Permission.objects.get_or_create(
            codename="cu_patient_permissions",
            name="CU patient permissions",
            content_type=ct,
        )
        g1 = Group.objects.get_or_create(name=role)
        g1[0].permissions.add(p1[0])
        if len(a.groups.all()) > 1:
            return False
        else:
            a.groups.add(g1[0])
        return a
    except Exception as e:
        print("add role exceptionsss", e)
        try:
            us = get_user_model().objects.get(email=a["email"]).delete()
            print(f"user deleted role...{us}---{get_user_model()}")
        except Exception as f:
            print("delete user role exceptionsss", f)
        return str(e)


def verify_phone_otp_new(otp, email):
    try:
        otp_obj = CuUserPhoneOTP.objects.filter(
            UserEmail=email, PhoneOTP=otp).latest("PhoneOTPGenTime")
    except CuUserPhoneOTP.DoesNotExist:
        return "OTP validation failed"

    current_time = timezone.localtime(timezone.now())
    otp_generated_time = timezone.localtime(otp_obj.PhoneOTPGenTime)
    expiry_time = otp_generated_time + timedelta(minutes=2)

    if current_time >= expiry_time:
        otp_obj.OTPExpired = True
        otp_obj.save(update_fields=["OTPExpired"])
        return "OTP has expired. Please request a new one."

    # Mark OTP as verified
    otp_obj.PhoneOTPVerified = True
    otp_obj.save(update_fields=["PhoneOTPVerified"])

    return "Valid"


# added
def admin_notification(u_role, u_name):
    # Notification to super_admin
    admin_obj = CuUser.objects.filter(is_admin__exact=1)
    current_time = timezone.now()

    for y in admin_obj:
        device_d = FCMDevice.objects.filter(user_id__exact=y.id)

        print(f"fcm devices----{device_d}---{type(device_d)}")

        if device_d.exists():  # ✅ Check if devices exist before sending message
            a = device_d.send_message(
                Message(
                    notification=Notification(
                        title="New user registered!",
                        body=f"Hi {y.name}, New {u_role} {u_name} has registered with us!",
                    ),
                    webpush=WebpushConfig(
                        fcm_options=WebpushFCMOptions(
                            link=os.getenv(
                                "NEXT_CANCER_UNWIRED_ADMIN_APP") + "/usermanagement"
                        )
                    ),
                )
            )
            print(f"push----{a}")

        # ✅ Save notification to DB regardless of whether FCM devices exist
        res = PushNotifications.objects.create(
            UserId=y.id,
            NotificationTime=current_time,
            Title="New user registered!",
            Body=f"Hi {y.name}, New {u_role} {u_name} has registered with us!",
            Link=os.getenv("NEXT_CANCER_UNWIRED_ADMIN_APP") +
            "/usermanagement",
        )

        print(f"Notification stored in DB: {res}")



# added


class SocialUser(views.APIView):
    serializer_class = CuUserRegisterSerializer
    permission_classes = []

    def post(self, r, *args, **kwargs):
        u_data = json.loads(r.body.decode("utf-8"))
        user_exists = (
            get_user_model().objects.filter(
                email__exact=u_data["email"]).exists()
        )

        if user_exists:
            user_data = get_user_model().objects.filter(
                email__exact=u_data["email"])[0]
            user_type = get_cu_user_type(user_data.id)
            print(f"existsssssss{user_exists}-------{user_type}")
            # added
            if user_data.approval == "self_deactivation":
                return JsonResponse(
                    {"login_status": "You have deactivated your account."}, status=401
                )
            # added
            if u_data["user_app"] == "patient":
                if user_type == "patient":
                    u = get_user_model().objects.get(
                        email__exact=u_data["email"])

                    bb = filter_user_data(u)
                    token = get_access_token(u)
                    return JsonResponse(
                        {
                            "login_status": "success",
                            "user_details": bb,
                            "X-AUTH-TOKEN": token,
                        }
                    )
                else:
                    return JsonResponse({"login_status": "failed"})

            elif u_data["user_app"] == "expert":
                if user_type in ["doctor", "researcher", "influencer"]:
                    u = get_user_model().objects.get(
                        email__exact=u_data["email"])

                    bb = filter_user_data(u)
                    token = get_access_token(u)

                    return JsonResponse(
                        {
                            "login_status": "success",
                            "user_details": bb,
                            "X-AUTH-TOKEN": token,
                        }
                    )
                else:
                    return JsonResponse(
                        {
                            "login_status": "failed",
                            "message": "you are not authorised to access this application",
                        }
                    )
        else:
            user = r.data
            user["password"] = "xxxxxxxxxx"
            user["phone"] = "".join(random.choice("**********")
                                    for i in range(10))
            user["country_code"] = None
            print(f"Social Userrrrrrrrrrrrrrr{user}")
            # res = AddUser(user, u_data['user_role'])
            user_role = "patient" if u_data["user_app"] == "patient" else "doctor"

            res = AddUser(user, user_role)
            if isinstance(res, get_user_model()):

                # res1 = AddUserRole(res, u_data['user_role'])
                res1 = AddUserRole(res, user_role)
                if isinstance(res1, get_user_model()):
                    if get_cu_user_type(res1.id) == "patient":
                        d1 = AddPatientDetails(res, int(u_data["NDAConsent"]))
                        d1.SocialLogin = 1
                        d1.EmailVerified = True
                        d1.save()
                        print(f"social   patient verify {d1}----------------")
                        bb = filter_user_data(res)
                        response = Response(bb, status=status.HTTP_201_CREATED)
                        # inserting patient data into zoho contacts
                        acc_zoho = AddToZohoContacts(
                            CuUser.objects.get(email__exact=u_data["email"])
                        )
                        # return response
                        return Response(
                            {"register_status": "success", "user_details": bb},
                            status=status.HTTP_201_CREATED,
                        )
                    elif get_cu_user_type(res1.id) in [
                        "doctor",
                        "researcher",
                        "influencer",
                    ]:
                        d1 = AddDoctorDetails(res, int(u_data["NDAConsent"]))
                        d1.SocialLogin = 1
                        d1.EmailVerified = True
                        d1.save()
                        print(f"social   doctor verify {d1}----------------")
                        bb = filter_user_data(res)
                        response = Response(bb, status=status.HTTP_201_CREATED)
                        # inserting doctor data into zoho accounts
                        acc_zoho = AddToZoho(
                            CuUser.objects.get(email__exact=u_data["email"])
                        )
                        # added
                        # return response
                        return Response(
                            {"register_status": "success", "user_details": bb},
                            status=status.HTTP_201_CREATED,
                        )
                    else:
                        return JsonResponse(
                            {
                                "register_status": "failed",
                                "message": "you are not authorised to register in this application",
                            }
                        )

                else:
                    return JsonResponse({"register_status": "failed", "message": res})
            else:
                return JsonResponse({"register_status": "failed", "message": res})


class UserRegisterAPIView(generics.CreateAPIView):
    serializer_class = CuUserRegisterSerializer
    permission_classes = []

    def post(self, request, *args, **kwargs):
        try:
            print("request.body", request.body)
            u_data = json.loads(request.body.decode("utf-8"))

        except json.JSONDecodeError:
            return JsonResponse({"message": "Invalid JSON payload."}, status=400)

        required_fields = ["email", "phone", "country_code", "name",
                           "user_role", "NDAConsent", "whatsapp_notification"]
        missing_fields = [
            field for field in required_fields if field not in u_data]
        if missing_fields:
            return JsonResponse({"message": f"{', '.join(missing_fields)} field is required"}, status=400)

        u_data.pop("confirmPassword", None)
        # Convert phone and country_code to strings safely
        phone = str(u_data.get("phone", "")).strip()
        country_code = str(u_data.get("country_code", "")).strip()

        print("country_code ////    /////", country_code)

        if not phone.isdigit():
            return JsonResponse({"message": "Phone number must be numeric."}, status=400)

        serializer = CuUserRegisterSerializer(data=u_data)
        if not serializer.is_valid():
            print(serializer.errors)
            if "email" in serializer.errors:
                return Response("This email address is already registered", status=400)
            elif "phone" in serializer.errors:
                return Response("This phone number is already registered", status=400)
            else:
                return Response(serializer.errors, status=400)

        # OTP rate-limiting check
        recent_otp_exists = CuUserPhoneOTP.objects.filter(
            UserEmail=u_data["email"],
            PhoneOTPVerified=False,
            PhoneOTPGenTime__gte=timezone.now() - timedelta(seconds=60)
        ).exists()
        if recent_otp_exists:
            return JsonResponse({"message": "Please wait at least 1 minute before requesting another OTP."}, status=429)

        password = u_data["password"].strip()
        # hashed_pwd = make_password(password)
        # Generate and save OTP
        otp_code = "".join(random.choice("**********") for _ in range(6))
        obj, _ = CuUserPhoneOTP.objects.update_or_create(
            UserEmail=u_data["email"],
            PhoneOTPVerified=False,
            OTPExpired=False,
            defaults={
                "PhoneOTP": otp_code,
                "PhoneOTPGenTime": timezone.now(),
                "TempPhone": phone,
                "TempCountryCode": country_code,
                # "TempUserData": json.dumps(u_data)
                "TempUserData": json.dumps({k: v for k, v in u_data.items() if k != "password"}),
                "HashedPassword": password
            }
        )

        # Prepare SMS body and send OTP
        otp_body = f"Use {otp_code} to verify your Health Unwired account. This code is valid for 2 mins."
        otp_res = CuSendOTP(phone, otp_body, country_code)

        # WhatsApp Notification (optional)
        if u_data.get("whatsapp_notification") == 1:
            try:
                interakt_url = "https://api.interakt.ai/v1/public/message/"
                payload = json.dumps({
                    "countryCode": country_code,
                    "phoneNumber": phone,
                    "callbackData": "some text here",
                    "type": "Template",
                    "template": {
                        "name": "cu_otp_1",
                        "languageCode": "en",
                        "bodyValues": [otp_code, "HealthUnwired", "cancerunwired.com"],
                    },
                })
                headers = {
                    "Authorization": f'Basic {os.getenv("WHATSAPP_INTERAKT_API_KEY")}',
                    "Content-Type": "application/json",
                }
                response = requests.post(
                    interakt_url, headers=headers, data=payload)
                print(f"WhatsApp Response: {response.text}")
            except Exception as e:
                print(f"WhatsApp send error: {str(e)}")

        if otp_res.get("MessageId"):
            return JsonResponse({
                "message": "OTP sent successfully.",
                "otp": otp_code
            })
        else:
            return JsonResponse({"message": "Failed to send OTP"}, status=500)


class UserVerifyOTP(generics.CreateAPIView):
    serializer_class = CuUserRegisterSerializer
    permission_classes = []

    def post(self, request, *args, **kwargs):
        otp = request.data.get("otp")
        email = request.data.get("email")
        if not otp or not email:
            return Response({"message": "OTP and email are required"}, status=400)

        # Find OTP object
        try:
            otp_obj = CuUserPhoneOTP.objects.filter(
                PhoneOTP=otp, UserEmail=email).latest("PhoneOTPGenTime")
        except CuUserPhoneOTP.DoesNotExist:
            return Response({"message": "Invalid or expired OTP"}, status=400)

        email = otp_obj.UserEmail
        verify_otp_status = verify_phone_otp_new(otp, email)

        if verify_otp_status != "Valid":
            return Response({"message": verify_otp_status}, status=400)

        # Load user data
        try:
            user_data = json.loads(otp_obj.TempUserData)
            user_role = user_data.get("user_role")
            country_code = otp_obj.TempCountryCode
            print("country code from the tempCountruode-------- ", country_code)
            user_data["country_code"] = country_code
            NDAConsent = int(user_data.get("NDAConsent", 0))
            # Inject hashed password
            if not otp_obj.HashedPassword:
                return Response({"message": "Password not found for this registration"}, status=500)
            user_data["password"] = otp_obj.HashedPassword
        except Exception as e:
            return Response({"message": "Failed to load user data"}, status=500)

        u_name = ""

        # ✅ Separate flow for child_admin
        if user_role == "child_admin":
            try:
                res = get_user_model().objects.get(email=email)
                obj_a = AdminDetails.objects.get(AdminId=res)
                obj_a.PhoneVerified = True
                obj_a.save()
                u_name = res.name
                email_service.onboarding_email(
                    email, u_name, "https://admin.healthunwired.com/auth/login","patient"
                )
                otp_obj.delete()
                return Response({"message": "Mobile verification successful"}, status=200)
            except Exception as e:
                return Response({"message": f"Error verifying child admin: {str(e)}"}, status=500)

        # 👇 Normal flow for all other roles
        res = AddUser(user_data, user_role)
        print("tis is the add user resp", res)
        if not isinstance(res, get_user_model()):
            return Response({"message": res})

        res1 = AddUserRole(res, user_role)
        if not isinstance(res1, get_user_model()):
            return Response({"message": res1})

        u_name = res.name

        if user_role == "patient":
            AddPatientDetails(res, NDAConsent)
            email_service.onboarding_email(
                email, u_name, "https://healthunwired.com/auth/login","patient")
            # send_onboarding_email(
            #     email, u_name, "https://healthunwired.com/auth/login")
            otp_obj.HashedPassword = None
            otp_obj.save()
            otp_obj.delete()

        elif user_role in ["doctor", "researcher", "influencer"]:
            AddDoctorDetails(res, NDAConsent)
            # AddToZoho(res)
            print("Not after adding to the zoho")
            email_service.onboarding_email(
                email, u_name, "https://expert.healthunwired.com/login","expert")
            otp_obj.HashedPassword = None
            otp_obj.save()
            otp_obj.delete()
            print("not after deleting the objct")
        elif user_role == "admin":
            AddAdminDetails(res)
            email_service.onboarding_email(
                email, u_name, "https://admin.healthunwired.com/auth/login","patient")

            otp_obj.HashedPassword = None
            otp_obj.save()
            otp_obj.delete()

        # Notify admin and delete OTP
        try:
            print("in admin notification")
            admin_notification(user_role, u_name)
        except Exception as e:
            logger.error(f"Admin notification failed: {str(e)}")

        return Response({"message": "OTP verified successfully"}, status=status.HTTP_201_CREATED)


@method_decorator(csrf_exempt, name="dispatch")
class UserResendOTP(View):
    def post(self, request):
        try:
            u_data = json.loads(request.body.decode("utf-8"))
        except json.JSONDecodeError:
            return JsonResponse({"message": "Invalid JSON payload."}, status=400)

        email = u_data.get("email")
        phone = u_data.get("phone")
        country_code = u_data.get("country_code")

        if not (email and phone and country_code):
            return JsonResponse({"message": "Missing required fields"}, status=400)

        try:
            otp_obj = CuUserPhoneOTP.objects.filter(
                UserEmail=email,
                PhoneOTPVerified=False
            ).latest("PhoneOTPGenTime")
        except CuUserPhoneOTP.DoesNotExist:
            return JsonResponse({"message": "OTP not found. Please register again."}, status=404)

        if otp_obj.TempPhone != phone or otp_obj.TempCountryCode != country_code:
            return JsonResponse({
                "message": "Phone number doesn't match our records. Please use the number you registered with."
            }, status=400)

        if otp_obj.PhoneOTPGenTime >= timezone.now() - timedelta(seconds=60):
            return JsonResponse({"message": "Please wait at least 1 minute before requesting another OTP."}, status=429)

        # Expire old OTP
        if timezone.now() >= otp_obj.PhoneOTPGenTime + timedelta(minutes=2):
            otp_obj.OTPExpired = True
            otp_obj.save(update_fields=["OTPExpired"])

        # Generate and send new OTP
        regotp = "".join(random.choice("**********") for _ in range(6))
        otp_body = f"Use {regotp} to verify your Health Unwired account. This code is valid for 2 mins."

        otp_res = CuSendOTP(phone, otp_body, country_code)

        if otp_res.get("MessageId") is not None:
            otp_obj.PhoneOTP = regotp
            otp_obj.PhoneOTPGenTime = timezone.now()
            otp_obj.OTPExpired = False
            otp_obj.save(update_fields=["PhoneOTP",
                         "PhoneOTPGenTime", "OTPExpired"])

            return JsonResponse({
                "message": "OTP Re-sent successfully.",
                "otp": regotp  # Remove in prod
            })
        else:
            return JsonResponse({"message": "OTP couldn't be sent"}, status=500)


class SendPhoneUpdateOTPAPIView(APIView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        user = request.user
        data = request.data

        phone = str(data.get("phone"))
        country_code = str(data.get("country_code"))

        if not phone or not country_code:
            return JsonResponse({
                "message": "Phone and country_code are required"
            }, status=400)

        if user.phone == phone and user.country_code == country_code:
            return JsonResponse({
                "message": "This phone number is already associated with your account"
            }, status=400)

        if CuUser.objects.filter(phone=phone, country_code=country_code).exclude(id=user.id).exists():
            return JsonResponse({
                "message": "This phone number is already registered with another account"
            }, status=400)

        recent_otp_exists = CuUserPhoneOTP.objects.filter(
            UserEmail=user.email,
            PhoneOTPVerified=False,
            PhoneOTPGenTime__gte=timezone.now() - timedelta(seconds=60)
        ).exists()

        if recent_otp_exists:
            return JsonResponse({
                "message": "Please wait at least 1 minute before requesting another OTP."
            }, status=429)

        try:
            otp_code = "".join(random.choice("**********") for _ in range(6))
            obj = CuUserPhoneOTP.objects.update_or_create(
                UserEmail=user.email,
                PhoneOTPVerified=False,
                defaults={
                    "PhoneOTP": otp_code,
                    "PhoneOTPGenTime": timezone.now(),
                    "TempPhone": phone,
                    "TempCountryCode": country_code
                }
            )

            otp_body = f"Use {otp_code} to verify your phone number change. This code is valid for 2 mins."
            otp_res = CuSendOTP(phone, otp_body, country_code)

            if otp_res.get("MessageId"):
                return JsonResponse({
                    "message": "OTP sent successfully",
                    "otp": otp_code
                })
            else:
                return JsonResponse({
                    "message": "Failed to send OTP"
                }, status=500)

        except Exception as e:
            return JsonResponse({
                "message": "An unexpected error occurred",
                "error": str(e)
            }, status=500)


class VerifyPhoneUpdateOTPAPIView(APIView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        user = request.user
        otp = request.data.get("otp")

        if not otp:
            return JsonResponse({"message": "OTP is required"}, status=400)

        result = verify_phone_otp_new(otp, user.email)

        if result != "Valid":
            return JsonResponse({"message": result}, status=400)
        try:
            otp_obj = CuUserPhoneOTP.objects.filter(
                UserEmail=user.email,
                PhoneOTP=otp
            ).latest("PhoneOTPGenTime")

            user.phone = otp_obj.TempPhone
            user.country_code = otp_obj.TempCountryCode
            user.save(update_fields=["phone", "country_code"])

            otp_obj.PhoneOTP = None
            otp_obj.PhoneOTPVerified = True
            otp_obj.save(update_fields=["PhoneOTP", "PhoneOTPVerified"])

            otp_obj.delete()

        except Exception as e:
            return JsonResponse({"message": f"Failed to update phone: {str(e)}"}, status=500)

        return JsonResponse({"message": "Phone number updated successfully."})


def EncString(text):

    key = os.getenv("ENC_KEY")
    iv = b"\x00" * 16  # initialization vector, must be random for real-world use
    cipher = AES.new(key.encode(), AES.MODE_CBC, iv)
    text = text.encode()
    padded_text = pad(text, AES.block_size, style="pkcs7")
    encrypted_text = cipher.encrypt(padded_text)
    dec = base64.b64encode(iv + encrypted_text).decode()

    print(f"encryption--{dec}")
    return dec


def SendPushNotification(user_id):
    device = FCMDevice.objects.filter(user_id__exact=user_id)

    u_name = get_user_model().objects.get(id__exact=user_id).name
    print(f"fcm devices----{device}---{type(device)}")
    a = device.send_message(
        Message(
            notification=Notification(
                title="Login alert!!", body=f"hi {u_name} hope u r good!!"
            ),
            webpush=WebpushConfig(
                fcm_options=WebpushFCMOptions(
                    link=os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP")
                    + "/myprofile?tab=inbox"
                )
            ),
        )
    )

    print(f"push----{a}")
    return a


# added
def SendPushFollowupReminder(u_id, uu_name, current_time):
    # print(days)
    device = FCMDevice.objects.filter(user_id__exact=u_id)

    u_name = get_user_model().objects.get(id__exact=u_id).name
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    data = ""
    print(data)
    if data:
        a = ""
    else:
        a = device.send_message(
            Message(
                notification=Notification(
                    title="Follow up reminder!",
                    body=f"Hi {u_name}, It has been 10 days since your appointment with {uu_name}, please follow up!",
                ),
                webpush=WebpushConfig(
                    fcm_options=WebpushFCMOptions(
                        link=os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP")
                        + "/appointment"
                    )
                ),
            )
        )
        res = PushNotifications.objects.create(
            UserId=u_id,
            NotificationTime=current_time,
            Title="Follow up reminder!",
            Body=f"Hi {u_name}, It has been 10 days since your appointment with {uu_name}, please follow up!",
            Link=os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP") + "/appointment",
        )
        print(f"push----{a}")
    return a


# added


# added
def SendPushMedicalRecordReminder(u_id, uu_name, current_time):
    # print(days)
    device = FCMDevice.objects.filter(user_id__exact=u_id)

    u_name = get_user_model().objects.get(id__exact=u_id).name
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    data = ""
    print(data)
    if data:
        a = ""
    else:
        a = device.send_message(
            Message(
                notification=Notification(
                    title="Upload latest medical records!",
                    body=f"Hi {u_name}, Upload latest medical records before appointment with {uu_name}.",
                ),
                webpush=WebpushConfig(
                    fcm_options=WebpushFCMOptions(
                        link=os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP")
                        + "/myprofile?tab=medical"
                    )
                ),
            )
        )
        res = PushNotifications.objects.create(
            UserId=u_id,
            NotificationTime=current_time,
            Title="Upload latest medical records!",
            Body=f"Hi {u_name}, Upload latest medical records before appointment with {uu_name}.",
            Link=os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP")
            + "/myprofile?tab=medical",
        )
        print(f"push----{a}")
    return a


# added


# added


def SendPushPresReminder(u_id, uu_name, current_time):

    device = FCMDevice.objects.filter(user_id__exact=u_id)
    print(f"in reminderrrrrrrrrrr{device}")
    u_name = get_user_model().objects.get(id__exact=u_id).name
    print(f"in user nameeeeeeeeeeeeeeeeeeeee{u_name}")
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    a = device.send_message(
        Message(
            notification=Notification(
                title="Prescription reminder!",
                body=f"Hi {u_name}, Prescription is pending for the recent appointment with {uu_name}",
            ),
            webpush=WebpushConfig(
                fcm_options=WebpushFCMOptions(
                    link=os.getenv(
                        "NEXT_CANCER_UNWIRED_DOCTOR_APP") + "/appointment"
                )
            ),
        )
    )
    res = PushNotifications.objects.create(
        UserId=u_id,
        NotificationTime=current_time,
        Title="Prescription reminder!",
        Body=f"Hi {u_name}, Prescription is pending for the recent appointment with {uu_name}",
        Link=os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP") + "/appointment",
    )
    return a

    # print(f"in reminderrrrrrrrrrr response{a}")


# added


# added


def SendPushReminder(
    u_id, uu_name, days, current_time, user_role, start_time, end_time
):

    device = FCMDevice.objects.filter(user_id__exact=u_id)
    print(f"in reminderrrrrrrrrrr{device}")
    u_name = get_user_model().objects.get(id__exact=u_id).name
    print(f"in user nameeeeeeeeeeeeeeeeeeeee{u_name}")
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    data = ""
    print(data)

    s_date = start_time.date()
    ss_time = start_time.time()
    b = str(ss_time).split(":")
    s_time = b[0] + ":" + b[1]

    ee_time = end_time.time()
    b = str(ee_time).split(":")
    e_time = b[0] + ":" + b[1]

    if user_role == "patient":

        # push_data = PushNotifications.objects.filter(UserId=u_id,Title='Appointment reminder!', Body=f'Hi {u_name}, You have an appointment with {uu_name} in {days} days!',Link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments')
        # if push_data is not None:

        a = device.send_message(
            Message(
                notification=Notification(
                    title="Appointment reminder!",
                    body=f"Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}",
                ),
                webpush=WebpushConfig(
                    fcm_options=WebpushFCMOptions(
                        link=os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP")
                        + "/myprofile?tab=appointments"
                    )
                ),
            )
        )
        res = PushNotifications.objects.create(
            UserId=u_id,
            NotificationTime=current_time,
            Title="Appointment reminder!",
            Body=f"Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}",
            Link=os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP")
            + "/myprofile?tab=appointments",
        )
        return a
    elif user_role in ["doctor", "researcher", "influencer"]:
        # push_data = PushNotifications.objects.filter(UserId=u_id,Title='Appointment reminder!',Body=f'Hi {u_name}, You have an appointment with {uu_name} in {days} days!',Link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment')
        # if push_data is not None:

        a = device.send_message(
            Message(
                notification=Notification(
                    title="Appointment reminder!",
                    body=f"Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}",
                ),
                webpush=WebpushConfig(
                    fcm_options=WebpushFCMOptions(
                        link=os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP")
                        + "/appointment"
                    )
                ),
            )
        )
        res = PushNotifications.objects.create(
            UserId=u_id,
            NotificationTime=current_time,
            Title="Appointment reminder!",
            Body=f"Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}",
            Link=os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP") + "/appointment",
        )
        return a
    else:
        pass

    # print(f"in reminderrrrrrrrrrr response{a}")


# added

# added


def appointment_reminder(user_id):
    user_role = get_cu_user_type(user_id)
    current_time = timezone.now()
    n_e = notification_check("Appointment reminder")
    if user_role == "patient" and n_e == True:
        app_data = Appointments.objects.filter(patient_id=user_id)
        for x in app_data:
            slot_id = x.slot_id_id
            s_data = SchedulerSlots.objects.filter(id=x.slot_id_id)
            if s_data.exists():
                slot_data = SchedulerSlots.objects.filter(id=x.slot_id_id)[0]

                print(
                    f"-----------------------------slot_data----------{slot_data}")
                start_date = slot_data.schedule_start_time
                end_date = slot_data.schedule_end_time
                print(
                    f"--------------------------start_date-----------{start_date}")
                print(
                    f"--------------------------end_date-----------{end_date}")
                # start_date3 = datetime.strptime(str(parse_datetime(str(start_date))),format_data)
                # start_date2 = timezone.make_aware(parse_datetime(str(start_date)),timezone.get_current_timezone())
                converted_time1 = timezone.localtime(
                    start_date, timezone.get_current_timezone()
                )
                print(
                    f"-------------------coverted_time---------{converted_time1}")
                converted_time2 = timezone.localtime(
                    end_date, timezone.get_current_timezone()
                )
                print(
                    f"-------------------coverted_time---------{converted_time2}")
                diff = converted_time1 - current_time
                if diff.days == 7 or diff.days == 15 or diff.days == 1:
                    # if user_role == 'patient':
                    doctor_id = slot_data.doctor_id
                    uu_name = CuUser.objects.filter(id=doctor_id)[0].name
                    a = SendPushReminder(
                        user_id,
                        uu_name,
                        diff.days,
                        current_time,
                        user_role,
                        converted_time1,
                        converted_time2,
                    )
    elif user_role in ["doctor", "researcher", "influencer"] and n_e == True:
        slot_data = SchedulerSlots.objects.filter(doctor_id=user_id)
        print(f"slot dataaaaaaaaaa{slot_data}")
        for x in slot_data:
            slot_id = x.id
            print(f"slot iddddddddddddd{slot_id}")
            p_data = Appointments.objects.filter(slot_id_id=slot_id)
            if p_data.exists():
                patient_data = Appointments.objects.filter(
                    slot_id_id=slot_id)[0]
                print(
                    f"-----------------------------slot_data----------{slot_data}")
                start_date = x.schedule_start_time
                end_date = x.schedule_end_time
                print(
                    f"--------------------------start_date-----------{start_date}")
                print(
                    f"--------------------------end_date-----------{end_date}")
                converted_time1 = timezone.localtime(
                    parse_datetime(
                        str(start_date)), timezone.get_current_timezone()
                )

                print(
                    f"-------------------converted_time1---------{converted_time1}")
                converted_time2 = timezone.localtime(
                    end_date, timezone.get_current_timezone()
                )
                print(
                    f"-------------------converted_time---------{converted_time2}")
                diff = converted_time1 - current_time
                if diff.days == 7 or diff.days == 15 or diff.days == 1:
                    patient_id = patient_data.patient_id
                    uu_name = CuUser.objects.filter(id=patient_id)[0].name
                    a = SendPushReminder(
                        user_id,
                        uu_name,
                        diff.days,
                        current_time,
                        user_role,
                        converted_time1,
                        converted_time2,
                    )


# added


# added


def prescription_reminder(user_id):
    current_time = timezone.now()
    slot_data = SchedulerSlots.objects.filter(doctor_id=user_id)
    slot_data1 = SchedulerSlots.objects.filter(doctor_id=user_id).values()
    print(slot_data1)
    b = 1
    n_p = notification_check("Prescription reminder")
    for x in slot_data:
        print(x)
        a_data = Appointments.objects.filter(slot_id_id=x.id)
        if a_data.exists():
            app_data = Appointments.objects.filter(slot_id_id=x.id)[0]
            print(f"-----------------------------app_data----------{app_data}")
            pres_data = Prescription.objects.filter(
                AppointmentId_id=app_data.id)
            print(f"--------------------------pres_data-----------{pres_data}")
            if pres_data.exists():
                pass
            else:
                end_date = x.schedule_end_time

                print(
                    f"--------------------------end_date-----------{end_date}")

                # start_date3 = datetime.strptime(str(parse_datetime(str(start_date))),format_data)
                # start_date2 = timezone.make_aware(parse_datetime(str(start_date)),timezone.get_current_timezone())
                converted_time1 = timezone.localtime(
                    end_date, timezone.get_current_timezone()
                )
                print(
                    f"-------------------converted_time---------{converted_time1}")
                diff = current_time - converted_time1
                if diff.days in [1, 2, 3] and n_p == True:

                    patient_id = app_data.patient_id
                    uu_name = CuUser.objects.filter(id=patient_id)[0].name
                    a = SendPushPresReminder(user_id, uu_name, current_time)
                    print(
                        f"-------------------push_reminder_response---------{a}")


# added

# added


def notification_check(n_name):
    noti_check = NotificationType.objects.filter(
        NotificationName=n_name, CategoryType="N"
    )
    if noti_check.exists():
        if (
            NotificationType.objects.filter(NotificationName=n_name, CategoryType="N")[
                0
            ].ActiveStatus
            == 1
        ):
            return True
        return False
    else:
        noti_res = NotificationType.objects.create(
            NotificationName=n_name, CategoryType="N"
        )
        return True


def send_noti(u_id):
    # --------------------------appointment reminder notification starts here-------------------
    app_rem_res = appointment_reminder(str(u_id))
    print(
        f"----------- appointment reminder response-------{app_rem_res}------------")
    # -----------------------------ends here------------------------------------------------------

    # -------medical record push notification starts here
    if get_cu_user_type(u_id) == "patient":
        current_date = timezone.now()
        data = Appointments.objects.filter(patient__id=u_id).values()
        print(f"test data{data}")
        n_m = notification_check("Upload latest medical records")
        n_f = notification_check("Follow up reminder")
        for x in data:

            print(x["slot_id_id"])
            uu_name = SchedulerSlots.objects.get(
                id__exact=x["slot_id_id"]).doctor.name

            print(uu_name)
            start_date = SchedulerSlots.objects.get(
                id__exact=x["slot_id_id"]
            ).schedule_start_time
            end_date = SchedulerSlots.objects.get(
                id__exact=x["slot_id_id"]
            ).schedule_end_time

            print(start_date)

            converted_time1 = timezone.localtime(
                start_date, timezone.get_current_timezone()
            )
            converted_time2 = timezone.localtime(
                end_date, timezone.get_current_timezone()
            )
            print(converted_time1)
            diff = converted_time1 - current_date
            print(diff.days)
            if diff.days in [1, 2, 3] and n_m == True:
                a = SendPushMedicalRecordReminder(u_id, uu_name, current_date)
                print(
                    f"----------- medical record upload reminder response-------{a}------------"
                )

            # ------------------medical record push notification ends here ----------------

            # --------------------------------follow up starts here--------------------------------

            follow_up_date = converted_time2 - current_date
            if follow_up_date.days >= 10 and follow_up_date.days <= 11:
                m_success = MeetingSession.objects.filter(AppointmentId_id=x["id"])[
                    0
                ].IsSuccess
                print(f"---------------{m_success}")
                if m_success == 1 and n_f == True:

                    a = SendPushFollowupReminder(u_id, uu_name, current_date)
                    print(
                        f"-----------follow up reminder response-------{a}------------"
                    )

                else:
                    pass

            else:
                pass
    # --------------------------------follow up ends here--------------------------------

    # ------------------------------prescription notification starts here ----------------------------
    if get_cu_user_type(u_id) in ["doctor", "researcher", "influencer"]:

        pres_rem_res = prescription_reminder(str(u_id))
        print(
            f"----------- prescription reminder response-------{pres_rem_res}------------"
        )

    # ------------------------------prescription notification ends here ------------------------------
    return Response("reminder sent")


# added


# @method_decorator(csrf_exempt, name="dispatch")
# class UserLogin(View):
#     def post(self, request):
#         u_data = json.loads(request.body.decode("utf-8"))
#         print("u_data============", u_data)
#         if_key = "role" in u_data
#         # u = authenticate(email=u_data['email'], password=u_data['password'],user_role=u_data['user_role'])
#         self_d_status = CuUser.objects.filter(email__exact=u_data["email"])[0]
#         a_r_r = StatusReason.objects.filter(
#             ExpertId_id__exact=self_d_status.id, ReasonType__exact="Reactivation"
#         )
#         if a_r_r.exists() and self_d_status.approval == "Approval_requested":
#             a_r_r = a_r_r.order_by("-CurrentTime")[0]
#             d_d = DoctorDetails.objects.filter(
#                 DoctorId__exact=self_d_status.id)
#             if d_d.exists() and d_d[0].DateOfActivation < a_r_r.CurrentTime:
#                 return JsonResponse(
#                     {"login_status": "You have deactivated your account."}, status=401
#                 )
#             else:
#                 pass
#         else:
#             pass
#         if self_d_status.approval == "self_deactivation":
#             return JsonResponse(
#                 {"login_status": "You have deactivated your account."}, status=401
#             )
#         u = authenticate(
#             email=u_data["email"],
#             password=u_data["password"],
#             user_app=u_data["user_app"],
#         )
#         print(f"logiiiinnn------{u}")
#         if u is not None:
#             login(request, u)
#             timezone.activate(zoneinfo.ZoneInfo(u.TimeZone))
#             timezone.localtime(u.last_login, timezone.get_current_timezone())
#             print(
#                 f"userrr {u} groups {u.groups.all()[0].name} permissions {u.get_all_permissions()}-------{type(u.get_all_permissions())}"
#             )
#             bb = filter_user_data(u)
#             bb["user_permissions"] = list(u.get_all_permissions())
#             user_role = get_cu_user_type(u.id)
#             if user_role in ["doctor", "researcher", "influencer"]:
#                 dd = u.doctordetails

#                 print(f"bbbbbbbbb---{dd}----{dd.EmailVerifyCode is not None}")
#                 code = ""
#                 if dd.EmailVerifyCode is not None:
#                     code = EncString(dd.EmailVerifyCode)
#                 bb["Enc"] = code

#             # if bb['approval'] in ["Deactivated","Rejected"]:
#             #     reasons = get_user_model().objects.get(id__exact=bb['id']).statusreason_set.all()
#             #     if len(reasons) > 0:
#             #         bb['StatusReason']=serialize_model(StatusReason.objects.get(ExpertId__exact=bb['id']),StatusReasonSerializer)
#             if bb["approval"] in ["Rejected"]:
#                 reasons = (
#                     get_user_model()
#                     .objects.get(id__exact=bb["id"])
#                     .statusreason_set.filter(ReasonType="Rejection")
#                 )
#                 if len(reasons) > 0:
#                     bb["StatusReason"] = serialize_model(
#                         StatusReason.objects.filter(
#                             ExpertId__exact=bb["id"]).last(),
#                         StatusReasonSerializer,
#                     )

#             elif bb["approval"] in ["Deactivated"]:
#                 reasons = (
#                     get_user_model()
#                     .objects.get(id__exact=bb["id"])
#                     .statusreason_set.filter(
#                         ReasonType__in=["Deactivation",
#                                         "Reactivation_Rejection"]
#                     )
#                 )
#                 if len(reasons) > 0:
#                     bb["StatusReason"] = serialize_model(
#                         StatusReason.objects.filter(
#                             ExpertId__exact=bb["id"]).last(),
#                         StatusReasonSerializer,
#                     )
#             else:
#                 pass
#             token = get_access_token(u)
#             # added------------notifications added----------------------------------------------------------------------
#             n_res = send_noti(u.id)
#             print(
#                 f"---------------------------------notifications res {n_res}")
#             # added
#             return JsonResponse(
#                 {"login_status": "success", "user_details": bb, "X-AUTH-TOKEN": token}
#             )
#         else:
#             uu_data = CuUser.objects.filter(email__exact=u_data['email'])
#             if uu_data.exists() and uu_data[0].approval in ["Deleted", "self_deleted"]:
#                 return JsonResponse({"login_status": "Your account has been deleted."}, status=401)
#             return JsonResponse({"login_status": "failed"})

@method_decorator(csrf_exempt, name="dispatch")
class UserLogin(View):
    def post(self, request):
        # Parse the incoming request data
        try:
            u_data = json.loads(request.body.decode("utf-8"))
        except json.JSONDecodeError:
            return JsonResponse({"login_status": "Invalid input data. Please provide valid JSON."}, status=400)

        # Basic input validation
        required_fields = ["email", "password", "user_app"]
        missing_fields = [
            field for field in required_fields if field not in u_data]
        if missing_fields:
            return JsonResponse({"login_status": f"Missing required fields: {', '.join(missing_fields)}"}, status=400)

        email = u_data["email"]
        password = u_data["password"]
        user_app = u_data["user_app"]

        # Validate email format
        if not email or "@" not in email:
            return JsonResponse({"login_status": "Invalid email format."}, status=400)

        # Attempt to fetch user data
        try:
            self_d_status = CuUser.objects.get(email__exact=email)
            print("this is the user ----!", self_d_status)
            if self_d_status.check_password(password):
                print("Password is correct!")
            else:
                print("Password is incorrect.")
        except CuUser.DoesNotExist:
            return JsonResponse({"login_status": "User not found."}, status=404)

        # Handle account deactivation and reactivation logic
        a_r_r = StatusReason.objects.filter(
            ExpertId_id__exact=self_d_status.id, ReasonType__exact="Reactivation"
        )
        if a_r_r.exists() and self_d_status.approval == "Approval_requested":
            a_r_r = a_r_r.order_by("-CurrentTime")[0]
            d_d = DoctorDetails.objects.filter(
                DoctorId__exact=self_d_status.id)
            if d_d.exists() and d_d[0].DateOfActivation < a_r_r.CurrentTime:
                return JsonResponse(
                    {"login_status": "You have deactivated your account."}, status=401
                )

        if self_d_status.approval == "self_deactivation":
            return JsonResponse(
                {"login_status": "You have deactivated your account."}, status=401
            )

        print("password------", password)
        print("email------", email)
        print("user_app------", user_app)

        # Authenticate the user with email, password, and app
        u = authenticate(
            email=email,
            password=password,
            user_app=user_app,
        )
        print(f"Authenticated User: {u}")

        if u is not None:
            login(request, u)
            timezone.activate(zoneinfo.ZoneInfo(u.TimeZone))
            timezone.localtime(u.last_login, timezone.get_current_timezone())

            # Prepare user details for response
            bb = filter_user_data(u)
            bb["user_permissions"] = list(u.get_all_permissions())
            user_role = get_cu_user_type(u.id)

            if user_role in ["doctor", "researcher", "influencer"]:
                dd = u.doctordetails
                code = ""
                if dd.EmailVerifyCode is not None:
                    code = EncString(dd.EmailVerifyCode)
                bb["Enc"] = code

            if bb["approval"] in ["Rejected"]:
                reasons = (
                    get_user_model()
                    .objects.get(id__exact=bb["id"])
                    .statusreason_set.filter(ReasonType="Rejection")
                )
                if len(reasons) > 0:
                    bb["StatusReason"] = serialize_model(
                        StatusReason.objects.filter(
                            ExpertId__exact=bb["id"]).last(),
                        StatusReasonSerializer,
                    )

            elif bb["approval"] in ["Deactivated"]:
                reasons = (
                    get_user_model()
                    .objects.get(id__exact=bb["id"])
                    .statusreason_set.filter(
                        ReasonType__in=["Deactivation",
                                        "Reactivation_Rejection"]
                    )
                )
                if len(reasons) > 0:
                    bb["StatusReason"] = serialize_model(
                        StatusReason.objects.filter(
                            ExpertId__exact=bb["id"]).last(),
                        StatusReasonSerializer,
                    )
            else:
                pass

            # Generate access token for the user
            token = get_access_token(u)

            return JsonResponse(
                {"login_status": "success", "user_details": bb, "X-AUTH-TOKEN": token}
            )
        else:
            # If authentication fails, check for deleted/self-deleted accounts
            uu_data = CuUser.objects.filter(email__exact=email)
            if uu_data.exists():
                user = uu_data[0]
                # Check if user account is deleted or self-deleted
                if user.approval in ["Deleted", "self_deleted"]:
                    return JsonResponse({"login_status": "Your account has been deleted."}, status=401)

            # If credentials are incorrect, return a failed message
            return JsonResponse({"login_status": "Invalid email or password."}, status=401)


def get_cu_user(a):
    return get_user_model().objects.get(id=a)


class UserTest(generics.RetrieveAPIView):

    queryset = CuUser.objects.all()
    serializer_class = CuUserRegisterSerializer
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        a = self.retrieve(request, *args, **kwargs)
        y = json.loads(json.dumps(a.data))
        print(f"test----{y['id']}")
        if a.status_code == 200:

            return JsonResponse({"status": "authenticated", "user_id": y["id"]})


class GetUser(generics.RetrieveAPIView):

    queryset = CuUser.objects.all()
    serializer_class = CuUserRegisterSerializer
    lookup_field = "email"

    def get(self, request, *args, **kwargs):

        a = self.retrieve(request, *args, **kwargs)
        user_details = get_cu_user(a.data["id"])
        user_data = filter_user_data(user_details)
        s_r = dict()
        if user_data["role"] in ["doctor", "researcher", "influencer"]:

            print(
                f"user permissions----------{user_details.user_permissions.all()}---------{user_details.has_perm('cu_app.perm1_code')}"
            )

            if user_data["doctor_other_details"]["ProfilePhoto"] is not None:
                new_obj_url = get_s3_signed_url_bykey(
                    user_data["doctor_other_details"]["ProfilePhoto"]
                )
                user_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url

            if user_data["doctor_other_details"]["Signature"] is not None:
                new_obj_url = get_s3_signed_url_bykey(
                    user_data["doctor_other_details"]["Signature"]
                )
                user_data["doctor_other_details"]["Signature"] = new_obj_url

            if user_data["doctor_other_details"]["IntVideoUrl"] is not None:
                a_i = []
                b = user_data["doctor_other_details"]["IntVideoUrl"]
                for i in b:
                    new_obj_url = get_s3_signed_url_bykey(i)
                    a_i.append(new_obj_url)
                    print(f"ddd details video--------{new_obj_url}")
                user_data["doctor_other_details"]["IntVideoUrl"] = a_i
            # added
            if user_data["doctor_other_details"]["IntroVideoStatus"] == 3:
                dd = StatusReason.objects.filter(
                    ExpertId_id__exact=a.data["id"],
                    ReasonType__exact="Intro_Video_Rejection",
                )
                if dd.exists():
                    reason = StatusReason.objects.filter(
                        ExpertId_id__exact=a.data["id"],
                        ReasonType__exact="Intro_Video_Rejection",
                    ).order_by("-CurrentTime")[0]
                    user_data["doctor_other_details"][
                        "IntroVideoStatus_Reason"
                    ] = reason.Reason
            # added
            if user_data["doctor_other_details"]["Certificates"] is not None:
                l1 = []
                print(
                    f"certs--------{user_data['doctor_other_details']['Certificates']}--------{type(user_data['doctor_other_details']['Certificates'])}"
                )
                for x in user_data["doctor_other_details"]["Certificates"]:
                    c1 = []
                    new_obj_url = get_s3_signed_url_bykey(
                        x[1]) if len(x) == 2 else None
                    c1.append(x[0])
                    c1.append(new_obj_url)
                    # user_data['doctor_other_details']['IntVideoUrl']=new_obj_url
                    print(f"ddd details video--------{l1}")
                    l1.append(c1)
                user_data["doctor_other_details"]["Certificates"] = l1
            if user_data["doctor_other_details"]["OtherAchievements"] is not None:
                l1 = []
                print(
                    f"certs--------{user_data['doctor_other_details']['OtherAchievements']}--------{type(user_data['doctor_other_details']['OtherAchievements'])}"
                )
                for x in user_data["doctor_other_details"]["OtherAchievements"]:
                    c1 = []
                    new_obj_url = get_s3_signed_url_bykey(
                        x[1]) if len(x) == 2 else None
                    c1.append(x[0])
                    c1.append(new_obj_url)
                    # user_data['doctor_other_details']['IntVideoUrl']=new_obj_url
                    print(f"ddd details video--------{l1}")
                    l1.append(c1)
                user_data["doctor_other_details"]["OtherAchievements"] = l1
            # s_r=dict()
            if user_data["approval"] == "Rejected":
                reasons = (
                    get_user_model()
                    .objects.get(id__exact=user_data["id"])
                    .statusreason_set.filter(ReasonType="Rejection")
                )
                if len(reasons) > 0:
                    reasons = StatusReason.objects.filter(
                        ExpertId__exact=user_data["id"], ReasonType="Rejection"
                    ).order_by("-CurrentTime")[0]
                s_r.update(
                    {
                        "approval": "Rejected",
                        "rejected_reason": reasons.Reason,
                        "rejected_time": reasons.CurrentTime,
                        "rejected_category": reasons.ReasonCategory,
                    }
                )
                user_data["approval_status_reason"] = s_r
            elif user_data["approval"] == "Deactivated":
                reasons = (
                    get_user_model()
                    .objects.get(id__exact=user_data["id"])
                    .statusreason_set.filter(
                        ReasonType__in=["Deactivation",
                                        "Reactivation_Rejection"]
                    )
                )
                print(f"------eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee{reasons}")
                if len(reasons) > 0:
                    reasons = StatusReason.objects.filter(
                        ExpertId__exact=user_data["id"],
                        ReasonType__in=["Deactivation",
                                        "Reactivation_Rejection"],
                    ).order_by("-CurrentTime")[0]
                    print(f"------eeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee{reasons}")
                s_r.update(
                    {
                        "approval": "Deactivated",
                        "deactivated_reason": reasons.Reason,
                        "deactivated_time": reasons.CurrentTime,
                        "deactivated_category": reasons.ReasonCategory,
                    }
                )
                user_data["approval_status_reason"] = s_r

            elif user_data["approval"] == "Approval_requested":
                print(f"--------------checking status")
                dd = StatusReason.objects.filter(
                    ExpertId_id__exact=a.data["id"],
                    ReasonCategory__exact="user_reactivation",
                    ReasonType__exact="Reactivation",
                )
                if dd.exists():
                    dd = dd.order_by("-CurrentTime")[0]
                else:
                    dd = "na"
                if dd != "na" and timezone.localtime(
                    dd.CurrentTime, timezone.get_current_timezone()
                ) > timezone.localtime(
                    parse_datetime(
                        user_data["doctor_other_details"]["ReasonDate"]),
                    timezone.get_current_timezone(),
                ):
                    print(f"--------------checking status")
                    reason = StatusReason.objects.filter(
                        ExpertId_id__exact=a.data["id"],
                        ReasonCategory__exact="user_reactivation",
                        ReasonType__exact="Reactivation",
                    ).order_by("-CurrentTime")[0]
                    s_r.update(
                        {
                            "approval": "Approval_requested",
                            "self_reactivation_reason": reason.Reason,
                            "self_reactivation_time": reason.CurrentTime,
                        }
                    )
                    user_data["approval_status_reason"] = s_r
                elif (
                    user_data["doctor_other_details"]["Reason"] is not None
                    and user_data["doctor_other_details"]["Reason"][1] == "reactivation"
                ):
                    s_r.update(
                        {
                            "approval": "Approval_requested",
                            "reactivation_reason": user_data["doctor_other_details"][
                                "Reason"
                            ][0],
                            "reactivation_time": user_data["doctor_other_details"][
                                "ReasonDate"
                            ],
                        }
                    )
                    user_data["approval_status_reason"] = s_r
                elif (
                    user_data["doctor_other_details"]["Reason"] is not None
                    and user_data["doctor_other_details"]["Reason"][1] == "resubmission"
                ):
                    s_r.update(
                        {
                            "approval": "Approval_requested",
                            "resubmission_reason": user_data["doctor_other_details"][
                                "Reason"
                            ][0],
                            "resubmission_time": user_data["doctor_other_details"][
                                "ReasonDate"
                            ],
                        }
                    )
                    user_data["approval_status_reason"] = s_r
                else:
                    pass
            elif user_data["approval"] == "self_deactivation":
                dd = StatusReason.objects.filter(
                    ExpertId_id__exact=a.data["id"],
                    ReasonType__exact="Deactivation",
                    ReasonCategory__exact="user_deactivation"
                ).order_by("-CurrentTime")

                if dd.exists():
                    latest_reason = dd[0]
                    s_r.update(
                        {
                            "approval": "self_deactivation",
                            "self_deactivation_reason": latest_reason.Reason,
                            "self_deactivation_time": latest_reason.CurrentTime,
                            "self_deactivation_category": latest_reason.ReasonCategory,
                        }
                    )
                else:
                    s_r.update(
                        {
                            "approval": "self_deactivation",
                            "self_deactivation_reason": "",
                            "self_deactivation_time": "",
                        }
                    )
                user_data["approval_status_reason"] = s_r

            # elif user_data["approval"] == "self_deactivation":
            #     dd = StatusReason.objects.filter(
            #         ExpertId_id__exact=a.data["id"],
            #         ReasonType__exact="Self_Reactivation_Rejection",
            #     )
            #     if dd.exists() and timezone.localtime(
            #         dd.order_by("-CurrentTime")[0].CurrentTime,
            #         timezone.get_current_timezone(),
            #     ) > timezone.localtime(
            #         parse_datetime(
            #             user_data["doctor_other_details"]["DateOfActivation"]
            #         ),
            #         timezone.get_current_timezone(),
            #     ):
            #         reason = StatusReason.objects.filter(
            #             ExpertId_id__exact=a.data["id"],
            #             ReasonType__exact="Self_Reactivation_Rejection",
            #         ).order_by("-CurrentTime")[0]
            #         s_r.update(
            #             {
            #                 "approval": "self_deactivation",
            #                 "self_reactivation_rejection_reason": reason.Reason,
            #                 "self_reactivation_rejection_time": reason.CurrentTime,
            #                 "self_reactivation_rejected_category": reason.ReasonCategory,
            #             }
            #         )
            #         user_data["approval_status_reason"] = s_r

            #     else:
            #         s_r.update(
            #             {
            #                 "approval": "self_deactivation",
            #                 "self_deactivation_reason": "",
            #                 "self_deactivation_time": "",
            #             }
            #         )
            #         user_data["approval_status_reason"] = s_r

            elif user_data["approval"] == "self_deletion":
                dd = StatusReason.objects.filter(
                    ExpertId_id__exact=a.data["id"],
                    ReasonType__exact="Deletion",
                    ReasonCategory__exact="user_deletion"
                ).order_by("-CurrentTime")

                if dd.exists():
                    latest_reason = dd[0]
                    s_r.update(
                        {
                            "approval": "self_deletion",
                            "self_deletion_reason": latest_reason.Reason,
                            "self_deletion_time": latest_reason.CurrentTime,
                            "self_deletion_category": latest_reason.ReasonCategory,
                        }
                    )
                else:
                    s_r.update(
                        {
                            "approval": "self_deletion",
                            "self_deletion_reason": "",
                            "self_deletion_time": "",
                        }
                    )
                user_data["approval_status_reason"] = s_r
            else:
                pass

            # update s3 urls ends

            p_data1 = []
            for x in user_data["expertise"]:
                p_data1.append(
                    serialize_model(
                        ExpertiseCancertype.objects.filter(
                            id__exact=x["id"])[0],
                        ExpertiseCancertypeSerializer,
                    )
                )

            user_data["expertise"] = p_data1
            # added
            Apps = Appointments.objects.filter(
                slot_id__doctor__exact=a.data["id"])
            doctors_s = CuUser.objects.filter(id__exact=a.data["id"])
            stories_count = 0
            reviews_count = 0
            rating_s = 0
            rating_r = 0
            for z in Apps:
                stories = z.patientstories_set
                stories_count += stories.count()
                for v in stories.all():
                    rating_s += v.Rating
            for z in doctors_s:
                reviews = z.doctorreviews_set
                for u in reviews.all():
                    if u.ReviewStatus == 2:
                        rating_r += u.ReviewRating
                        reviews_count += 1
            rating_in_number1 = (
                int(rating_s / stories_count) if stories_count != 0 else 0
            )
            rating_in_number2 = (
                int(rating_r / reviews_count) if reviews_count != 0 else 0
            )
            rating_in_number = int(rating_in_number1 + rating_in_number2) / 2
            user_data["expert_rating"] = rating_in_number
            # added
        elif user_data["role"] in ["patient"]:

            print(
                f"patient dattttttttttttttttttttt{user_details.get_all_permissions()}-----------{user_data['patient_other_details']['ProfilePhoto']}"
            )
            if user_data["patient_other_details"]["ProfilePhoto"] is not None:
                # update s3 urls of patient
                new_obj_url = get_s3_signed_url_bykey(
                    user_data["patient_other_details"]["ProfilePhoto"]
                )
                user_data["patient_other_details"]["ProfilePhoto"] = new_obj_url

            if user_data["patient_other_details"]["Signature"] is not None:

                new_obj_url = get_s3_signed_url_bykey(
                    user_data["patient_other_details"]["Signature"]
                )
                user_data["patient_other_details"]["Signature"] = new_obj_url
            # added for patient reactivation request
            if user_data["approval"] == "Deactivated":
                dd = StatusReason.objects.filter(
                    ExpertId_id__exact=a.data["id"],
                    ReasonType__in=["Deactivation", "Reactivation_Rejection"],
                )
                if dd.exists():
                    dd = dd.order_by("-CurrentTime")[0]
                    s_r.update(
                        {
                            "approval": "Deactivated",
                            "deactivated_reason": dd.Reason,
                            "deactivated_time": dd.CurrentTime,
                            "deactivated_category": dd.ReasonCategory,
                        }
                    )
                    user_data["approval_status_reason"] = s_r
            elif user_data["approval"] == "Approval_requested":
                dd = StatusReason.objects.filter(
                    ExpertId_id__exact=a.data["id"], ReasonType__exact="Reactivation"
                )
                if dd.exists():
                    dd = dd.order_by("-CurrentTime")[0]
                    s_r.update(
                        {
                            "approval": "Approval_requested",
                            "reactivation_reason": dd.Reason,
                            "reactivation_time": dd.CurrentTime,
                        }
                    )
                    user_data["approval_status_reason"] = s_r
            else:
                pass

        elif user_data["role"] in ["child_admin"]:
            user_details = get_cu_user(a.data["id"])
            print(
                f"user permissionssss----------{user_details.get_all_permissions()}---------{user_details.has_perm('cu_app.perm1_code')}"
            )
            # added
            if user_data["admin_other_details"]["ProfilePhoto"] is not None:
                # update s3 urls of patient
                new_obj_url = get_s3_signed_url_bykey(
                    user_data["admin_other_details"]["ProfilePhoto"]
                )
                user_data["admin_other_details"]["ProfilePhoto"] = new_obj_url
            user_data.update(
                {
                    "permissions": json.loads(
                        json.dumps(list((user_details.get_all_permissions())))
                    )
                }
            )
        elif user_data["role"] in ["admin"]:
            user_details = get_cu_user(a.data["id"])
            if user_data["admin_other_details"]["ProfilePhoto"] is not None:
                # update s3 urls of patient
                new_obj_url = get_s3_signed_url_bykey(
                    user_data["admin_other_details"]["ProfilePhoto"]
                )
                user_data["admin_other_details"]["ProfilePhoto"] = new_obj_url
            user_data.update(
                {
                    "permissions": json.loads(
                        json.dumps(list((user_details.get_all_permissions())))
                    )
                }
            )
            # added

        else:
            pass
        # b = SendPushNotification(a.data['id'])
        return JsonResponse({"user_data": user_data})


class GetMedicalRecord(generics.RetrieveAPIView):
    queryset = patient_medical_records.objects.all()
    serializer_class = patient_medical_recordsSerializer
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        print(f"med rec request--{request}----{args}---{kwargs['id']}")
        a = self.retrieve(request, *args, **kwargs)
        print(f"med rec---{a.data}--{type(a.data)}")
        # check for valid url
        if a.data["report_file"] is not None:
            # new_obj_url = get_s3_signed_url_bykey(a.data['report_file'])
            # a.data['report_file']=new_obj_url
            r_file = a.data["report_file"]
            r_f = []
            for i in r_file:
                new_obj_url = get_s3_signed_url_bykey(i)
                print(new_obj_url)
                r_f.append(new_obj_url)
            a.data["report_file"] = r_f

        return a


class TestJsonRet(generics.RetrieveAPIView):
    queryset = testjson.objects.all()
    serializer_class = testjsonSerializer
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        print(f"test json request--{request}----{args}---{kwargs['id']}")
        a = self.retrieve(request, *args, **kwargs)
        print(
            f"test json request122342---{a.data['test_data']}--{type(a.data['test_data'])}"
        )
        return a


@method_decorator(csrf_exempt, name="dispatch")
class SendGridTest(View):
    def post(self, r):
        print(f"keyyyyyyyyyyy--{settings.SENDGRID_API_KEY}")
        sg = sendgrid.SendGridAPIClient(api_key=settings.SENDGRID_API_KEY)
        from_email = Email("<EMAIL>")
        to_email = To("<EMAIL>")
        subject = "Sending with SendGrid is Fun"
        content = Content(
            "text/plain", "and easy to do anywhere, even with Python")
        mail = Mail(from_email, to_email, subject, content)
        try:
            response = sg.client.mail.send.post(request_body=mail.get())
            print(response.status_code)
            print(response.body)
            print(response.headers)
        except HTTPError as e:
            print(e.to_dict)


def cu_sendmail(r, u_name, u_email, code, email_sub, host):
    #     print(f"reg send mail--{os.environ.get('SENDGRID_API_KEY')}")
    #     sg = sendgrid.SendGridAPIClient(api_key=os.environ.get('SENDGRID_API_KEY'))
    #     print(f"reg send mail--{settings.SENDGRID_API_KEY}")
    #     sg = sendgrid.SendGridAPIClient(api_key=settings.SENDGRID_API_KEY)
    #
    #     from_email = Email("<EMAIL>")
    #     to_email = To(u_email)
    #     subject = email_sub

    #     verify_url=f'''Dear {u_email},
    #
    # Welcome to Cancer Unwired – a dedicated space for empowering connections between cancer specialists and patients.
    #
    # To ensure the security of your account and to complete your registration, please verify your email address by clicking on the link below:
    # {verify_url1}
    # This link will remain active for 24 hours. If you did not initiate this request, please ignore this email or contact our support team.
    #
    # Once verified, you will have full access to consult and connect with leading cancer care specialists, manage appointments, and receive personalized care online.
    #
    # Thank you for joining our community of care.
    #
    # Warm regards,
    # The Cancer Unwired Team
    #
    # '''
    #     content = Content("text/plain", verify_url)
    #     mail = Mail(from_email, to_email, subject, content)
    # zeptomail added
    verify_url1 = host + "/email-verification/?verify_code=" + code
    payload = {
        "template_key": "2518b.41c485fda42f6e5f.k1.ec531080-dc5d-11ee-96f3-52540038fbba.18e180f6188",
        # "bounce_address": "<EMAIL>",
        "from": {"address": "<EMAIL>", "name": "Health Unwired"},
        "to": [{"email_address": {"address": u_email, "name": u_email}}],
        "merge_info": {
            "u_name": u_name.capitalize(),
            "u_email": u_email,
            "verify_url1": verify_url1,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }

    headers = {
        "Authorization": f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"

    try:
        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload)
        )
        print(
            f"verify mail send-----------{response.status_code}-------{response.content}--------"
        )
        response_status = (
            True
            if response.status_code == 200
            else True if response.status_code in [201, 202] else False
        )
        return response_status
    except HTTPError as e:
        print(f"exception-----------{e.to_dict}")
        return response_status


def verify_email(gen_time, purpose="password"):
    # expiry_time = timezone.localtime(
    #     gen_time, timezone.get_current_timezone()
    # ) + timedelta(minutes=5)
    # is_expired = (
    #     timezone.localtime(
    #         timezone.now(), timezone.get_current_timezone()).timestamp()
    #     >= expiry_time.timestamp()
    # )
    # return not is_expired
    expiry_duration = timedelta(
        minutes=5) if purpose == "password" else timedelta(hours=24)

    expiry_time = timezone.localtime(
        gen_time, timezone.get_current_timezone()) + expiry_duration
    current_time = timezone.localtime(
        timezone.now(), timezone.get_current_timezone())

    if current_time >= expiry_time:
        return "Your verification link has expired. Please request a new one."

    return "Valid"


@method_decorator(csrf_exempt, name="dispatch")
class UserVerifyEmail(View):
    def post(self, request, *args, **kwargs):
        try:
            u_data = json.loads(request.body.decode("utf-8"))
        except json.JSONDecodeError:
            return JsonResponse({"message": "Invalid JSON format"}, status=400)

        user_id = u_data.get("user_id")
        code = u_data.get("code")

        if not user_id or not code:
            return JsonResponse({"message": "User ID and verification code are required"}, status=400)

        try:
            user_type = get_cu_user_type(user_id)
            user_obj = get_user_model().objects.get(id=user_id)
        except get_user_model().DoesNotExist:
            return JsonResponse({"message": "User not found"}, status=404)

        match_obj = None

        try:
            if user_type in ["doctor", "researcher", "influencer"]:
                match_obj = DoctorDetails.objects.filter(
                    DoctorId=user_obj,
                    EmailVerifyCode=code
                ).first()

            elif user_type == "patient":
                match_obj = PatientDetails.objects.filter(
                    PatientId=user_obj,
                    EmailVerifyCode=code
                ).first()

            if not match_obj:
                return JsonResponse({"message": "Verification code doesn't match"}, status=400)

            gen_time = match_obj.EmailCodeGentime
            res = verify_email(gen_time, purpose="email")

            if res == "Valid":
                match_obj.EmailVerified = True
                match_obj.save()
                return JsonResponse({"message": "Email successfully verified"})
            else:
                return JsonResponse({"message": "Your verification link has expired. Please request a new one."}, status=400)

        except Exception as e:
            return JsonResponse({"message": f"An error occurred: {str(e)}"}, status=500)


@method_decorator(csrf_exempt, name="dispatch")
class UserVerifyEmailReq(View):
    def post(self, request, *args, **kwargs):
        try:
            u_data = json.loads(request.body.decode("utf-8"))
        except json.JSONDecodeError:
            return JsonResponse({"message": "Invalid JSON format"}, status=400)

        user_id = u_data.get("user_id")

        if not user_id:
            return JsonResponse({"message": "User ID is required"}, status=400)

        try:
            user_type = get_cu_user_type(user_id)
            match_user_obj = get_user_model().objects.get(id=user_id)
        except get_user_model().DoesNotExist:
            return JsonResponse({"message": "User not found"}, status=404)

        v_code = "".join(random.choice("**********ABCDEF") for _ in range(16))

        try:
            if user_type in ["doctor", "researcher", "influencer"]:
                mail_res = cu_sendmail(
                    request,
                    match_user_obj.name,
                    match_user_obj.email,
                    v_code,
                    "Verify Your Email to Complete Your Health Unwired Registration",
                    settings.NEXT_CANCER_UNWIRED_DOCTOR_APP
                )

                if mail_res:
                    match_obj = match_user_obj.doctordetails
                    match_obj.EmailVerifyCode = v_code
                    match_obj.EmailCodeGentime = timezone.now()
                    match_obj.save()
                    return JsonResponse({"message": "Verification email sent"})

            elif user_type == "patient":
                mail_res = cu_sendmail(
                    request,
                    match_user_obj.name,
                    match_user_obj.email,
                    v_code,
                    "Health Unwired Email Verification Link",
                    settings.NEXT_CANCER_UNWIRED_PATIENT_APP
                )

                if mail_res:
                    match_obj = match_user_obj.patientdetails
                    match_obj.EmailVerifyCode = v_code
                    match_obj.EmailCodeGentime = timezone.now()
                    match_obj.save()
                    return JsonResponse({"message": "Verification email sent"})

            return JsonResponse({"message": "Verification email could not be sent"}, status=500)

        except Exception as e:
            return JsonResponse({"message": f"An error occurred: {str(e)}"}, status=500)


def CuSendOTP(phone, message, c_code):
    sns_client = boto3.client(
        "sns",
        aws_access_key_id=settings.AWS_ACCESS_KEY_ID,
        aws_secret_access_key=settings.AWS_SECRET_ACCESS_KEY,
        region_name=settings.AWS_REGION,
    )

    sns_res = sns_client.publish(
        # PhoneNumber="+91"+phone,
        PhoneNumber=c_code + phone,
        Message=message,
    )

    print("resss----", sns_res)
    return sns_res


class AppointmentConsentView(generics.CreateAPIView):
    serializer_class = AppointmentConsentSerializer

    def create(self, request, *args, **kwargs):
        a = get_cu_user_type(request.data["UserId"])
        appobj = Appointments.objects.filter(
            id__exact=request.data["AppId"])[0]
        p_id = Appointments.objects.filter(id__exact=request.data["AppId"])[
            0
        ].patient.id
        e_id = Appointments.objects.filter(id__exact=request.data["AppId"])[
            0
        ].slot_id.doctor.id
        appid, created = AppointmentConsent.objects.get_or_create(AppId=appobj)

        if a in ["doctor", "researcher", "influencer"]:

            if e_id == int(request.data["UserId"]) and "ExpertConsent" in request.data:

                appid.ExpertConsent = request.data["ExpertConsent"]
                appid.save()

            else:
                return ["invalid user", "failed"]

        elif a == "patient":

            if (
                p_id == int(request.data["UserId"])
                and "PatientConsent" in request.data
                and "PatientConsent_DoctorForm" in request.data
            ):

                appid.PatientConsent = request.data["PatientConsent"]
                appid.PatientConsent_DoctorForm = request.data[
                    "PatientConsent_DoctorForm"
                ]
                appid.save()
            else:
                return ["invalid user", "failed"]

        else:
            return ["invalid user role", "failed"]

        return ["success", "success"]

    def post(self, request, *args, **kwargs):
        res1 = self.create(request, *args, **kwargs)

        return JsonResponse({"consent status": res1[0], "message": res1[1]})


class PricingView(generics.ListCreateAPIView):
    serializer_class = PricingSerializer
    queryset = Pricing.objects.all()

    def create(self, request, *args, **kwargs):
        res = Pricing.objects.all().exists()

        if not res:
            return super().create(request, *args, **kwargs)
        else:
            a = Pricing.objects.all().first()
            a.PlatformCharges = request.data["PlatformCharges"]
            a.TransactionCharges = request.data["TransactionCharges"]
            a.save()
            return a

    def post(self, request, *args, **kwargs):
        result1 = self.create(request, *args, **kwargs)

        if isinstance(result1, Response):
            return result1
        else:
            return Response(serialize_model(result1, PricingSerializer))

    def get(self, request, *args, **kwargs):
        res1 = self.list(request, *args, **kwargs)

        return res1


def cu_sendpwmail(r, u_email, code, email_sub, host, u_name):

    #     sg = sendgrid.SendGridAPIClient(api_key=settings.SENDGRID_API_KEY)
    #
    #     from_email = Email("<EMAIL>")
    #     to_email = To(u_email)
    #     subject = email_sub
    #     verify_url1=host+"/pw-reset/?verify_code="+code+"&email="+u_email
    #     verify_url=f'''Dear {u_email},
    #
    # Welcome to Cancer Unwired – a dedicated space for empowering connections between cancer specialists and patients.
    #
    # Please reset your password by clicking on the link below:
    # {verify_url1}
    # This link will remain active for 2 mins. If you did not initiate this request, please ignore this email or contact our support team.
    #
    #
    # Warm regards,
    # The Cancer Unwired Team
    #
    # '''
    #     content = Content("text/plain", verify_url)
    #     mail = Mail(from_email, to_email, subject, content)
    # zeptomail added
    verify_url1 = host + "/pw-reset/?verify_code=" + code + "&email=" + u_email
    payload = {
        "template_key": "2518b.41c485fda42f6e5f.k1.d019c470-e2bd-11ee-adf0-525400674725.18e41d62237",
        # "bounce_address": "<EMAIL>",
        "from": {"address": "<EMAIL>", "name": "Health Unwired"},
        "to": [{"email_address": {"address": u_email, "name": u_email}}],
        "merge_info": {"u_name": u_name,
                       "verify_url1": verify_url1,
                       "fb_url": os.getenv("fb_url"),
                       "insta_url": os.getenv("insta_url"),
                       "twitter_url": os.getenv("twitter_url"),
                       "linkedin_url": os.getenv("linkedin_url"),
                       "youtube_url": os.getenv("youtube_url"), },
    }

    headers = {
        "Authorization": f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
    try:
        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload)
        )
        print(
            f"pw mail send-----------{response.status_code}-------{response.content}--------"
        )
        response_status = (
            True
            if response.status_code == 200
            else True if response.status_code in [201, 202] else False
        )
        return response_status
    except HTTPError as e:
        print(f"exception-----------{e.to_dict}")
        return response_status


# class PasswordResetLinkView(generics.UpdateAPIView):
#     permission_classes = []
#     queryset = get_user_model().objects.all()
#     lookup_field = "email"
#     serializer_class = CuUserSerializer

#     def partial_update(self, request, *args, **kwargs):

#         pw_code = "".join(random.choice("**********ABCDEF") for i in range(16))
#         instance = self.get_object()
#         user_role = get_cu_user_type(instance.id)

#         if user_role in ["patient"]:
#             app_url = os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP")
#         elif user_role in ["doctor", "researcher", "influencer"]:
#             app_url = os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP")
#         # added
#         elif user_role in ["admin", "child_admin"]:
#             app_url = os.getenv("NEXT_CANCER_UNWIRED_ADMIN_APP")
#         # added
#         else:
#             app_url = ""

#         mail_res = cu_sendpwmail(
#             request,
#             instance.email,
#             pw_code,
#             "CancerUnwired Password Reset Link",
#             app_url,
#             instance.name,
#         )

#         if mail_res:

#             instance.PWVerifyCode = pw_code
#             # instance.PWCodeGentime = datetime.now()
#             instance.PWCodeGentime = timezone.now()
#             instance.save()
#             return super().partial_update(request, *args, **kwargs)
#         else:
#             return "something went wrong!!pls try again"

#     def put(self, request, *args, **kwargs):
#         a = self.partial_update(request, *args, **kwargs)
#         if isinstance(a, Response):
#             return JsonResponse({"message": "Password reset link sent"})
#         else:
#             return JsonResponse({"message": a})


class PasswordResetLinkView(generics.UpdateAPIView):
    permission_classes = []
    queryset = get_user_model().objects.all()
    lookup_field = "email"
    serializer_class = CuUserSerializer

    def get_object(self):
        email = self.kwargs.get(self.lookup_field)
        user = self.queryset.filter(email=email).first()
        return user

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        if not instance:
            return Response(
                {"message": "User with this email does not exist."},
                status=status.HTTP_400_BAD_REQUEST
            )

        pw_code = "".join(random.choice("**********ABCDEF") for _ in range(16))
        user_role = get_cu_user_type(instance.id)

        if user_role in ["patient"]:
            app_url = os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP")
        elif user_role in ["doctor", "researcher", "influencer"]:
            app_url = os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP")
        elif user_role in ["admin", "child_admin"]:
            app_url = os.getenv("NEXT_CANCER_UNWIRED_ADMIN_APP")
        else:
            app_url = ""

        mail_res = cu_sendpwmail(
            request,
            instance.email,
            pw_code,
            "CancerUnwired Password Reset Link",
            app_url,
            instance.name,
        )

        if mail_res:
            instance.PWVerifyCode = pw_code
            instance.PWCodeGentime = timezone.now()
            instance.save()
            return Response({"message": "Password reset link sent"})
        else:
            return Response(
                {"message": "Something went wrong. Please try again."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def put(self, request, *args, **kwargs):
        return self.partial_update(request, *args, **kwargs)


class VerifyPasswordView(generics.UpdateAPIView):
    permission_classes = []
    queryset = get_user_model().objects.all()
    lookup_field = "email"
    serializer_class = CuUserSerializer

    def get_object(self):
        email = self.kwargs.get("email")  # Get the email from the request
        try:
            return get_user_model().objects.get(email=email)
        except get_user_model().DoesNotExist:
            raise NotFound(
                "The provided email is not registered in our system.")

    def partial_update(self, request, *args, **kwargs):
        try:
            instance = self.get_object()
        except NotFound as e:
            return str(e)

        if instance.PWVerifyCode == request.data["code"]:

            res = verify_email(instance.PWCodeGentime, purpose="password")

            if res == 'Valid':
                new_password = request.data["pw"]
                if check_password(new_password, instance.password):
                    return "You have entered your previous password. Please enter a new password."
                instance.set_password(new_password)

                instance.PWVerifyCode = None
                instance.PWCodeGentime = None
                instance.save()
                return super().partial_update(request, *args, **kwargs)
            else:
                return "Your password reset link has expired. Please request a new one."
        else:
            return "invalid password reset code"

    def put(self, request, *args, **kwargs):
        a = self.partial_update(request, *args, **kwargs)

        if isinstance(a, Response):

            return JsonResponse({"message": "password reset successfull"})
        else:
            return JsonResponse({"message": a})


def RefreshZohoAccessToken():
    deskurl = (
        "https://accounts.zoho.in/oauth/v2/token?refresh_token="
        + os.getenv("ZOHO_REFRESH_TOKEN")
        + "&grant_type=refresh_token&client_id="
        + os.getenv("ZOHODESK_CLIENT_ID")
        + "&client_secret="
        + os.getenv("ZOHODESK_CLIENT_SECRET")
        + "&redirect_uri=https://www.zylker.com/oauthgrant&scope=Desk.tickets.READ"
    )
    r = requests.post(deskurl)
    a = r.json()
    return a['access_token']


class IsCuAdminPermission(permissions.BasePermission):

    def has_permission(self, request, view):
        user_id = request.GET["user_id"]
        is_cu_admin = (
            get_user_model()
            .objects.filter(id__exact=user_id, groups__name="admin")
            .exists()
        )
        return is_cu_admin


def get_s3_attachment_url(url, headers):
    response = requests.get(url, headers=headers, stream=True)
    my_config = Config(
        region_name="ap-south-1",
        signature_version="v4",
    )
    s3_client = boto3.client(
        "s3",
        aws_access_key_id=os.getenv("AWS_ACCESS_KEY_ID"),
        aws_secret_access_key=os.getenv("AWS_SECRET_ACCESS_KEY"),
        region_name=os.getenv("AWS_REGION"),
        config=my_config,
    )
    if response.status_code == 200:
        # Determine file extension dynamically
        content_disposition = response.headers.get("Content-Disposition")
        file_extension = None

        # Extract extension from Content-Disposition if present
        if content_disposition:
            filename = content_disposition.split("filename=")[-1].strip('"')
            file_extension = os.path.splitext(filename)[
                -1
            ]  # Extract the extension (e.g., '.pdf')

        # Fallback to Content-Type if no extension is found
        if not file_extension:
            content_type = response.headers.get("Content-Type")
            if content_type:
                # e.g., 'application/pdf' -> '.pdf'
                file_extension = f".{content_type.split('/')[-1]}"

        # Default to a generic extension if none found
        if not file_extension:
            file_extension = ".bin"

        # Construct the dynamic file name
        FILE_ID = url.split("/")[-2]
        file_name = f"{FILE_ID}{file_extension}"
        file_key = file_name
        try:
            s3_client.head_object(Bucket="cuapp-files", Key=file_key)
            a_url = f"https://cuapp-files.s3.ap-south-1.amazonaws.com/{file_key}"
            print(
                "---------------------------------------key already exists-------",
                a_url,
            )
            return a_url
        except ClientError as e:
            s3_client.put_object(
                Bucket="cuapp-files", Key=file_key, Body=response.content
            )
            a_url = f"https://cuapp-files.s3.ap-south-1.amazonaws.com/{file_key}"
            print(a_url)
            return a_url

    else:
        return None


class PatientTicketsView(views.APIView):
    # permission_classes = [IsCuAdminPermission]

    def get(self, request, email, status):
        print(f"emailllllll{email}")
        a1 = ""
        deskurl = "https://desk.zoho.in/api/v1/tickets?include=contacts,assignee,departments,team&limit=100"

        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "orgId": os.getenv("ZOHODESK_ORG_ID"),
            "Authorization": "Bearer " + os.getenv("ZOHO_ACCESS_TOKEN"),
        }
        r = requests.get(deskurl, headers=headers)
        if r.status_code == 401:
            a = RefreshZohoAccessToken()
            a1 = a
            headers["Authorization"] = "Bearer " + a
            r = requests.get(deskurl, headers=headers)
        a = r.json()

        # list1=[x for x in a['data'] if x['email']==email]
        # added
        list1 = []
        # added
        o_t = 0
        c_t = 0
        h_t = 0
        e_tt = 0
        for x in a["data"]:
            if x["email"] == email:
                # added
                if x["status"] == "Open":
                    o_t += 1
                elif x["status"] == "On Hold":
                    h_t += 1
                elif x["status"] == "Escalated":
                    e_tt += 1
                elif x["status"] == "Closed":
                    c_t += 1
                else:
                    pass
                # added
                list1.append(x)
        ticket_main_details = []
        print(f"----------------{list1}----------------")
        # added
        if status != "all":
            list1 = [y for y in list1 if y["status"] == status]

            print(f"----------------{list1}----------------")
        # ----------------------------------------------------------------
        ticket_main_details = []
        # Get the page number from the request
        page_number = request.GET.get("page", 1)
        # Get the number of items per page from the request
        items_per_page = request.GET.get("per_page", 10)
        total_items = len(list1)
        paginator = Paginator(list1, items_per_page)
        if int(page_number) not in range(1, int(paginator.num_pages) + 1):
            return HttpResponse("Not a valid page number", status=400)
        list1 = paginator.page(page_number)
        for x in list1:

            yourdata = []
            thread_data = []
            attach_data = []
            # add user profilephoto
            user_obj = get_user_model().objects.filter(
                email__exact=x["email"])[0]
            if get_cu_user_type(user_obj.id) == "patient":
                user_other_details = PatientDetails.objects.get(
                    PatientId__exact=user_obj.id
                )
                if user_other_details.ProfilePhoto is not None:
                    print(
                        f"patient ticketssssssssssss typeeee------------{user_other_details.ProfilePhoto}"
                    )
                    new_obj_url = get_s3_signed_url_bykey(
                        user_other_details.ProfilePhoto
                    )
                    x["ProfilePhoto"] = new_obj_url
                else:
                    x['ProfilePhoto'] = None

            elif get_cu_user_type(user_obj.id) in [
                "doctor",
                "researcher",
                "influencer",
            ]:
                user_other_details = DoctorDetails.objects.get(
                    DoctorId__exact=user_obj.id
                )
                if user_other_details.ProfilePhoto is not None:

                    # is_valid_url = requests.get(user_other_details.ProfilePhoto)
                    print(
                        f"doctor ticketssssssssssss typeeee------------{user_other_details.ProfilePhoto}"
                    )
                    new_obj_url = get_s3_signed_url_bykey(
                        user_other_details.ProfilePhoto
                    )
                    x["ProfilePhoto"] = new_obj_url
                else:
                    x['ProfilePhoto'] = None
            else:
                x["ProfilePhoto"] = None
            # add user profilephoto ends
            yourdata.append(
                {
                    "ticket_details": {
                        "id": x["id"],
                        "ticketNumber": x["ticketNumber"],
                        "email": x["email"],
                        "status": x["status"],
                        "priority": x["priority"],
                        "subject": x["subject"],
                        "profilephoto": x["ProfilePhoto"],
                    }
                }
            )

            thread_url = "https://desk.zoho.in/api/v1/tickets/" + \
                x["id"] + "/threads"
            r1 = requests.get(thread_url, headers=headers)
            if r1.status_code == 401:
                a1 = RefreshZohoAccessToken()
                headers["Authorization"] = "Bearer " + a1
                # os.environ['ZOHO_ACCESS_TOKEN']=a
                r1 = requests.get(thread_url, headers=headers)
            tt = r1.json()

            list2 = tt["data"]
            for y in list2:
                if "to" in y:
                    to_email = y["to"]
                else:
                    to_email = ""
                thread_data.append(
                    {
                        "summary": y["summary"],
                        "author_name": y["author"]["name"],
                        "author_photo": y["author"]["photoURL"],
                        "CreatedTime": y["createdTime"],
                        "to": to_email,
                    }
                )
                if y["hasAttach"] == True:
                    attach_url = thread_url + "/" + y["id"]
                    r3 = requests.get(attach_url, headers=headers)
                    if r3.status_code == 401:
                        a3 = RefreshZohoAccessToken()
                        headers["Authorization"] = "Bearer " + a3

                        r3 = requests.get(attach_url, headers=headers)
                        rr3 = r3.json()
                        for z in rr3["attachments"]:
                            s3_attachment_url = get_s3_attachment_url(
                                z["href"], headers
                            )
                            attach_data.append(
                                {"attachments": s3_attachment_url})
                            print(
                                f"-------------{z['href']}----------------------")

                    else:
                        rr3 = r3.json()
                        for z in rr3["attachments"]:
                            s3_attachment_url = get_s3_attachment_url(
                                z["href"], headers
                            )
                            attach_data.append(
                                {"attachments": s3_attachment_url})
                            print(
                                f"-------------{z['href']}----------------------")

            yourdata.append({"thread_data": thread_data})
            yourdata.append({"attachements_data": attach_data})
            # attachments ends
            ticket_main_details.append(yourdata)
        # added
        ticket_main_details.append(
            {
                "total_tickets": o_t + c_t + h_t + e_tt,
                "total_open_ticket": o_t,
                "total_closed_ticket": c_t,
                "total_on_hold_ticket": h_t,
                "total_escalated_ticket": e_tt,
            }
        )
        # added
        return Response(
            {
                "total_items": total_items,
                "total_pages": paginator.num_pages,
                "ticket_details": ticket_main_details,
                "content": a1,
            }
        )


class TicketDetailsView(views.APIView):
    def get(self, request, ticket_id):
        thread_data = []
        thread_url = "https://desk.zoho.in/api/v1/tickets/" + \
            ticket_id + "/threads"
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "orgId": os.getenv("ZOHODESK_ORG_ID"),
            "Authorization": "Bearer " + os.getenv("ZOHO_ACCESS_TOKEN"),
        }
        r1 = requests.get(thread_url, headers=headers)
        if r1.status_code == 401:
            a1 = RefreshZohoAccessToken()
            headers["Authorization"] = "Bearer " + a1
            # os.environ['ZOHO_ACCESS_TOKEN']=a
            r1 = requests.get(thread_url, headers=headers)
        tt = r1.json()

        list2 = tt["data"]
        for y in list2:
            if "to" in y:
                to_email = y["to"]
            else:
                to_email = ""
                # add user profilephoto
            user_obj = get_user_model().objects.filter(
                email__exact=y["author"]["email"])
            if user_obj.exists() and get_cu_user_type(user_obj[0].id) == "patient":
                user_obj = user_obj[0]
                user_other_details = PatientDetails.objects.get(
                    PatientId__exact=user_obj.id
                )
                if user_other_details.ProfilePhoto is not None:
                    print(
                        f"patient ticketssssssssssss typeeee------------{user_other_details.ProfilePhoto}"
                    )
                    new_obj_url = get_s3_signed_url_bykey(
                        user_other_details.ProfilePhoto
                    )
                    y["ProfilePhoto"] = new_obj_url
                    y["user_role"] = "patient"
                else:
                    y['ProfilePhoto'] = None
                    y["user_role"] = "patient"

            elif user_obj.exists() and get_cu_user_type(user_obj[0].id) in [
                "doctor",
                "researcher",
                "influencer",
            ]:
                user_obj = user_obj[0]
                user_other_details = DoctorDetails.objects.get(
                    DoctorId__exact=user_obj.id
                )
                if user_other_details.ProfilePhoto is not None:

                    # is_valid_url = requests.get(user_other_details.ProfilePhoto)
                    print(
                        f"doctor ticketssssssssssss typeeee------------{user_other_details.ProfilePhoto}"
                    )
                    new_obj_url = get_s3_signed_url_bykey(
                        user_other_details.ProfilePhoto
                    )
                    y["ProfilePhoto"] = new_obj_url
                    y["user_role"] = get_cu_user_type(user_obj.id)
                else:
                    y['ProfilePhoto'] = None
                    y["user_role"] = get_cu_user_type(user_obj.id)
            else:
                y["ProfilePhoto"] = None
                y["user_role"] = None
            attach_url = thread_url + "/" + y["id"]+"?include=plainText"
            r3 = requests.get(attach_url, headers=headers)
            if r3.status_code == 401:
                a3 = RefreshZohoAccessToken()
                headers["Authorization"] = "Bearer " + a3

                r3 = requests.get(attach_url, headers=headers)
            rrr3 = r3.json()
            print(f"------rr3", rrr3)
            print("This is plainText:", rrr3['plainText'])
            print("This is content:", rrr3['content'])
            print("This is sumary:", rrr3['summary'])
            y['summary'] = rrr3['plainText']
            if y["hasAttach"] == True:
                attach_url = thread_url + "/" + y["id"]
                r3 = requests.get(attach_url, headers=headers)
                if r3.status_code == 401:
                    a3 = RefreshZohoAccessToken()
                    headers["Authorization"] = "Bearer " + a3

                    r3 = requests.get(attach_url, headers=headers)
                    rr3 = r3.json()
                    attachment_url = []
                    for z in rr3["attachments"]:
                        attachment_url.append(get_s3_attachment_url(
                            z["href"], headers
                        ))
                    thread_data.append(
                        {"id": y["id"],
                         "email": y["author"]["email"],
                         "status": y["status"],
                         "profilephoto": y["ProfilePhoto"],
                         "user_role": y["user_role"],
                         "summary": rrr3["plainText"],
                         "author_name": y["author"]["name"],
                         "author_photo": y["author"]["photoURL"],
                         "CreatedTime": y["createdTime"],
                         "to": to_email,
                         "attachments": attachment_url})
                    print(
                        f"-------------{attachment_url}----------------------")

                else:
                    rr3 = r3.json()
                    attachment_url = []
                    for z in rr3["attachments"]:
                        attachment_url.append(get_s3_attachment_url(
                            z["href"], headers
                        ))
                    thread_data.append(
                        {"id": y["id"],
                         "email": y["author"]["email"],
                         "status": y["status"],
                         "profilephoto": y["ProfilePhoto"],
                         "user_role": y["user_role"],
                         "summary": rrr3["plainText"],
                         "author_name": y["author"]["name"],
                         "author_photo": y["author"]["photoURL"],
                         "CreatedTime": y["createdTime"],
                         "to": to_email,
                            "attachments": attachment_url})
                    print(
                        f"-------------{attachment_url}----------------------")

    # added
        return JsonResponse({"ticket_data": thread_data})


class AllTicketsView(views.APIView):
    # permission_classes=[IsCuAdminPermission]
    def get(self, request, user_role, status):
        print(
            f"time testttttttttttttttt{timezone.now()}---------------{timezone.get_current_timezone_name()}-----------{timezone.localtime(timezone.now(), timezone.get_current_timezone())}"
        )
        deskurl = "https://desk.zoho.in/api/v1/tickets?include=contacts,assignee,departments,team&limit=100"
        a1 = os.getenv("ZOHO_ACCESS_TOKEN")
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "orgId": os.getenv("ZOHODESK_ORG_ID"),
            "Authorization": "Bearer " + os.getenv("ZOHO_ACCESS_TOKEN"),
        }
        r = requests.get(deskurl, headers=headers)
        if r.status_code == 401:
            a = RefreshZohoAccessToken()
            a1 = a
            headers["Authorization"] = "Bearer " + a
            r = requests.get(deskurl, headers=headers)
        a = r.json()

        all_users = get_user_model().objects.all().values_list("email", flat=True)

        list1 = []
        # added
        o_t = 0
        c_t = 0
        h_t = 0
        e_tt = 0
        for x in a["data"]:
            if x["email"] in all_users:

                urole = get_cu_user_type(
                    get_user_model().objects.get(email__exact=x["email"]).id
                )
                print(
                    f"ticket emaillllllllllllllll{x['email']}--------------{urole}")
                if urole == user_role:
                    # added
                    if x["status"] == "Open":
                        o_t += 1
                    elif x["status"] == "On Hold":
                        h_t += 1
                    elif x["status"] == "Escalated":
                        e_tt += 1
                    elif x["status"] == "Closed":
                        c_t += 1
                    else:
                        pass
                    # added
                    list1.append(x)
        ticket_main_details = []
        print(f"----------------{list1}----------------")
        # added
        if status != "all":
            list1 = [y for y in list1 if y["status"] == status]

        print(f"----------------{list1}----------------")
        # ----------------------------------------------------------------
        # Get the page number from the request
        page_number = request.GET.get("page", 1)
        # Get the number of items per page from the request
        items_per_page = request.GET.get("per_page", 10)
        total_items = len(list1)
        paginator = Paginator(list1, items_per_page)
        if int(page_number) not in range(1, int(paginator.num_pages) + 1):
            return HttpResponse("Not a valid page number", status=400)
        list1 = paginator.page(page_number)
        for x in list1:

            yourdata = []
            thread_data = []
            # add user profilephoto
            user_obj = get_user_model().objects.filter(
                email__exact=x["email"])[0]
            if get_cu_user_type(user_obj.id) == "patient":
                user_other_details = PatientDetails.objects.get(
                    PatientId__exact=user_obj.id
                )
                if user_other_details.ProfilePhoto is not None:

                    # is_valid_url = requests.get(user_other_details.ProfilePhoto)
                    print(
                        f"patient all ticketssssssssssss typeeee------------{user_other_details.ProfilePhoto}"
                    )
                    new_obj_url = get_s3_signed_url_bykey(
                        user_other_details.ProfilePhoto
                    )
                    x["ProfilePhoto"] = new_obj_url
                else:
                    x['ProfilePhoto'] = None

            elif get_cu_user_type(user_obj.id) in [
                "doctor",
                "researcher",
                "influencer",
            ]:
                user_other_details = DoctorDetails.objects.get(
                    DoctorId__exact=user_obj.id
                )
                if user_other_details.ProfilePhoto is not None:

                    # is_valid_url = requests.get(user_other_details.ProfilePhoto)
                    print(
                        f"doctor all ticketssssssssssss typeeee------------{user_other_details.ProfilePhoto}"
                    )
                    new_obj_url = get_s3_signed_url_bykey(
                        user_other_details.ProfilePhoto
                    )
                    x["ProfilePhoto"] = new_obj_url
                else:
                    x['ProfilePhoto'] = None
            else:
                x["ProfilePhoto"] = None
            # add user profilephoto ends
            print("profilePhoto-----------", x['ProfilePhoto'])
            yourdata.append(
                {
                    "ticket_details": {
                        "id": x["id"],
                        "ticketNumber": x["ticketNumber"],
                        "email": x["email"],
                        "status": x["status"],
                        "priority": x["priority"],
                        "subject": x["subject"],
                        "profilephoto": x["ProfilePhoto"],
                    }
                }
            )
            thread_url = "https://desk.zoho.in/api/v1/tickets/" + \
                x["id"] + "/threads"
            r1 = requests.get(thread_url, headers=headers)
            if r1.status_code == 401:
                a1 = RefreshZohoAccessToken()
                headers["Authorization"] = "Bearer " + a1
                # os.environ['ZOHO_ACCESS_TOKEN']=a
                r1 = requests.get(thread_url, headers=headers)
            tt = r1.json()

            list2 = tt["data"]
            for y in list2:
                if "to" in y:
                    to_email = y["to"]
                else:
                    to_email = ""
                thread_data.append(
                    {
                        "summary": y["summary"],
                        "author_name": y["author"]["name"],
                        "author_photo": y["author"]["photoURL"],
                        "CreatedTime": y["createdTime"],
                        "to": to_email,
                    }
                )
            yourdata.append({"thread_data": thread_data})
            ticket_main_details.append(yourdata)
        # added
        ticket_main_details.append(
            {
                "total_tickets": o_t + c_t + h_t + e_tt,
                "total_open_ticket": o_t,
                "total_closed_ticket": c_t,
                "total_on_hold_ticket": h_t,
                "total_escalated_ticket": e_tt,
            }
        )
        # added
        return Response(
            {
                "total_items": total_items,
                "total_pages": paginator.num_pages,
                "ticket_details": ticket_main_details,
                "content": a1,
            }
        )


@method_decorator(csrf_exempt, name="dispatch")
class S3TestView(View):
    def post(self, r):
        b = handle_uploaded_file(r.FILES["testfile"])
        f_name = b.split("/")[2]
        file_url = settings.PROJECT_ROOT + b

        try:
            a = handle_s3_uploaded_file(f_name, file_url)
            print(f"fileurllllllllll------------{a}--------")

        except ClientError as e:
            print(f"exccccc----------{e}")
            return None

        return HttpResponse("hi")


class DoctorPrescriptionDownload(generics.RetrieveAPIView):
    queryset = Prescription.objects.all()
    serializer_class = PrescriptionSerializer
    lookup_field = "id"

    def get(self, request, *args, **kwargs):

        a = self.retrieve(request, *args, **kwargs)
        medic = Prescription.objects.filter(id__exact=a.data["id"])[0]

        res_data = dict()
        app = Appointments.objects.filter(id__exact=a.data["AppointmentId"])[0]
        p_data = filter_user_data(app.patient)
        d_data = filter_user_data(app.slot_id.doctor)

        res_data["prescription_data"] = a.data

        res_data["prescription_data"]["oral_medication"] = OralMedicationSerializer(
            medic.oralmedication_set.all(), many=True
        ).data

        res_data["prescription_data"]["iv_medication"] = IVIMMedicationSerializer(
            medic.ivimmedication_set.all(), many=True
        ).data

        res_data["patient_details"] = p_data
        res_data["doctor_details"] = d_data
        res_data["appointment_details"] = serialize_model(
            app, AppointmentsSerializer)

        prescription_data = res_data["prescription_data"]
        patient_details = res_data["patient_details"]
        doctor_details = res_data["doctor_details"]

        print(f"-----------------##################{a}")
        response = HttpResponse(content_type="application/pdf")
        response["Content-Disposition"] = 'attachment; filename="file.pdf"'

        dateofpres = prescription_data["DateOfCreatingPresc"]

        name = doctor_details["name"]
        doctor = doctor_details["doctor_other_details"]
        doctor_id = doctor["DoctorId"]
        patient_id = asUnicode(
            str(res_data["prescription_data"]["id"]), enc="utf8")
        patient_name = asUnicode(str(patient_details["name"]), enc="utf8")
        patient_gender = asUnicode(str(patient_details["sex"]), enc="utf8")
        patient_age = asUnicode(str(patient_details["age"]), enc="utf8")
        appointment_id = asUnicode(
            str(res_data["prescription_data"]["AppointmentId"]))
        current_diagnosis = asUnicode(
            str(res_data["prescription_data"]["CurrentDiagnosis"]), enc="utf8"
        )
        p = canvas.Canvas(response)
        p.setFont("Helvetica-Bold", 12)

        # Adding text
        p.setFillColor("purple")
        p.drawString(10, 826, name)

        p.setFont("Helvetica", 9)
        p.setFillColor("purple")
        p.drawString(10, 814, "Dept. of cardiology")

        p.setFillColor("grey")
        p.drawString(10, 802, "MD.Licenece -12344342")
        p.drawString(10, 790, f"Doctor ID -{doctor_id}")
        p.drawString(10, 778, dateofpres)

        # image drawing

        # p.drawImage("C:\\Users\\<USER>\\Downloads\\CU_api-dev_new\\CU_api-dev_new\\logo3.jpg", 500, 810, 60, 20)
        p.drawImage(settings.MEDIA_ROOT + "/logo.png", 500, 810, 60, 20)
        p.setFont("Helvetica-Bold", 10)
        p.setFillColor("purple")
        p.drawString(500, 800, "Apollo")

        p.setFillColor("blue")
        p.setFont("Helvetica", 8)
        p.drawString(500, 790, "https://healthunwired.com/")

        p.setFillColor("grey")
        p.setLineWidth(0)
        p.line(0, 772, 600, 772)

        p.setFont("Helvetica", 10)
        p.setFillColor("purple")

        p.drawString(10, 755, "id")
        p.drawString(110, 755, "Name")
        p.drawString(230, 755, "ConsultationID")
        p.drawString(350, 755, "Sex")
        p.drawString(470, 755, "Age")

        p.setFillColor("grey")
        p.setLineWidth(0)

        #      x  y   w  h
        p.roundRect(10, 730, 90, 20, 2)
        p.roundRect(110, 730, 110, 20, 2)
        p.roundRect(230, 730, 110, 20, 2)
        p.roundRect(350, 730, 110, 20, 2)
        p.roundRect(470, 730, 110, 20, 2)

        # p.setFillColor("purple")
        # inside box
        p.setFont("Helvetica", 10)
        p.drawString(14, 734, patient_id)
        p.drawString(115, 734, patient_name)
        p.drawString(235, 734, appointment_id)
        p.drawString(355, 734, patient_gender)
        p.drawString(475, 734, patient_age)

        p.roundRect(10, 700, 90, 20, 2)
        p.setFillColor("purple")
        p.drawString(12, 705, "Current Diagnosis")
        p.roundRect(110, 700, 470, 20, 2)
        p.setFillColor("grey")
        p.setFont("Helvetica", 10)
        p.drawString(115, 703, current_diagnosis)
        p.setFont("Helvetica-Bold", 12)
        p.setFillColor("purple")
        p.drawString(10, 670, "Oral Medication")

        oral_medication = prescription_data["oral_medication"]

        iv_medication = prescription_data["iv_medication"]
        # converting ordered dict list to simple dict list
        resulting_dict_list = [dict(item) for item in oral_medication]

        resulting_dict_list1 = [dict(item) for item in iv_medication]

        # length = len(oral_medication)

        p.setFillColor("purple")
        p.setFont("Helvetica", 10)
        p.drawString(12, 650, "Sno.")
        p.drawString(90, 650, "Oral Medication")
        p.drawString(210, 650, "Dose Strength")
        p.drawString(310, 650, "Frequency")
        p.drawString(420, 650, "Duration(Days)")
        p.drawString(510, 650, "Remarks")
        # checking for dynamic addition

        x_start = 10
        x_end = 570
        # position = 640
        position1 = 610

        p.line(x_start, 640, x_end, 640)

        p.setFillColor("grey")
        p.setFont("Helvetica", 9)
        s_no = 1
        for i in resulting_dict_list:
            # print(oral_medication)

            p.line(x_start, position1, x_end, position1)
            unicode_string_id = asUnicode(str(s_no), enc="utf8")
            unicode_string_name = asUnicode(str(i["MedicineName"]), enc="utf8")
            unicode_string_dose = asUnicode(str(i["DoseStrength"]), enc="utf8")
            unicode_string_duration = asUnicode(str(i["Duration"]), enc="utf8")
            unicode_string_frequency = asUnicode(
                str(i["Frequency"]), enc="utf8")
            unicode_string_remarks = asUnicode(str(i["Remarks"]), enc="utf8")

            p.drawString(13, position1 + 8, unicode_string_id)
            p.drawString(90, position1 + 8, unicode_string_name)
            p.drawString(200, position1 + 8, unicode_string_dose)
            p.drawString(310, position1 + 8, unicode_string_frequency)
            p.drawString(420, position1 + 8, unicode_string_duration)
            p.drawString(500, position1 + 8, unicode_string_remarks)
            s_no = s_no + 1
            position1 -= 30

        p.setFont("Helvetica-Bold", 12)
        p.setFillColor("purple")
        p.drawString(10, position1 - 10, "IV/IM Medication")

        p.setFont("Helvetica", 10)
        p.setFillColor("purple")
        p.drawString(12, position1 - 30, "Sno.")
        p.drawString(90, position1 - 30, "IV Medication")
        p.drawString(210, position1 - 30, "Dose Strength")
        p.drawString(300, position1 - 30, "Mode of Administration")
        p.drawString(420, position1 - 30, "Frequency")
        p.drawString(510, position1 - 30, "Remarks")

        p.setFont("Helvetica", 9)
        p.setFillColor("grey")
        p.setLineWidth(0)

        p.line(10, position1 - 40, 570, position1 - 40)
        s_no = 1
        for i in resulting_dict_list1:

            # print(oral_medication)
            p.line(x_start, position1 - 70, x_end, position1 - 70)
            unicode_string_id = asUnicode(str(s_no), enc="utf8")
            unicode_string_name = asUnicode(str(i["MedicineName"]), enc="utf8")
            unicode_string_dose = asUnicode(str(i["DoseStrength"]), enc="utf8")
            unicode_string_mode = asUnicode(
                str(i["ModeOfAdministration"]), enc="utf8")
            unicode_string_frequency = asUnicode(
                str(i["Frequency"]), enc="utf8")
            unicode_string_remarks = asUnicode(str(i["Remarks"]), enc="utf8")

            p.drawString(13, position1 - 60, unicode_string_id)
            p.drawString(90, position1 - 60, unicode_string_name)
            p.drawString(200, position1 - 60, unicode_string_dose)
            p.drawString(300, position1 - 60, unicode_string_mode)
            p.drawString(420, position1 - 60, unicode_string_frequency)
            p.drawString(500, position1 - 60, unicode_string_remarks)
            s_no = s_no + 1
            position1 -= 30

        p.setLineWidth(0)
        p.setFillColor("grey")
        p.setFont("Helvetica", 10)
        p.roundRect(10, position1 - 140, 570, 80, 2)

        my_style = ParagraphStyle(
            "my_style",
            fontName="Helvetica",
            fontSize=10,
            spaceBefore=10,
            spaceAfter=5,
            alignment=0,  # 0: left, 1: center, 2: right, 4: justified
            leading=12,  # Line height
        )

        # Add Recommendation Section
        unicode_string_recommend = asUnicode(
            str(prescription_data["Recommendation"]), enc="utf8"
        )
        p.setFont("Helvetica-Bold", 12)
        p.setFillColor("purple")
        p.drawString(12, position1 - 80, "Recommendation")

        # Using Paragraph for proper alignment and word wrapping
        para = Paragraph(unicode_string_recommend, my_style)
        para.wrapOn(p, 550, 60)  # Adjust width and height as needed
        para.drawOn(p, 20, position1 - 110)
        # p.drawString(20, position1 - 98, unicode_string_recommend)

        unicode_string_instruction = asUnicode(
            str(prescription_data["SpecialInstructions"]), enc="utf8"
        )
        unicode_string_follow_up = asUnicode(
            str(prescription_data["FollowUp"]), enc="utf8"
        )

        p.setLineWidth(0)
        p.setFillColor("grey")
        p.setFont("Helvetica", 10)
        p.roundRect(10, position1 - 240, 575, 80, 2)

        # Special Instructions
        unicode_string_instruction = asUnicode(
            str(prescription_data["SpecialInstructions"]), enc="utf8"
        )
        p.setFont("Helvetica-Bold", 12)
        p.setFillColor("purple")
        p.drawString(12, position1 - 175, "Special Instructions")

        # Using Paragraph for proper alignment
        para = Paragraph(unicode_string_instruction, my_style)
        para.wrapOn(p, 550, 60)  # Adjust width and height as needed
        para.drawOn(p, 20, position1 - 205)

        doctor_other_details = doctor_details["doctor_other_details"]

        print(f"{doctor_other_details}")

        signature = doctor_other_details["Signature"]

        if signature is not None:

            print(f"doctor sign-------------{signature}-------------")
            new_obj_url = get_s3_signed_url_bykey(signature)
            signature = new_obj_url

        signature = asUnicode(str(signature), enc="utf-8")

        p.setFillColor("grey")
        p.setFont("Helvetica", 10)
        p.roundRect(440, position1 - 330, 140, 80, 2)
        p.setFillColor("purple")
        p.drawString(468, position1 - 320, "Doctor signature")
        p.drawImage(signature, 470, position1 - 312, 100, 60)

        p.showPage()

        # added
        e_t = asUnicode(
            str(res_data["prescription_data"]["ExistingTreatment"]), enc="utf8"
        )
        if e_t is not None:
            p.setFont("Helvetica-Bold", 12)

            # Adding text
            p.setFillColor("purple")
            p.drawString(10, 826, name)

            p.setFont("Helvetica", 9)
            p.setFillColor("purple")
            p.drawString(10, 814, "Dept. of cardiology")

            p.setFillColor("grey")
            p.drawString(10, 802, "MD.Licenece -12344342")
            p.drawString(10, 790, f"Doctor ID -{doctor_id}")
            p.drawString(10, 778, dateofpres)

            # image drawing

            # p.drawImage("C:\\Users\\<USER>\\Downloads\\CU_api-dev_new\\CU_api-dev_new\\logo3.jpg", 500, 810, 60, 20)
            p.drawImage(settings.MEDIA_ROOT + "/logo.png", 500, 810, 60, 20)
            p.setFont("Helvetica-Bold", 10)
            p.setFillColor("purple")
            p.drawString(500, 800, "Apollo")

            p.setFillColor("blue")
            p.setFont("Helvetica", 8)
            p.drawString(500, 790, "https://healthunwired.com/")

            p.setFillColor("grey")
            p.setLineWidth(0)
            p.line(0, 772, 600, 772)

            p.setFillColor("grey")
            p.setLineWidth(0)
            p.setFont("Helvetica-Bold", 12)
            p.setFillColor("purple")
            p.drawString(10, 755, "Existing Treatment")

            p.setLineWidth(0)
            p.setFillColor("grey")
            p.setFont("Helvetica", 10)
            p.setFont("Helvetica-Bold", 10)

            position1 = 725
            start = 12
            spoint = 0
            epoint = 110
            length = len(e_t)

            # print(special_instruction[spoint:epoint])

            while length >= 0:

                p.drawString(start, position1, e_t[spoint: epoint + 1])
                length -= 120
                position1 -= 10
                start = 14
                spoint = epoint + 1
                epoint = epoint + 110

            signature = asUnicode(str(signature), enc="utf-8")

            p.setFillColor("grey")
            p.setFont("Helvetica", 10)
            p.setFillColor("purple")
            p.roundRect(440, position1 - 330, 140, 80, 2)
            p.setFillColor("purple")
            p.drawString(468, position1 - 320, "Doctor signature")
            p.drawImage(signature, 470, position1 - 310, 100, 60)
            p.showPage()
            # ----------------------------------------------------------------
        p.save()
        return response


class IRPrescriptionDownload(generics.RetrieveAPIView):

    queryset = IRPrescription.objects.all()
    serializer_class = IRPrescriptionSerializer
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        a = self.retrieve(request, *args, **kwargs)
        res_data = dict()
        app = Appointments.objects.filter(id__exact=a.data["AppointmentId"])[0]
        p_data = filter_user_data(app.patient)
        d_data = filter_user_data(app.slot_id.doctor)

        res_data["prescription_data"] = a.data
        res_data["patient_details"] = p_data
        res_data["doctor_details"] = d_data
        res_data["appointment_details"] = serialize_model(
            app, AppointmentsSerializer)

        patient_id = asUnicode(
            str(res_data["prescription_data"]["id"]), enc="utf8")
        current_diagnosis = asUnicode(
            str(res_data["prescription_data"]["CurrentDiagnosis"]), enc="utf8"
        )
        consultation_summary = asUnicode(
            str(res_data["prescription_data"]["ConsultationSummary"]), enc="utf8"
        )
        patient_details = res_data["patient_details"]

        patient_name = asUnicode(str(patient_details["name"]), enc="utf8")
        patient_gender = asUnicode(str(patient_details["sex"]), enc="utf8")
        patient_age = asUnicode(str(patient_details["age"]), enc="utf8")
        dateofpres = asUnicode(
            str(res_data["prescription_data"]["DateOfCreatingPresc"]), enc="utf8"
        )
        doctor_id = asUnicode(
            str(res_data["doctor_details"]
                ["doctor_other_details"]["DoctorId"]),
            enc="utf8",
        )
        summary = asUnicode(
            str(res_data["prescription_data"]["ConsultationSummary"]), enc="utf8"
        )
        remarks = asUnicode(str(res_data["prescription_data"]["Remarks"]))
        special_instruction = asUnicode(
            str(res_data["prescription_data"]["SpecialInstructions"])
        )
        follow_up = asUnicode(str(res_data["prescription_data"]["FollowUp"]))
        appointment_id = asUnicode(
            str(res_data["prescription_data"]["AppointmentId"]))
        doctor_name = asUnicode(str(res_data["doctor_details"]["name"]))
        signature = res_data["doctor_details"]["doctor_other_details"]["Signature"]

        # checking for SUrl validity
        if signature is not None:

            print(f"doctor sign----------------{signature}-------------")

            new_obj_url = get_s3_signed_url_bykey(signature)

            signature = new_obj_url

        response = HttpResponse(content_type="application/pdf")
        response["Content-Disposition"] = 'attachment; filename="file.pdf"'

        p = canvas.Canvas(response)
        p.setFont("Helvetica-Bold", 12)

        # Adding text
        p.setFillColor("purple")
        p.drawString(10, 826, doctor_name)

        p.setFont("Helvetica", 9)
        p.setFillColor("purple")
        p.drawString(10, 814, "Dept. of Surgeon")

        p.setFillColor("grey")
        p.drawString(10, 802, "MD.Licenece: 12-136547")
        p.drawString(10, 790, f"Doctor ID - {doctor_id}")
        p.drawString(10, 778, dateofpres)

        # image drawing

        # p.drawImage("C:\\Users\\<USER>\\Downloads\\CU_api-dev_new\\CU_api-dev_new\\logo3.jpg", 500, 810, 60, 20)
        p.drawImage(settings.MEDIA_ROOT + "/logo.png", 500, 810, 60, 20)
        p.setFont("Helvetica-Bold", 10)
        p.setFillColor("purple")
        p.drawString(500, 800, "Apollo")

        p.setFillColor("blue")
        p.setFont("Helvetica", 5)
        p.drawString(500, 796, "https://healthunwired.com/")

        p.setFillColor("grey")
        p.setLineWidth(0)
        p.line(0, 772, 600, 772)

        p.setFont("Helvetica", 10)
        p.setFillColor("purple")

        p.drawString(10, 755, "ID")
        p.drawString(110, 755, "Name")
        p.drawString(230, 755, "Consultation ID")
        p.drawString(350, 755, "Sex")
        p.drawString(470, 755, "Age")

        p.setFillColor("grey")
        p.setLineWidth(0)

        #      x  y   w  h
        p.roundRect(10, 730, 90, 20, 2)
        p.roundRect(110, 730, 110, 20, 2)
        p.roundRect(230, 730, 110, 20, 2)
        p.roundRect(350, 730, 110, 20, 2)
        p.roundRect(470, 730, 110, 20, 2)

        # p.setFillColor("purple")
        # inside box
        p.setFont("Helvetica", 10)
        p.drawString(14, 734, patient_id)
        p.drawString(115, 734, patient_name)
        p.drawString(235, 734, appointment_id)
        p.drawString(355, 734, patient_gender)
        p.drawString(475, 734, patient_age)

        p.roundRect(10, 700, 90, 20, 2)
        p.setFillColor("purple")
        p.drawString(12, 705, "Current Diagnosis")
        p.roundRect(110, 700, 470, 20, 2)
        p.setFillColor("grey")
        p.drawString(114, 705, current_diagnosis)

        position1 = 705
        height = 100

        p.setFillColor("grey")
        p.roundRect(10, position1 - 118, 570, height, 2)
        p.drawString(12, position1 - 30, "Consultation Summary")
        p.setFont("Helvetica-Bold", 10)

        # remarks
        start = 14
        position_next = 40
        width, height = A4
        my_style = ParagraphStyle(
            "my style",
            fontName="Helvetica",
            fontSize=10,
            spaceBefore=10,
            spaceAfter=5,
            alignment=0,
        )

        para = Paragraph(summary, my_style)
        para.wrapOn(p, 500, 50)
        para.drawOn(p, width - 580, height - 280)

        p.roundRect(10, position1 - 224, 570, height, 2)
        p.setFont("Helvetica", 10)
        p.drawString(12, position1 - 134, "Remarks")
        p.setFont("Helvetica-Bold", 10)
        para = Paragraph(remarks, my_style)
        para.wrapOn(p, 500, 50)
        para.drawOn(p, width - 580, height - 380)

        # p.drawString(14, position1 - 144, remarks)
        # Paragraph(14, position1 - 144,remarks, my_style)

        # special instruction

        p.roundRect(10, position1 - 328, 570, height, 2)
        p.setFont("Helvetica", 10)
        p.drawString(12, position1 - 238, "Special Instructions")
        p.setFont("Helvetica-Bold", 10)

        para = Paragraph(special_instruction, my_style)
        para.wrapOn(p, 500, 50)
        para.drawOn(p, width - 580, height - 480)
        # p.drawText(special_instruction)
        # p.drawString(14, position1 - 250, special_instruction)

        # follow-up

        p.roundRect(10, position1 - 354, 130, 20, 2)
        p.setFont("Helvetica", 10)
        p.setFillColor("purple")
        p.drawString(12, position1 - 345, "Follow-up Appointment")
        p.roundRect(143, position1 - 354, 438, 20, 2)
        p.setFont("Helvetica-Bold", 10)
        p.setFillColor("grey")
        p.drawString(146, position1 - 345, follow_up)

        # signature
        # p.roundRect(460, position1 - 398, 120, 40, 2)
        p.setFont("Helvetica", 6)
        p.setFillColor("purple")
        p.drawString(500, position1 - 367, "Digital Signature")
        # p.drawImage("C:\\Users\\<USER>\\Downloads\\CU_api-dev_new\\CU_api-dev_new\\doctorsig.jpg", 463,
        #             position1 - 395, 114, 26)
        p.drawImage(signature, 463, position1 - 395, 114, 40)

        p.showPage()
        p.save()
        return response


class UserAppointmentsDownload(generics.ListAPIView):
    queryset = Appointments.objects.all()
    serializer_class = AppointmentsSerializer
    lookup_field = "id"

    def get_queryset(self):
        id_val = self.kwargs.get(self.lookup_field)
        user_role = get_cu_user_type(id_val)

        data = []
        if (
            self.kwargs.get("start_date") != "all"
            and self.kwargs.get("end_date") != "all"
        ):

            # start_date = datetime.strptime(self.kwargs.get('start_date'), "%Y-%m-%d")
            # end_date = datetime.strptime(self.kwargs.get('end_date'), "%Y-%m-%d")
            start_date = timezone.make_aware(
                parse_datetime(self.kwargs.get("start_date")),
                timezone.get_current_timezone(),
            )
            end_date = timezone.make_aware(
                parse_datetime(self.kwargs.get("end_date")),
                timezone.get_current_timezone(),
            )

            if user_role == "patient":
                # data = Appointments.objects.filter(patient__id=id_val)
                data = Appointments.objects.filter(
                    patient__id=id_val,
                    slot_id__schedule_start_time__gte=start_date,
                    slot_id__schedule_end_time__lte=end_date,
                )

            elif (
                user_role == "doctor"
                or user_role == "researcher"
                or user_role == "influencer"
            ):
                print(f"is doctor datesss")
                # data = Appointments.objects.filter(slot_id__doctor=id_val)
                data = Appointments.objects.filter(
                    slot_id__doctor__exact=id_val,
                    slot_id__schedule_start_time__gte=start_date,
                    slot_id__schedule_end_time__lte=end_date,
                )
            else:
                pass
        else:
            if user_role == "patient":
                data = Appointments.objects.filter(patient__id=id_val)
            elif (
                user_role == "doctor"
                or user_role == "researcher"
                or user_role == "influencer"
            ):
                print(f"is doctor all")
                data = Appointments.objects.filter(
                    slot_id__doctor__exact=id_val)
            else:
                pass

        return data

    def get(self, request, *args, **kwargs):
        res = self.list(request, *args, **kwargs)
        for x in res.data:

            doc = SchedulerSlots.objects.filter(
                id__exact=json.loads(json.dumps(x))["slot_id"]
            )[0].doctor.id
            if get_cu_user_type(doc) in ["researcher", "influencer"]:
                pres = Appointments.objects.filter(id__exact=x["id"])[
                    0
                ].irprescription_set.all()
            elif get_cu_user_type(doc) in ["doctor"]:
                pres = Appointments.objects.filter(id__exact=x["id"])[
                    0
                ].prescription_set.all()
            else:
                pass
            p_queries = Appointments.objects.filter(id__exact=x["id"])[
                0
            ].patientqueries_set.all()
            meeting_session_details = Appointments.objects.filter(id__exact=x["id"])[
                0
            ].meetingsession_set.all()
            consent_details = Appointments.objects.filter(id__exact=x["id"])[
                0
            ].appointmentconsent_set.all()
            pres_ids = []
            for z in pres:
                pres_ids.append(z.id)
            p_queries_list1 = []
            for xx in p_queries:
                p_queries_list = []
                qqqq = PatientQueries.objects.filter(
                    Q(id__exact=xx.id) | Q(ReplyTo__exact=xx.id)
                )
                s1 = [serialize_model(t, PatientQueriesSerializer)
                      for t in qqqq]

                p_queries_list.append(s1)

                p_queries_list1.append(p_queries_list)

            x["prescriptions"] = pres_ids
            x["p_queries"] = p_queries_list1
            # added
            sch = SchedulerSlots.objects.filter(id__exact=x["slot_id"])[0]

            # if get_cu_user_type(self.kwargs.get(self.lookup_field)) == "patient":
            #
            #     converted_time1 = sch.schedule_start_time.astimezone(
            #         timezone(CuUser.objects.filter(id__exact=x['patient'])[0].TimeZone))
            #     converted_time2 = sch.schedule_end_time.astimezone(
            #         timezone(CuUser.objects.filter(id__exact=x['patient'])[0].TimeZone))
            #
            #
            # else:
            #
            #
            #     converted_time1 = sch.schedule_start_time
            #     converted_time2 = sch.schedule_end_time
            converted_time1 = timezone.localtime(
                sch.schedule_start_time, timezone.get_current_timezone()
            )
            converted_time2 = timezone.localtime(
                sch.schedule_end_time, timezone.get_current_timezone()
            )

            # added ends

            x["patient"] = filter_user_data(
                CuUser.objects.filter(id__exact=x["patient"])[0]
            )

            doc = sch.doctor
            x["doctor"] = filter_user_data(doc)
            x["slot_start_time"] = converted_time1
            x["slot_end_time"] = converted_time2
            x["meeting_session_details"] = [
                serialize_model(t, MeetingSessionSerializer)
                for t in meeting_session_details
            ]
            x["consent_details"] = [
                serialize_model(t, AppointmentConsentSerializer)
                for t in consent_details
            ]

        # print(f'########THIS IS RESPONSE{res}')
        all_data = []
        for i in res.data:
            appointment_id = i["id"]
            status = "Booked" if i["status"] == "B" else "Cancelled"
            summary = i["summary"]
            location = i["location"]

            patient_data = i["patient"]
            doctor = i["doctor"]

            patient_name = patient_data["name"]
            patient_email = patient_data["email"]
            cancer_type = patient_data["cancer_type"]

            patient_other_details = patient_data["patient_other_details"]

            # patient_id = patient_other_details['PatientId']

            doctor_other_details = doctor["doctor_other_details"]

            doctor_id = doctor_other_details["DoctorId"]
            doctor_name = doctor["name"]
            doctor_email = doctor["email"]
            doctor_timezone = doctor["TimeZone"]
            appointment_date = i["slot_start_time"]

            date_format = "%Y-%m-%d"
            appointment_dates = date.strftime(appointment_date, date_format)

            datas = {
                "appointment_id": appointment_id,
                "status": status,
                "summary": summary,
                "location": location,
                "patient_name": patient_name,
                "patient_email": patient_email,
                # 'cancer_type': cancer_type,
                "doctor_email": doctor_email,
                "doctor_name": doctor_name,
                "appointment_date": appointment_dates,
                "doctor_timezone": doctor_timezone,
            }
            all_data.append(datas)

        print(f"all dataaaaaaaaaaa{all_data}")
        # response = HttpResponse(content_type='application/excel')
        response = HttpResponse(content_type="application/vnd.ms-excel")
        response["Content-Disposition"] = 'attachment; filename="file.xlsx"'

        with xlsxwriter.Workbook(response, {"in_memory": True}) as workbook:
            worksheet = workbook.add_worksheet()

            # add a bold format
            keys = []
            data_zero = all_data[0]
            for key in data_zero:
                keys.append(key)

            row = 0
            col = 0
            bold = workbook.add_format({"bold": 1})
            s = 1
            for key in keys:
                key = str(key)
                length = len(key)
                length *= 2
                worksheet.set_column(0, s, length)
                worksheet.write(row, col, key, bold)
                s += 1
                col += 1

            row += 1
            col = 0
            # starting from column 1
            j = 1

            for i in all_data:
                for values in i.values():
                    values = str(values)
                    length = len(key)
                    length *= 2
                    worksheet.set_column(1, j, length)
                    worksheet.write(row, col, values)
                    j += 1
                    col += 1

                row += 1
                j = 1
                col = 0

        return response


# added
class GetUserNotifications(generics.ListAPIView):
    serializer_class = PushNotificationsSerializer
    lookup_field = "user_id"
    queryset = PushNotifications.objects.all()

    def get_queryset(self):
        id_val = self.kwargs.get(self.lookup_field)
        data = PushNotifications.objects.filter(
            UserId=id_val, Status=1).order_by('-NotificationTime')
        return data

    def get(self, request, *args, **kwargs):
        res = self.list(request, *args, **kwargs)
        if 'page' in request.GET and request.GET['page'] != "":

            # Get the page number from the request
            page_number = request.GET.get('page', 1)
            # Get the number of items per page from the request
            items_per_page = request.GET.get('per_page', 10)
            total_items = len(res.data)
            paginator = Paginator(res.data, items_per_page)
            print(paginator)
            if int(page_number) not in range(1, int(paginator.num_pages)+1):
                return HttpResponse("Not a valid page number", status=400)
            res.data = paginator.page(page_number)
            response_data = {
                'total_items': total_items,
                'total_pages': paginator.num_pages,
                'items': list(res.data)
            }
            return JsonResponse(response_data)

        else:
            return res


# added


# added
class ReadNotifications(generics.RetrieveUpdateAPIView):
    serializer_class = PushNotificationsSerializer
    lookup_field = "id"
    queryset = PushNotifications.objects.all()

    def get_queryset(self):
        id_val = self.kwargs.get(self.lookup_field)
        data = PushNotifications.objects.filter(id=id_val)
        return data

    def get(self, request, *args, **kwargs):
        return self.retrieve(request, *args, **kwargs)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.Status = 0
        instance.save(update_fields=["Status"])
        return instance

    def put(self, request, *args, **kwargs):
        res = self.partial_update(request, *args, **kwargs)
        res = serialize_model(res, PushNotificationsSerializer)
        return JsonResponse(res)


# added

# added


def RefreshZohoSANDBOXAccessToken():
    deskurl = (
        "https://accounts.zoho.in/oauth/v2/token?refresh_token="
        + os.getenv("ZOHO_SANDBOX_REFRESH_TOKEN")
        + "&grant_type=refresh_token&client_id="
        + os.getenv("ZOHODESK_CLIENT_ID")
        + "&client_secret="
        + os.getenv("ZOHODESK_CLIENT_SECRET")
        + "&redirect_uri=https://crmsandbox.zoho.in/crm/testing_modules/tab/Home/begin"
    )
    r = requests.post(deskurl)
    a = r.json()

    return a["access_token"]


class DoctorDataToZohoAccount(generics.ListAPIView):
    def get(self, request, *args, **kwargs):
        data = DoctorDetails.objects.all()
        print(f"---------------doctor_data{data}")
        d_data = []
        for x in data:
            doctor_data = CuUser.objects.filter(id__exact=x.id)[0]
            d_name = doctor_data.name
            print(f"---------doctor_name{d_name}")
            doctor_email = doctor_data.email
            doctor_phone = doctor_data.phone
            datas = {
                "Account_Name": d_name,
                "Email_address": doctor_email,
                "Phone": doctor_phone,
                "Doctor_Name": d_name,
                "Status": doctor_data.approval,
                #         "Phone": "********",
                # "Website": "https://second_doctor.in/",
                # "Account Owner": "Oncofit Solutions"
            }
            d_data.append(datas)
        #         d_data1 ={
        #     "data": [
        #         {

        #             "Account_Name": "First Doctor",
        #             "Phone": "12345",
        #             "Website": "https://first_doctor.in/",
        #             "Account Owner": "Oncofit Solutions"

        #         },
        #         {

        #              "Account_Name": "Second Doctor",
        #             "Phone": "********",
        #             "Website": "https://second_doctor.in/",
        #             "Account Owner": "Oncofit Solutions"

        #         }
        #     ],
        #     "apply_feature_execution": [
        #         {
        #             "name": "layout_rules"
        #         }
        #     ]
        # }
        sending_data = {
            "data": d_data,
            "apply_feature_execution": [{"name": "layout_rules"}],
            "duplicate_check_fields": ["Email_address"],
        }

        print(f"--------------------sending_data{sending_data}")

        account_url = "https://crmsandbox.zoho.in/crm/v6/Accounts/upsert"
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Layout": os.getenv("ZOHO_SANDBOX_ACCOUNT_LAYOUT_ID"),
            "Authorization": "Bearer " + os.getenv("ZOHO_SANDBOX_ACCESS_TOKEN"),
        }

        r = requests.post(account_url, headers=headers, json=sending_data)
        print(r)
        if r.status_code == 401:
            a = RefreshZohoSANDBOXAccessToken()
            headers["Authorization"] = "Bearer " + a
            r = requests.post(account_url, headers=headers, json=sending_data)
            print(r.status_code)
            a = r.json()

            return Response(a)
        return Response(r.json())


# added


# added
class PatientDataToZohoContact(generics.ListAPIView):
    def get(self, request, *args, **kwargs):
        data = PatientDetails.objects.all()
        print(f"---------------doctor_data{data}")
        p_data = []
        for x in data:
            patient_data = CuUser.objects.filter(id=x.PatientId_id)[0]
            p_name = patient_data.name
            print(f"---------doctor_name{p_name}")
            patient_email = patient_data.email
            patient_phone = patient_data.phone
            datas = {
                "Patient_Name": p_name,
                "Email": patient_email,
                "Phone": patient_phone,
                "Last_Name": p_name,
                #         "Doctor_Name":d_name,
                #         "Phone": "********",
                # "Website": "https://second_doctor.in/",
                # "Account Owner": "Oncofit Solutions"
            }
            p_data.append(datas)
        #         d_data1 ={
        #     "data": [
        #         {

        #             "Account_Name": "First Doctor",
        #             "Phone": "12345",
        #             "Website": "https://first_doctor.in/",
        #             "Account Owner": "Oncofit Solutions"

        #         },
        #         {

        #              "Account_Name": "Second Doctor",
        #             "Phone": "********",
        #             "Website": "https://second_doctor.in/",
        #             "Account Owner": "Oncofit Solutions"

        #         }
        #     ],
        #     "apply_feature_execution": [
        #         {
        #             "name": "layout_rules"
        #         }
        #     ]
        # }
        sending_data = {
            "data": p_data,
            "apply_feature_execution": [{"name": "layout_rules"}],
            "duplicate_check_fields": ["Email_address"],
        }

        print(f"--------------------sending_data{sending_data}")

        account_url = "https://crmsandbox.zoho.in/crm/v6/Contacts/upsert"
        headers = {
            "Content-Type": "application/json",
            "Accept": "application/json",
            "Layout": os.getenv("ZOHO_SANDBOX_CONTACT_LAYOUT_ID"),
            "Authorization": "Bearer " + os.getenv("ZOHO_SANDBOX_ACCESS_TOKEN"),
        }

        r = requests.post(account_url, headers=headers, json=sending_data)
        print(r)
        if r.status_code == 401:
            a = RefreshZohoSANDBOXAccessToken()
            headers["Authorization"] = "Bearer " + a
            r = requests.post(account_url, headers=headers, json=sending_data)
            print(r.status_code)
            a = r.json()

            return Response(a)
        return Response(r.json())


# added

# added


class MedicalRecordDownload(generics.RetrieveAPIView):
    queryset = patient_medical_records.objects.all()
    serializer_class = patient_medical_recordsSerializer
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        a = self.retrieve(request, *args, **kwargs)
        print(f"-----------------{a}----------------")
        patient_details = CuUser.objects.filter(id__exact=a.data["patient"])[0]
        patient_name = asUnicode(str(patient_details.name), enc="utf8")
        patient_age = asUnicode(str(patient_details.age), enc="utf8")
        patient_gender = asUnicode(str(patient_details.sex), enc="utf8")
        patient_email = asUnicode(str(patient_details.email), enc="utf8")
        record_id = asUnicode(str(a.data["id"]), enc="utf8")

        report_name = asUnicode(str(a.data["reportname"]), enc="utf8")
        report_type = asUnicode(str(a.data["reporttype"]), enc="utf8")
        report_summary = asUnicode(str(a.data["reportsummary"]), enc="utf8")
        report_key = a.data["report_file"]
        report_file = a.data["report_file"]

        # checking for SUrl validity
        # if report_file is not None:
        #     print(f"medical download report-----------{report_file}-------------")
        #     # new_obj_url = get_s3_signed_url_bykey(report_file)
        #     # report_file = new_obj_url
        #     r_f=[]
        #     for i in report_file:
        #         new_obj_url = get_s3_signed_url_bykey(i)
        #         print(new_obj_url)
        #         r_f.append(new_obj_url)
        #     report_file = r_f
        b = str(a.data["generation_date"]).split("T")
        generation_date = asUnicode(b[0], enc="utf8")

        # creating a pdf file

        response = HttpResponse(content_type="application/pdf")
        response["Content-Disposition"] = (
            'attachment; filename="medical_record_file.pdf"'
        )

        p = canvas.Canvas(response)

        p.setFont("Helvetica-Bold", 12)

        # Adding text
        # p.setFillColor("purple")
        # p.drawString(10, 826, "Cancer")

        p.setFont("Helvetica", 9)
        # p.setFillColor("purple")
        # p.drawString(10, 814, "Unwired")

        p.setFillColor("grey")
        p.drawString(500, 805, f"Date: {generation_date}")
        p.drawString(500, 796, f"Report Id: {record_id}")

        # image drawing
        p.drawImage(settings.MEDIA_ROOT + "/logo.png", 12, 802, 60, 20)
        p.setFont("Helvetica-Bold", 9)
        p.setFillColor("purple")
        p.drawString(12, 795, "Apollo")

        p.setFillColor("blue")
        p.setFont("Helvetica", 6)
        p.drawString(12, 788, "www.healthunwired.com")

        p.setFillColor("grey")
        p.setLineWidth(0)
        p.line(0, 772, 600, 772)

        # creating a frame-something
        p.setFont("Helvetica", 10)
        p.setFillColor("purple")

        p.drawString(10, 755, "Medical record id")
        p.drawString(110, 755, "Name")
        p.drawString(230, 755, "Email")
        p.drawString(400, 755, "Sex")
        p.drawString(480, 755, "Age")

        p.setFillColor("grey")
        p.setLineWidth(0)

        #      x  y   w  h
        p.roundRect(10, 730, 90, 20, 2)
        p.roundRect(110, 730, 110, 20, 2)
        p.roundRect(230, 730, 150, 20, 2)
        p.roundRect(400, 730, 70, 20, 2)
        p.roundRect(480, 730, 70, 20, 2)

        p.setFont("Helvetica", 10)
        p.drawString(14, 734, record_id)
        p.drawString(115, 734, patient_name)
        p.drawString(235, 734, patient_email)
        p.drawString(405, 734, patient_gender)
        p.drawString(485, 734, patient_age)

        p.setFont("Helvetica", 10)

        p.setFillColor("purple")
        p.drawString(14, 690, "Report Name")
        p.drawString(170, 690, "Report Type")
        p.drawString(380, 690, "Generation Date")

        p.setFillColor("grey")
        p.drawString(17, 668, report_name)
        p.drawString(172, 668, report_type)
        p.drawString(382, 668, generation_date)
        # p.drawString()

        p.roundRect(14, 660, 150, 20, 2)
        p.roundRect(170, 660, 200, 20, 2)
        p.roundRect(380, 660, 200, 20, 2)

        p.setFont("Helvetica", 12)
        p.setFillColor("purple")
        p.drawString(14, 640, "Report Summary")
        p.setFillColor("grey")
        p.roundRect(14, 530, 570, 100, 2)

        p.setFont("Helvetica", 10)
        position1 = 618
        start = 15
        spoint = 0
        epoint = 120
        length = len(report_summary)

        # print(special_instruction[spoint:epoint])

        while length >= 0:

            p.drawString(start, position1, report_summary[spoint: epoint + 1])
            length -= 120
            position1 -= 10
            start = 14
            spoint = epoint + 1
            epoint = epoint + 120
        # p.drawString(15,618,report_summary)

        p.setFillColor("purple")
        p.setFont("Helvetica", 9)
        p.drawString(250, 518, "Report file")
        p.setLineWidth(0)
        p.line(13, 520, 248, 520)
        p.line(295, 520, 585, 520)

        if report_file is not None:
            print(
                f"medical download report-----------{report_file}-------------")
            # new_obj_url = get_s3_signed_url_bykey(report_file)
            # report_file = new_obj_url
            r_f = []
            for i in report_file:
                print(f"-------sssssssss{i}--------------------")
                new_obj_url = get_s3_signed_url_bykey(i)
                print(new_obj_url)
                r_f.append(new_obj_url)
                if ".pdf" in i:

                    # reading pdf
                    s3 = boto3.client("s3")
                    obj = s3.get_object(Bucket="cuapp-files", Key=i)
                    print(f"obj--------------------{obj}")
                    reader = PdfReader(BytesIO(obj["Body"].read()))
                    print(
                        f"reader--------------------{reader}--------------------")
                    # s_r = obj['Body'].read()
                    # print(f's_r--------{s_r}')

                    # creating doc
                    with open("pdf_1.pdf", "wb") as doc:
                        reader1 = reader
                        pdf_writer = PyPDF2.PdfWriter()
                        pdf_writer.clone_document_from_reader(reader1)
                        pdf_writer.write(doc)

                    input_pdf = "pdf_1.pdf"

                    pdf_document = fitz.open(input_pdf)
                    for page_number in range(len(pdf_document)):
                        page = pdf_document.load_page(page_number)
                        image = page.get_pixmap()
                        new = 1
                        image.save(f"converts{new + 1}" + ".jpeg")

                        p.drawImage(f"converts{new + 1}" +
                                    ".jpeg", 30, 10, 500, 500)
                        new += 1

                else:
                    # s3 = boto3.client("s3")
                    # obj = s3.get_object(Bucket="cu-files", Key=i)
                    # print(f'obj--------------------{obj}')
                    # zz=obj["Body"]
                    new_obj_url = get_s3_signed_url_bykey(i)
                    p.drawImage(new_obj_url, 12, 10, 500, 500)

                p.showPage()
        p.save()

        return response


# added
def send_deleted_email(u_email, u_name, host, reason):
    verify_url1 = host + "/deletedAccount"
    payload = {
        "template_key": "2518b.41c485fda42f6e5f.k1.6b13c4b0-287f-11ef-8dec-525400ab18e6.1900afdbc7b",
        # "bounce_address": "<EMAIL>",
        "from": {"address": "<EMAIL>", "name": "Health Unwired"},
        "to": [
            {
                "email_address": {
                    "address": u_email,
                    "name": u_email,
                }
            }
        ],
        "merge_info": {
            "u_name": u_name,
            "delete_link": verify_url1,
            "reason": reason,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }

    headers = {
        "Authorization": f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        "Content-Type": "application/json",
        "Accept": "application/json",
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
    try:
        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload)
        )
        print(
            f"pw mail send-----------{response.status_code}-------{response.content}--------"
        )
        response_status = (
            True
            if response.status_code == 200
            else True if response.status_code in [201, 202] else False
        )
        return response_status
    except HTTPError as e:
        print(f"exception-----------{e.to_dict}")
        return response_status


# added
class UserDeleteAPIView(generics.UpdateAPIView):
    queryset = CuUser.objects.all()
    lookup_field = "email"
    serializer_class = CuUserSerializer

    def put(self, request, *args, **kwargs):
        u_data = CuUser.objects.filter(email__exact=self.kwargs["email"])[0]
        u_name = u_data.name
        res = self.partial_update(request, *args, **kwargs)
        if res.status_code == 200:
            print("deleted successfully")
            if get_cu_user_type(u_data.id) in ["doctor", "researcher", "influencer"]:
                app_url = os.getenv("NEXT_CANCER_UNWIRED_DOCTOR_APP")
                zz = send_deleted_email(
                    self.kwargs["email"], u_name, app_url, "successfully"
                )
                # inserting doctor data into zoho accounts
                acc_zoho = AddToZoho(
                    CuUser.objects.get(email__exact=self.kwargs["email"])
                )
                # added
            elif get_cu_user_type(u_data.id) == "patient":
                app_url = os.getenv("NEXT_CANCER_UNWIRED_PATIENT_APP")

                zz = send_deleted_email(
                    self.kwargs['email'], u_name, app_url, "successfully")
            return HttpResponse("Your account has been deleted successfully.", status=200)
        else:
            return Response("Account deletion failed")

    # ----------------------------------------------------------------


# added Podcast/blogs views update view
class BlogPodcastViewsUpdateView(views.APIView):
    permission_classes = []

    def put(self, request, *args, **kwargs):
        if int(request.data["type"]) == 0:
            instance = Podcast.objects.get(id__exact=self.kwargs["id"])
            instance.PodcastViews += 1
            instance.save()
            return Response("View added successfully.", status=200)

        elif int(request.data["type"]) == 1:
            instance = ExpertBlogs.objects.get(id__exact=self.kwargs["id"])
            instance.BlogViews += 1
            instance.save()
            return Response("View added successfully.", status=200)
        else:
            return Response("Not a valid content", status=404)


# added FAQs List/Create
class GetCreateFAQsView(generics.ListCreateAPIView):
    permission_classes = []
    serializer_class = FAQsSerializer
    queryset = FAQs.objects.all()

    def get(self, request, *args, **kwargs):
        a = self.list(request, *args, **kwargs)
        return a

    def post(self, request, *args, **kwargs):
        if "user_id" in request.GET and get_cu_user_type(request.GET["user_id"]) in [
            "child_admin",
            "admin",
        ]:
            a = self.create(request, *args, **kwargs)
            return a
        else:
            return Response("You are not authorized to perform action", status=401)


# added Categories to blogs and podcasts
class ListCreateBlogCategoryView(generics.ListCreateAPIView):
    serializer_class = BlogCategorySerializer
    queryset = BlogCategory.objects.all()
    permission_classes = []

    def get(self, request, *args, **kwargs):
        a = self.list(request, *args, **kwargs)
        return a

    def post(self, request, *args, **kwargs):
        if "user_id" in request.GET and get_cu_user_type(request.GET["user_id"]) in [
            "child_admin",
            "admin",
        ]:
            a = self.create(request, *args, **kwargs)
            return a
        else:
            return Response("You are not authorized to perform action", status=401)


class ListCreatePodcastCategoryView(generics.ListCreateAPIView):
    serializer_class = PodcastCategorySerializer
    queryset = PodcastCategory.objects.all()
    permission_classes = []

    def get(self, request, *args, **kwargs):
        a = self.list(request, *args, **kwargs)
        return a

    def post(self, request, *args, **kwargs):
        if "user_id" in request.GET and get_cu_user_type(request.GET["user_id"]) in [
            "child_admin",
            "admin",
        ]:
            a = self.create(request, *args, **kwargs)
            return a
        else:
            return Response("You are not authorized to perform action", status=401)


# added to retrieve a single blog and podcast details
class GetSingleBlogView(generics.RetrieveAPIView):
    serializer_class = ExpertBlogsSerializer
    queryset = ExpertBlogs.objects.all()
    permission_classes = []
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        a = self.retrieve(request, *args, **kwargs)
        dataa = []

        if a.data["BlogBannerImage"] is not None:
            new_obj_url = get_s3_signed_url_bykey(a.data["BlogBannerImage"])
            a.data["BlogBannerImage"] = new_obj_url
        if a.data["BlogFeatureImage"] is not None:
            new_obj_url = get_s3_signed_url_bykey(a.data["BlogFeatureImage"])
            a.data["BlogFeatureImage"] = new_obj_url

        if a.data["BlogImages"] is not None:
            imagess = a.data["BlogImages"]
            b_images1 = []
            c = 0
            for i in imagess:
                new_obj_url = get_s3_signed_url_bykey(i)
                b_images1.append(new_obj_url)
            a.data["BlogImages"] = b_images1
        if a.data["BlogSubImage"] is not None:
            imagess = a.data["BlogSubImage"]
            b_subimages = []
            c = 0
            for i in imagess:
                new_obj_url = get_s3_signed_url_bykey(i)
                b_subimages.append(new_obj_url)
            a.data["BlogSubImage"] = b_subimages
        if a.data["BlogSectionVal"] is not None:
            a.data["BlogSectionName"] = BlogSection.objects.get(
                id__exact=a.data["BlogSectionVal"]
            ).SectionName
        if a.data["BlogCategoryVal"] is not None:
            a.data["BlogCategoryVal"] = BlogCategory.objects.get(
                id__exact=a.data["BlogCategoryVal"]
            ).Category
        dict1 = dict()
        # ----------------added for the check of admin/experts----------------------------------------
        user_data = []
        if get_cu_user_type(a.data["ExpertId"]) in ["admin", "child_admin"]:
            user_data = {
                "id": a.data["ExpertId"],
                "prefix": CuUser.objects.get(id=a.data["ExpertId"]).prefix,
                "name": "Cancer Unwired Team",
                "role": get_cu_user_type(a.data["ExpertId"]),
                "doctor_other_details": {
                    "ProfilePhoto": get_s3_signed_url_bykey(
                        "2024-08-06-11-28-28-MQ9HIZABRB_logo_OLIWZYL.png"
                    )
                },
            }
        elif get_cu_user_type(a.data["ExpertId"]) in [
            "doctor",
            "researcher",
            "influencer",
        ]:
            user_data = filter_user_data(
                get_user_model().objects.get(id__exact=a.data["ExpertId"])
            )
            print(f"other------{user_data['doctor_other_details']}")

            if user_data["doctor_other_details"]["ProfilePhoto"] is not None:
                new_obj_url = get_s3_signed_url_bykey(
                    user_data["doctor_other_details"]["ProfilePhoto"]
                )
                user_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url
        else:
            pass

        # added
        dict1["blog_details"] = a.data
        dict1["blog_details"]["expert_details"] = user_data
        dataa.append(dict1)
        return JsonResponse(dataa, safe=False)


class GetSinglePodcastView(generics.RetrieveAPIView):
    serializer_class = PodcastSerializer
    queryset = Podcast.objects.all()
    permission_classes = []
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        a = self.retrieve(request, *args, **kwargs)
        dataa = []

        dict1 = dict()
        if a.data["ThumbnailImage"] is not None:
            new_obj_url = get_s3_signed_url_bykey(a.data["ThumbnailImage"])
            a.data["ThumbnailImage"] = new_obj_url
        if a.data["PodcastSectionVal"] is not None:
            a.data["PodcastSectionName"] = PodcastSection.objects.get(
                id__exact=a.data["PodcastSectionVal"]
            ).SectionName
        if a.data["PodcastCategoryVal"] is not None:
            a.data["PodcastCategoryVal"] = PodcastCategory.objects.get(
                id__exact=a.data["PodcastCategoryVal"]
            ).Category
        dict1 = dict()
        # ----------------added for the check of admin/experts----------------------------------------
        user_data = []
        if get_cu_user_type(a.data["ExpertId"]) in ["admin", "child_admin"]:
            user_data = {
                "id": a.data["ExpertId"],
                "prefix": CuUser.objects.get(id=a.data["ExpertId"]).prefix,
                "name": "Cancer Unwired Team",
                "role": get_cu_user_type(a.data["ExpertId"]),
                "doctor_other_details": {
                    "ProfilePhoto": get_s3_signed_url_bykey(
                        "2024-08-06-11-28-28-MQ9HIZABRB_logo_OLIWZYL.png"
                    )
                },
            }
        elif get_cu_user_type(a.data["ExpertId"]) in [
            "doctor",
            "researcher",
            "influencer",
        ]:
            user_data = filter_user_data(
                get_user_model().objects.get(id__exact=a.data["ExpertId"])
            )
            print(f"other------{user_data['doctor_other_details']}")

            if user_data["doctor_other_details"]["ProfilePhoto"] is not None:
                new_obj_url = get_s3_signed_url_bykey(
                    user_data["doctor_other_details"]["ProfilePhoto"]
                )
                user_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url
        else:
            pass
        # added
        dict1["podcast_details"] = a.data
        dict1["podcast_details"]["expert_details"] = user_data
        dataa.append(dict1)
        return JsonResponse(dataa, safe=False)


class GetExpertPodcastView(generics.ListAPIView):
    serializer_class = PodcastSerializer
    permission_classes = []

    def get_queryset(self):
        podcast_filter = Podcast.objects.all()
        if self.kwargs["id"] != "all":
            print(f"experttttttttttttttt")
            podcast_filter = Podcast.objects.filter(
                PodcastStatus__exact=2, ExpertId__exact=self.kwargs["id"]
            )
        elif self.kwargs["id"] == "all":
            print(f"allllllllllllllllllllllllll")
            podcast_filter = Podcast.objects.filter(PodcastStatus__exact=2)
        else:
            print(f"nothinggggggggggggggg")
            return []
        for x in self.request.GET:
            if x == "category":
                cat_obj = eval(self.request.GET["category"])
                podcast_filter = podcast_filter.filter(
                    PodcastCategoryVal__Category__in=cat_obj
                )
            elif x == "date":
                date = timezone.make_aware(
                    parse_datetime(self.request.GET["date"]),
                    timezone.get_current_timezone(),
                )
                podcast_filter = podcast_filter.filter(PodcastDate__exact=date)
            elif x == "search":
                podcast_filter = podcast_filter.filter(
                    Q(PodcastCategoryVal__Category__exact=self.request.GET["search"])
                    | Q(PodcastTopic__icontains=self.request.GET["search"])
                )
            elif x == "start_date":
                start_date = timezone.make_aware(
                    parse_datetime(self.request.GET["start_date"]),
                    timezone.get_current_timezone(),
                )
                podcast_filter = podcast_filter.filter(
                    PodcastDate__gte=start_date)
            elif x == "end_date":
                end_date = timezone.make_aware(
                    parse_datetime(self.request.GET["end_date"]),
                    timezone.get_current_timezone(),
                )
                podcast_filter = podcast_filter.filter(
                    PodcastDate__lte=end_date)
            elif x == "role":
                role_obj = eval(self.request.GET["role"])
                podcast_filter = podcast_filter.filter(
                    ExpertId__groups__name__in=role_obj
                )
            elif x == "exp":
                podcast_filter = podcast_filter.filter(
                    ExpertId__doctordetails__Experience__exact=self.request.GET["exp"]
                )
            elif x == "location":
                podcast_filter = podcast_filter.filter(
                    Q(ExpertId__City__icontains=self.request.GET["location"])
                    | Q(ExpertId__Country__iexact=self.request.GET["location"])
                )
            else:
                print("Not a valid filter")
        return podcast_filter.order_by("-id")

    def get(self, request, *args, **kwargs):
        a = self.list(request, *args, **kwargs)
        y = json.loads(json.dumps(a.data))
        dataa = []
        if "page" in request.GET and request.GET["page"] != "":

            # Get the page number from the request
            page_number = request.GET.get("page", 1)
            # Get the number of items per page from the request
            items_per_page = request.GET.get("per_page", 10)
            total_items = len(y)
            paginator = Paginator(y, items_per_page)
            print(paginator)
            if int(page_number) not in range(1, int(paginator.num_pages) + 1):
                return HttpResponse("Not a valid page number", status=400)
            y = paginator.page(page_number)

        for x in y:
            dict1 = dict()
            if x["ThumbnailImage"] is not None:
                new_obj_url = get_s3_signed_url_bykey(x["ThumbnailImage"])
                x["ThumbnailImage"] = new_obj_url
            if x["PodcastSectionVal"] is not None:
                x["PodcastSectionName"] = PodcastSection.objects.get(
                    id__exact=x["PodcastSectionVal"]
                ).SectionName
            if x["PodcastCategoryVal"] is not None:
                x["PodcastCategoryVal"] = PodcastCategory.objects.get(
                    id__exact=x["PodcastCategoryVal"]
                ).Category
            dict1 = dict()
            zzzz = x["ExpertId"]
            print(f"expertid--------------------{zzzz}")
            # ----------------added for the check of admin/experts----------------------------------------
            user_data = []
            if get_cu_user_type(x["ExpertId"]) in ["admin", "child_admin"]:
                user_data = {
                    "id": x["ExpertId"],
                    "prefix": CuUser.objects.get(id=x["ExpertId"]).prefix,
                    "name": "Cancer Unwired Team",
                    "role": get_cu_user_type(x["ExpertId"]),
                    "doctor_other_details": {
                        "ProfilePhoto": get_s3_signed_url_bykey(
                            "2024-08-06-11-28-28-MQ9HIZABRB_logo_OLIWZYL.png"
                        )
                    },
                }
            elif get_cu_user_type(x["ExpertId"]) in [
                "doctor",
                "researcher",
                "influencer",
            ]:
                user_data = filter_user_data(
                    get_user_model().objects.get(id__exact=x["ExpertId"])
                )
                print(f"other------{user_data['doctor_other_details']}")

                if user_data["doctor_other_details"]["ProfilePhoto"] is not None:
                    new_obj_url = get_s3_signed_url_bykey(
                        user_data["doctor_other_details"]["ProfilePhoto"]
                    )
                    user_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url
            else:
                pass

            # added
            dict1["podcast_details"] = x
            dict1["podcast_details"]["expert_details"] = user_data
            dataa.append(dict1)
        if "page" in request.GET and request.GET["page"] != "":
            response_data = {
                "total_items": total_items,
                "total_pages": paginator.num_pages,
                "items": dataa,
            }
            return Response(response_data)
        else:
            return Response(dataa)


# added get_ranked blogs and podcasts
class GetBlogsPodcastSectionView(views.APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        if int(self.kwargs["type"]) == 0:
            ins = ExpertBlogs.objects.filter(
                BlogSectionVal__SectionName__exact=self.kwargs["section"]
            ).order_by("BlogRanking")
            if "category" in request.GET and request.GET["category"] != "":
                cat_obj = eval(request.GET["category"])
                ins = ins.filter(BlogCategoryVal__Category__in=cat_obj)
            data = []
            for x in ins:
                dict1 = dict()
                # ----------------added for the check of admin/experts----------------------------------------
                user_data = []
                if get_cu_user_type(x.ExpertId_id) in ["admin", "child_admin"]:
                    user_data = {
                        "id": x.ExpertId_id,
                        "prefix": CuUser.objects.get(id=x.ExpertId_id).prefix,
                        "name": "Cancer Unwired Team",
                        "role": get_cu_user_type(x.ExpertId_id),
                        "doctor_other_details": {
                            "ProfilePhoto": get_s3_signed_url_bykey(
                                "2024-08-06-11-28-28-MQ9HIZABRB_logo_OLIWZYL.png"
                            )
                        },
                    }
                elif get_cu_user_type(x.ExpertId_id) in [
                    "doctor",
                    "researcher",
                    "influencer",
                ]:
                    user_data = filter_user_data(
                        get_user_model().objects.get(id__exact=x.ExpertId_id)
                    )
                    print(f"other------{user_data['doctor_other_details']}")

                    if user_data["doctor_other_details"]["ProfilePhoto"] is not None:
                        new_obj_url = get_s3_signed_url_bykey(
                            user_data["doctor_other_details"]["ProfilePhoto"]
                        )
                        user_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url
                else:
                    pass
                z = serialize_model(x, ExpertBlogsSerializer)
                if z["BlogImages"] is not None:
                    imagess = z["BlogImages"]
                    b_images1 = []
                    for i in imagess:
                        new_obj_url = get_s3_signed_url_bykey(i)
                        print(new_obj_url)
                        b_images1.append(new_obj_url)

                    z["BlogImages"] = b_images1
                if z["BlogSubImage"] is not None:
                    imagess = z["BlogSubImage"]
                    b_images1 = []
                    for i in imagess:
                        new_obj_url = get_s3_signed_url_bykey(i)
                        print(new_obj_url)
                        b_images1.append(new_obj_url)

                    z["BlogSubImage"] = b_images1
                if z["BlogBannerImage"] is not None:
                    new_obj_url = get_s3_signed_url_bykey(z["BlogBannerImage"])
                    z["BlogBannerImage"] = new_obj_url
                if z["BlogFeatureImage"] is not None:
                    new_obj_url = get_s3_signed_url_bykey(
                        z["BlogFeatureImage"])
                    z["BlogFeatureImage"] = new_obj_url
                if z["BlogSectionVal"] is not None:
                    z["BlogSectionName"] = BlogSection.objects.get(
                        id__exact=z["BlogSectionVal"]
                    ).SectionName
                if z["BlogCategoryVal"] is not None:
                    z["BlogCategoryVal"] = BlogCategory.objects.get(
                        id__exact=z["BlogCategoryVal"]
                    ).Category
                dict1["blog_details"] = z
                dict1["blog_details"]["expert_details"] = {
                    "expert_name": user_data["name"],
                    "expert_role": get_cu_user_type(user_data["id"]),
                    "expert_id": user_data["id"],
                    "expert_profile_photo": user_data["doctor_other_details"][
                        "ProfilePhoto"
                    ],
                    "prefix": user_data["prefix"],
                }
                data.append(dict1)
            return JsonResponse(data, safe=False)
        elif int(self.kwargs["type"]) == 1:
            ins = Podcast.objects.filter(
                PodcastSectionVal__SectionName__exact=self.kwargs["section"]
            ).order_by("PodcastRanking")
            if "category" in request.GET and request.GET["category"] != "":
                cat_obj = eval(request.GET["category"])
                ins = ins.filter(PodcastCategoryVal__Category__in=cat_obj)
            data = []
            for x in ins:
                dict1 = dict()
                # ----------------added for the check of admin/experts----------------------------------------
                user_data = []
                if get_cu_user_type(x.ExpertId_id) in ["admin", "child_admin"]:
                    user_data = {
                        "id": x.ExpertId_id,
                        "prefix": CuUser.objects.get(id=x.ExpertId_id).prefix,
                        "name": "Cancer Unwired Team",
                        "role": get_cu_user_type(x.ExpertId_id),
                        "doctor_other_details": {
                            "ProfilePhoto": get_s3_signed_url_bykey(
                                "2024-08-06-11-28-28-MQ9HIZABRB_logo_OLIWZYL.png"
                            )
                        },
                    }
                elif get_cu_user_type(x.ExpertId_id) in [
                    "doctor",
                    "researcher",
                    "influencer",
                ]:
                    user_data = filter_user_data(
                        get_user_model().objects.get(id__exact=x.ExpertId_id)
                    )
                    print(f"other------{user_data['doctor_other_details']}")

                    if user_data["doctor_other_details"]["ProfilePhoto"] is not None:
                        new_obj_url = get_s3_signed_url_bykey(
                            user_data["doctor_other_details"]["ProfilePhoto"]
                        )
                        user_data["doctor_other_details"]["ProfilePhoto"] = new_obj_url
                else:
                    pass
                z = serialize_model(x, PodcastSerializer)
                if z["ThumbnailImage"] is not None:
                    z["ThumbnailImage"] = get_s3_signed_url_bykey(
                        z["ThumbnailImage"])
                if z["PodcastSectionVal"] is not None:
                    z["PodcastSectionName"] = PodcastSection.objects.get(
                        id__exact=z["PodcastSectionVal"]
                    ).SectionName
                if z["PodcastCategoryVal"] is not None:
                    z["PodcastCategoryVal"] = PodcastCategory.objects.get(
                        id__exact=z["PodcastCategoryVal"]
                    ).Category
                dict1["podcast_details"] = z
                dict1["podcast_details"]["expert_details"] = {
                    "expert_name": user_data["name"],
                    "expert_role": get_cu_user_type(user_data["id"]),
                    "expert_id": user_data["id"],
                    "expert_profile_photo": user_data["doctor_other_details"][
                        "ProfilePhoto"
                    ],
                    "prefix": user_data["prefix"],
                }
                data.append(dict1)
            return JsonResponse(data, safe=False)

        else:
            return Response("Not a valid status", status=404)


# added for list/retrieve blogs/podcasts
class GetPodcastSectionView(views.APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        if self.kwargs["id"] == "all":
            ins = PodcastSection.objects.all()
        else:
            ins = PodcastSection.objects.filter(id__exact=self.kwargs["id"])
        data = [serialize_model(t, PodcastSectionSerializer) for t in ins]
        return JsonResponse(data, safe=False)


class GetBlogSectionView(views.APIView):
    permission_classes = []

    def get(self, request, *args, **kwargs):
        if self.kwargs["id"] == "all":
            ins = BlogSection.objects.all()
        else:
            ins = BlogSection.objects.filter(id__exact=self.kwargs["id"])
        data = [serialize_model(t, BlogSectionSerializer) for t in ins]
        return JsonResponse(data, safe=False)


# for single podcast category
class GetPodcastCategoryView(generics.RetrieveAPIView):
    serializer_class = PodcastCategorySerializer
    permission_classes = []
    queryset = PodcastCategory.objects.all()
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        a = self.retrieve(request, *args, **kwargs)
        if a.status_code == 200:
            return a
        else:
            return Response("Podcast category not found", status=404)


# for single blog category
class GetBlogCategoryView(generics.RetrieveAPIView):
    serializer_class = BlogCategorySerializer
    permission_classes = []
    queryset = BlogCategory.objects.all()
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        a = self.retrieve(request, *args, **kwargs)
        if a.status_code == 200:
            return a
        else:
            return Response("Blog category not found", status=404)


class VideosLibraryGetView(
    generics.ListAPIView,
    generics.RetrieveAPIView,
):
    queryset = VideosLibrary.objects.all()
    serializer_class = VideosLibrarySerializer
    permission_classes = []
    lookup_field = "id"

    def get(self, request, *args, **kwargs):
        try:
            if "id" in kwargs:
                response = self.retrieve(request, *args, **kwargs)
                isUrl = response.data.get("isUrl")
                video_file_key = response.data.get("video_file")
                thumbnail_image = response.data.get("thumbnail_image")

                if video_file_key and str(isUrl) == 'False':
                    singned_url = get_s3_signed_url_bykey(video_file_key)
                    response.data["video_file"] = singned_url

                if thumbnail_image:
                    signed_thumbnail_url = get_s3_signed_url_bykey(
                        thumbnail_image)
                    response.data["thumbnail_image"] = signed_thumbnail_url

                return Response(
                    {"isSuccess": True, "data": response.data},
                    status=status.HTTP_200_OK,
                )
            else:
                search_query = request.GET.get('search', "").strip()
                videos_queryset = self.get_queryset().order_by('-uploadedAt')

                if search_query:
                    videos_queryset = videos_queryset.filter(
                        Q(video_title__icontains=search_query))

                serializer = self.serializer_class(videos_queryset, many=True)
                response_data = serializer.data
                # response = self.list(request, *args, **kwargs)

                page = int(request.GET.get('page', 1))
                per_page = int(request.GET.get('per_page', 10))
                paginator = Paginator(response_data, per_page)

                try:
                    paginated_videos = paginator.page(page)
                except EmptyPage:
                    return Response(
                        {"isSuccess": False, "message": "Page not found."},
                        status=status.HTTP_404_NOT_FOUND,
                    )
                video_data = []
                for videoUrl in paginated_videos:
                    isUrl = videoUrl.get("isUrl")
                    video_file_key = videoUrl.get("video_file")
                    thumbnail_image = videoUrl.get("thumbnail_image")

                    if video_file_key and str(isUrl) == 'False':
                        singned_url = get_s3_signed_url_bykey(video_file_key)
                        videoUrl["video_file"] = singned_url

                    if thumbnail_image:
                        signed_thumbnail_url = get_s3_signed_url_bykey(
                            thumbnail_image)
                        videoUrl["thumbnail_image"] = signed_thumbnail_url

                    video_data.append(videoUrl)

            paginated_response = {
                "isSuccess": True,
                "total_items": paginator.count,
                "total_pages": paginator.num_pages,
                "current_page": page,
                "per_page": per_page,
                "items": video_data,
            }

            return Response(
                paginated_response,
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            return Response(
                {"isSuccess": False, "message": "An error occurred",
                    "details": str(e)},
                status=status.HTTP_400_BAD_REQUEST,
            )


class UserSubscriptionView(generics.CreateAPIView):
    queryset = UserSubscription.objects.all()
    serializer_class = UserSubscriptionSerializer
    permission_classes = []

    def post(self, request, *args, **kwargs):
        try:
            email = request.data.get('email')
            existing_subscription = UserSubscription.objects.filter(
                email=email).first()

            if existing_subscription:
                if existing_subscription.is_subscribed:
                    return generate_response(
                        success=False,
                        message='This email is already subscribed.',
                        status_code=status.HTTP_400_BAD_REQUEST,
                    )

                else:
                    existing_subscription.is_subscribed = True
                    existing_subscription.save()

                    email_service.subscription_email(
                        existing_subscription.email,
                        existing_subscription.name,
                        "https://healthunwired.com/",
                    )

                    return generate_response(
                        success=True,
                        message='Subscribed Successfully',
                        status_code=status.HTTP_200_OK,
                    )

            response = self.create(request, *args, **kwargs)
            email_service.subscription_email(
                response.data["email"],
                response.data['name'],
                "https://healthunwired.com/",
            )

            return generate_response(
                success=True,
                message='Subscribed Successfully',
                # data=response.data,
                status_code=status.HTTP_200_OK,
            )

        except serializers.ValidationError as e:
            error_messages = "; ".join(
                f"{field}: {', '.join(errors)}" for field, errors in e.detail.items()
            )
            return generate_response(
                success=False,
                message=error_messages,
                status_code=status.HTTP_400_BAD_REQUEST,
            )


class UnsubscribeUserView(generics.UpdateAPIView):
    queryset = UserSubscription.objects.all()
    serializer_class = UserSubscriptionSerializer
    permission_classes = []
    lookup_field = 'email'

    def update(self, request, *args, **kwargs):
        try:

            instance = self.get_object()
            unsubscribe_reason = request.data.get(
                "unsubscribe_reason", "").strip()
            if not unsubscribe_reason:
                return Response(
                    {"success": False, "message": "Unsubscribe reason is required."},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            instance.is_subscribed = False
            instance.unsubscribe_reason = unsubscribe_reason
            instance.save()

            serialized_data = self.get_serializer(instance)

            return generate_response(
                success=True,
                message=f"UnSubscribed Successfully.",
                data=serialized_data.data,
                status_code=status.HTTP_200_OK,
            )

        except Exception as e:
            return generate_response(
                success=False,
                message=str(e),
                status_code=status.HTTP_400_BAD_REQUEST,
            )
