from __future__ import print_function
from django.utils.dateparse import parse_datetime
from django.http import JsonResponse
from rest_framework.exceptions import APIException, ValidationError
from rest_framework import generics, status
from decimal import Decimal
from rest_framework import generics, views
from django.http import JsonResponse, HttpResponse
from cu_app.services.airwallex import AirwallexService
from cu_admin.user_notifications import *
from cu_app.services.email_services import EmailService
from cu_app.utils.helper_functions import *
from ..serializers import *
from datetime import *
from rest_framework.response import Response
from rest_framework.permissions import BasePermission
from rest_framework.generics import RetrieveUpdateDestroyAPIView
import json
from django.views import View
from django.contrib.auth import get_user_model
from ..models import *
import os
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.core.paginator import Paginator
# calendar
import datetime
import os.path
from calendar import monthrange
from dateutil.relativedelta import relativedelta
import zoneinfo

import re
# from datetime import datetime,date, timedelta,timezone
from django.conf import settings
from ..forms import *
from ..cu_library import *
# aws sns
import logging
import time
import boto3
from botocore.exceptions import ClientError
from dotenv import load_dotenv
from rest_framework.parsers import JSONParser
import json
# from pytz import timezone

from rest_framework.exceptions import APIException
import random
from django.db.models import Q
from sendgrid.helpers.mail import *
from python_http_client.exceptions import HTTPError
from firebase_admin.messaging import Message, Notification, WebpushConfig, WebpushFCMOptions
from fcm_django.models import FCMDevice
from django.utils import timezone
from django.utils.dateparse import parse_datetime, parse_date
import stripe
from django.shortcuts import redirect
from urllib.parse import quote
from django.core.paginator import Paginator, EmptyPage
from apscheduler.schedulers.background import BackgroundScheduler
# Initialize the background scheduler
scheduler = BackgroundScheduler()
scheduler.start()

ENV_ROOT = os.path.join(settings.BASE_DIR, '.env')
load_dotenv(ENV_ROOT)

email_service = EmailService()

logger = logging.getLogger(__name__)


def serialize_model(a, serializer):

    result = {k: v for k, v in serializer(a).data.items()}
    return result


def get_expertise_data(expertise):
    p_data1 = []
    for x in expertise:
        p_data1.append(
            serialize_model(ExpertiseCancertype.objects.filter(id__exact=x.id)[0], ExpertiseCancertypeSerializer))

    return p_data1


def filter_user_data(a):
    keys = ["is_admin", "is_active", "is_superuser", "password",
            "user_permissions", "groups", "PWVerifyCode", "PWCodeGentime"]
    result = {k: v for k, v in CuUserRegisterSerializer(
        a).data.items() if k not in keys}
    result['role'] = a.groups.all()[0].name
    if result['role'] == "doctor" or result['role'] == "researcher" or result['role'] == "influencer":

        result['expertise'] = get_expertise_data(a.expertise.all())
        result['doctor_other_details'] = serialize_model(
            a.doctordetails, DoctorDetailsSerializer)
    elif result['role'] == "patient":
        result['patient_other_details'] = serialize_model(
            a.patientdetails, PatientDetailsSerializer)
    else:
        pass
    return result


def get_cu_user(a):
    return get_user_model().objects.get(id=a)


def check_if_slot_time_valid(a, b):

    converted_start_time = parse_datetime(a)
    converted_end_time = parse_datetime(b)
    t1 = timezone.make_aware(converted_start_time,
                             timezone.get_current_timezone())
    t2 = timezone.make_aware(
        converted_end_time, timezone.get_current_timezone())
    c = t2 - t1
    minutes = c.total_seconds() / 60
    return int(minutes)


def check_if_slot_alreadybooked(a):

    converted_start_time = parse_datetime((a['schedule_start_time']))
    converted_end_time = parse_datetime((a['schedule_end_time']))
    t1 = timezone.make_aware(converted_start_time,
                             timezone.get_current_timezone())
    t2 = timezone.make_aware(
        converted_end_time, timezone.get_current_timezone())

    d = get_user_model().objects.get(email=a['doctor'])
    data = SchedulerSlots.objects.filter(
        schedule_start_time__exact=t1, schedule_end_time__exact=t2, doctor__exact=d)
    return data


def check_if_slot_alreadybooked_rec(a, b, c):

    converted_start_time = parse_datetime(a)
    converted_end_time = parse_datetime(b)
    t1 = timezone.make_aware(converted_start_time,
                             timezone.get_current_timezone())
    t2 = timezone.make_aware(
        converted_end_time, timezone.get_current_timezone())

    d = get_user_model().objects.get(email=c)
    # data = SchedulerSlots.objects.filter(schedule_start_time__exact=t1,schedule_end_time__exact=t2, doctor__exact=d)
    data = SchedulerSlots.objects.filter((Q(doctor__exact=d, schedule_start_time__lte=t1, schedule_end_time__gte=t1)) | (
        Q(doctor__exact=d, schedule_start_time__lte=t2, schedule_end_time__gte=t2)))

    return data


class CreateSlot(generics.CreateAPIView):
    permission_classes = []
    queryset = SchedulerSlots.objects.all()
    serializer_class = SlotSerializer

    def create(self, request, *args, **kwargs):

        # time1 = parse_datetime(request.data['schedule_start_time'])
        # time2 = parse_datetime(request.data['schedule_end_time'])
        start_date = parse_datetime(request.data['start_date'])
        time1 = parse_datetime(str(start_date.date()) +
                               "T" + request.data['schedule_start_time'])
        time2 = parse_datetime(str(start_date.date()) +
                               "T" + request.data['schedule_end_time'])
        converted_time1 = timezone.make_aware(
            time1, timezone.get_current_timezone())
        converted_time2 = timezone.make_aware(
            time2, timezone.get_current_timezone())
        request.data['doctor'] = get_user_model().objects.filter(
            email=request.data['doctor']).first().id
        request.data['schedule_start_time'] = converted_time1
        request.data['schedule_end_time'] = converted_time2
        return super().create(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        if request.data['recurrence'] == 1:
            end_date = parse_date(request.data['end_date'])
            start_date = parse_datetime(request.data['start_date'])
            
            repeat_interval = request.data.get('repeat_interval', 1)
            
            try:
                repeat_interval = int(repeat_interval)
                if repeat_interval < 1:
                    return JsonResponse({"message": "repeat_interval must be at least 1"}, status=400)
            except (ValueError, TypeError):
                return JsonResponse({"message": "repeat_interval must be a valid integer"}, status=400)
            
            date_arr = []
            
            start_date_raw = str(start_date.date()) + "T" + request.data['schedule_start_time']
            start_date_aware = timezone.make_aware(parse_datetime(start_date_raw), timezone.get_current_timezone())
            end_date_raw = str(start_date.date()) + "T" + request.data['schedule_end_time']
            end_date_aware = timezone.make_aware(parse_datetime(end_date_raw), timezone.get_current_timezone())
            
            # Get current time for validation
            current_time = timezone.now()
            
            while start_date_aware.date() <= end_date:
                date_arr.append([start_date_aware, end_date_aware])
                start_date_aware += timedelta(days=repeat_interval)  # Use repeat_interval instead of 1
                end_date_aware += timedelta(days=repeat_interval)
            
            created_count = 0
            skipped_count = 0
            past_time_count = 0
            
            for slot_start, slot_end in date_arr:
                if slot_start <= current_time:
                    past_time_count += 1
                    skipped_count += 1
                    continue
                
                slot_start_str = slot_start.strftime('%Y-%m-%d %H:%M')
                slot_end_str = slot_end.strftime('%Y-%m-%d %H:%M')

                duration = check_if_slot_time_valid(
                    slot_start_str, slot_end_str)
                if duration not in [30, 60, 90, 120, 150, 180]:
                    skipped_count += 1
                    continue

                d = get_user_model().objects.get(email=request.data['doctor'])
                conflicts = SchedulerSlots.objects.filter(
                    (Q(doctor=d, schedule_start_time__lt=slot_end,
                     schedule_end_time__gt=slot_start))
                )

                if conflicts.exists():
                    skipped_count += 1
                    continue

                user_type = get_cu_user_type(get_user_model().objects.filter(
                    email=request.data['doctor']).first().id)
                if user_type not in ['doctor', 'researcher', 'influencer']:
                    return JsonResponse({"message": "Only experts can create appointment slots"})

                SchedulerSlots.objects.create(
                    doctor=d,
                    schedule_start_time=slot_start,
                    schedule_end_time=slot_end
                )
                created_count += 1

            if created_count == 0:
                if past_time_count > 0:
                    return JsonResponse({"message": "No slots created. All requested slots are in the past."})
                else:
                    return JsonResponse({"message": "No slots created. All requested slots were either invalid or already booked."})
            elif skipped_count == 0:
                return JsonResponse({"message": f"All {created_count} slots created successfully with {repeat_interval} day intervals."})
            else:
                msg = f"{created_count} slots created successfully with {repeat_interval} day intervals. {skipped_count} slots were skipped"
                if past_time_count > 0:
                    msg += f" ({past_time_count} were in the past)"
                msg += " due to conflicts, invalid duration, or past time."
                return JsonResponse({"message": msg})

        elif request.data['recurrence'] == 2:
            try:
                start_date = parse_datetime(request.data['start_date']).date()
                end_date = parse_date(request.data['end_date'])
                
                week_days = request.data.get('week_days', [request.data.get('week_day', 1)])  
                if not isinstance(week_days, list):
                    week_days = [week_days]
                
                weekday_name = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]

                for weekday_num in week_days:
                    if weekday_num < 1 or weekday_num > 7:
                        return JsonResponse({"message": f"Invalid weekday specified: {weekday_num}"}, status=400)

                selected_days = [weekday_name[day - 1] for day in week_days]
                doctor = get_user_model().objects.get(email=request.data['doctor'])
                user_type = get_cu_user_type(doctor.id)

                if user_type not in ['doctor', 'researcher', 'influencer']:
                    return JsonResponse({"message": "Only experts can create appointment slots"})

                date_arr, created, skipped, conflicts = [], 0, 0, []
                current_time = timezone.now()  

                while start_date <= end_date:
                    if start_date.strftime('%A') in selected_days:
                        slot_start = timezone.make_aware(
                            parse_datetime(f"{start_date}T{request.data['schedule_start_time']}"))
                        slot_end = timezone.make_aware(
                            parse_datetime(f"{start_date}T{request.data['schedule_end_time']}"))
                        date_arr.append((slot_start, slot_end))
                    start_date += timedelta(days=1)

                if not date_arr:
                    return JsonResponse({"message": "Please select a valid date range."}, status=400)

                past_time_count = 0
                for slot_start, slot_end in date_arr:
                    if slot_start <= current_time:
                        past_time_count += 1
                        skipped += 1
                        continue
                    
                    duration = check_if_slot_time_valid(slot_start.strftime('%Y-%m-%d %H:%M'),
                                                        slot_end.strftime('%Y-%m-%d %H:%M'))
                    if duration not in [30, 60, 90, 120, 150, 180]:
                        skipped += 1
                        continue

                    if SchedulerSlots.objects.filter(
                        doctor=doctor,
                        schedule_start_time__lt=slot_end,
                        schedule_end_time__gt=slot_start
                    ).exists():
                        skipped += 1
                        conflicts.append(slot_start.strftime('%Y-%m-%d'))
                        continue

                    SchedulerSlots.objects.create(
                        doctor=doctor,
                        schedule_start_time=slot_start,
                        schedule_end_time=slot_end
                    )
                    created += 1

                if created == 0:
                    msg = "No slots created."
                    if past_time_count > 0:
                        msg += f" {past_time_count} slots were in the past."
                    if conflicts:
                        msg += f" Conflicts on: {', '.join(conflicts)}."
                    return JsonResponse({"message": msg}, status=400)

                selected_days_str = ', '.join(selected_days)
                msg = f"{created} slots created for {selected_days_str}."
                if skipped:
                    msg += f" {skipped} skipped."
                    if past_time_count > 0:
                        msg += f" ({past_time_count} were in the past)"
                    if conflicts:
                        msg += f" Conflicts on: {', '.join(conflicts)}."
                return JsonResponse({"message": msg})

            except Exception as e:
                import traceback
                print(f"Error: {e}\n{traceback.format_exc()}")
                return JsonResponse({"message": f"Error: {str(e)}"}, status=400)

        elif request.data['recurrence'] == 0:
            start_date = parse_datetime(request.data['start_date'])
            time1 = str(start_date.date()) + "T" + \
                request.data['schedule_start_time']
            time2 = str(start_date.date()) + "T" + \
                request.data['schedule_end_time']

            slot_start_aware = timezone.make_aware(parse_datetime(time1), timezone.get_current_timezone())
            current_time = timezone.now()
            
            if slot_start_aware <= current_time:
                return JsonResponse({"message": "Cannot create slot in the past"}, status=400)

            r1 = check_if_slot_time_valid(time1, time2)
            if r1 in [30, 60, 90, 120, 150, 180]:
                res = check_if_slot_alreadybooked(
                    {"schedule_start_time": time1, "schedule_end_time": time2, "doctor": request.data['doctor']})
                if len(res) > 0:
                    raise APIException("Slot already booked")
                else:
                    a = get_cu_user_type(get_user_model().objects.filter(
                        email=request.data['doctor']).first().id)
                    if a in ['doctor', 'researcher', 'influencer']:

                        res1 = self.create(request, *args, **kwargs)
                        print(f"res---{res1}")

                        return res1
                    else:
                        return JsonResponse({"message": "only experts can create appointment slots"})
            else:
                raise APIException("Invalid time slot")

        elif request.data['recurrence'] == 3:
            start_date = parse_datetime(request.data['start_date'])
            end_date = parse_datetime(request.data['end_date'])
            timezone_name = request.data.get('timezone', 'UTC')
            user_timezone = zoneinfo.ZoneInfo(timezone_name)
            
            current_time = timezone.now()

            recurrence_day = request.data.get('recurrence_day')
            if recurrence_day is not None:
                recurrence_day = int(recurrence_day)
                if recurrence_day < 1 or recurrence_day > 31:
                    return JsonResponse({"message": "recurrence_day must be between 1 and 31"}, status=400)
            else:
                recurrence_day = start_date.day 

            month_interval = request.data.get('month_interval', 1) 
            if month_interval < 1:
                return JsonResponse({"message": "month_interval must be at least 1"}, status=400)

            current_date = start_date
            created_count = 0
            skipped_count = 0
            past_time_count = 0

            while current_date <= end_date:
                year = current_date.year
                month = current_date.month

                last_day = monthrange(year, month)[1]
                day = min(recurrence_day, last_day)
                slot_date = datetime(year, month, day).date()

                if slot_date < start_date.date():
                    current_date += relativedelta(months=month_interval)
                    continue
                if slot_date > end_date.date():
                    break

                start_date_raw = f"{slot_date}T{request.data['schedule_start_time']}"
                end_date_raw = f"{slot_date}T{request.data['schedule_end_time']}"

                naive_start = parse_datetime(start_date_raw)
                naive_end = parse_datetime(end_date_raw)

                slot_start = naive_start.replace(tzinfo=user_timezone)
                slot_end = naive_end.replace(tzinfo=user_timezone)
                
                if slot_start <= current_time:
                    past_time_count += 1
                    skipped_count += 1
                    current_date += relativedelta(months=month_interval)
                    continue

                slot_start_str = slot_start.strftime('%Y-%m-%d %H:%M')
                slot_end_str = slot_end.strftime('%Y-%m-%d %H:%M')

                duration = check_if_slot_time_valid(
                    slot_start_str, slot_end_str)
                if duration not in [30, 60, 90, 120, 150, 180]:
                    skipped_count += 1
                    current_date += relativedelta(months=month_interval)
                    continue

                conflicts = check_if_slot_alreadybooked_rec(
                    slot_start_str, slot_end_str, request.data['doctor'])
                if conflicts:
                    skipped_count += 1
                    current_date += relativedelta(months=month_interval)
                    continue

                user = get_user_model().objects.filter(
                    email=request.data['doctor']).first()
                user_type = get_cu_user_type(user.id)
                if user_type not in ['doctor', 'researcher', 'influencer']:
                    return JsonResponse({"message": "Only experts can create appointment slots"})

                SchedulerSlots.objects.create(
                    doctor=user,
                    schedule_start_time=slot_start,
                    schedule_end_time=slot_end,
                    timezone=timezone_name
                )
                created_count += 1
                current_date += relativedelta(months=month_interval)

            if created_count == 0:
                return JsonResponse({"message": "No slots created. All requested monthly slots were either invalid or already booked."})
            elif skipped_count == 0:
                interval_text = f"every {month_interval} month(s)" if month_interval > 1 else "monthly"
                return JsonResponse({"message": f"All {created_count} slots created successfully {interval_text} on day {recurrence_day}."})
            else:
                interval_text = f"every {month_interval} month(s)" if month_interval > 1 else "monthly"
                return JsonResponse({"message": f"{created_count} slots created successfully {interval_text} on day {recurrence_day}. {skipped_count} slots were skipped due to conflicts or invalid duration."})

        else:
            pass



@method_decorator(csrf_exempt, name="dispatch")
# get slots on a day
class GetDaySlots(View):
    def get(self, r):
        # req_date=datetime.strptime(r.GET['dateSelected'], "%Y-%m-%d")
        time1 = parse_datetime(r.GET['dateSelected'])
        req_date = timezone.make_aware(time1, timezone.get_current_timezone())
        d = get_cu_user(r.GET['doctor'])

        a = SchedulerSlots.objects.filter(Q(appointments__isnull=True, doctor__exact=d) | Q(
            doctor__exact=d, appointments__status__exact="C"))
        a = a.filter(~Q(status__exact="C"))
        date_filtered_data = []
        for x in a:
            if x.schedule_start_time.date() == req_date.date() and x.status == None:

                t_slot = x.id, timezone.localtime(x.schedule_start_time, timezone.get_current_timezone(
                )), timezone.localtime(x.schedule_end_time, timezone.get_current_timezone())
                date_filtered_data.append(list(t_slot))

        return JsonResponse({"available_slots": date_filtered_data})


@method_decorator(csrf_exempt, name="dispatch")
class GetAllSlots(View):
    def get(self, r):
        pass


def add_session_details(ApptId):
    """
    Create a MeetingSession for the given appointment and set an expiration time
    (1 hour after the meeting start time).
    """
    try:
        # Fetch the appointment to get the meeting start time
        appointment = Appointments.objects.get(id=ApptId.id)
        meeting_start_time = timezone.localtime(
            appointment.slot_id.schedule_start_time,
            timezone.get_current_timezone()
        )
        # Set expiration to 1 hour after meeting start time
        expiration_time = meeting_start_time + timedelta(hours=1)

        # Create meeting session
        session = MeetingSession.objects.create(
            AppointmentId=appointment,
            SessionId=''.join(random.choice('**********ABCDEF')
                              for i in range(16)),
            MeetingId=str(random.randint(1, 9999)).zfill(
                4),  # Ensure 4-digit MeetingId
            MeetingPwd=''.join(random.choice('**********') for i in range(8)),
            ExpirationTime=expiration_time  # Store expiration time
        )

        return session

    except Exception as e:
        print(f"Error creating meeting session: {str(e)}")
        return None


def send_meet_mail(app_obj):

    patient_details = app_obj.patient.name
    doctor_details = app_obj.slot_id.doctor.name
    doctor_id_val = app_obj.slot_id.doctor.id
    session_id = app_obj.meetingsession_set.all()[0].SessionId
    meeting_id = app_obj.meetingsession_set.all()[0].MeetingId
    meeting_pw = app_obj.meetingsession_set.all()[0].MeetingPwd
    meeting_start_time, meeting_end_time = (timezone.localtime(app_obj.slot_id.schedule_start_time, timezone.get_current_timezone()),
                                            timezone.localtime(app_obj.slot_id.schedule_end_time, timezone.get_current_timezone()))

    meeting_link1 = os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + "/consentFormPage?patientName=" + quote(patient_details + "&sessionID=" +
                                                                                                           session_id + "&role=0&appointmentId="+str(app_obj.id)+"&doctorName="+doctor_details+"&doctor_id="+str(doctor_id_val), safe='/&=')
    meeting_link2 = os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + "/consentFormPage?patientName=" + quote(patient_details + "&sessionID=" + session_id +
                                                                                                          "&role=1&appointmentId="+str(app_obj.id)+"&doctorName="+doctor_details+"&appointment_date="+str(meeting_start_time.date()), safe='/&=-')

    payload1 = {
        "template_key": "2518b.41c485fda42f6e5f.k1.c361edf0-e2bf-11ee-adf0-525400674725.18e41e2ea4f",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": app_obj.patient.email,
                    "name": patient_details
                }
            }

        ],
        "merge_info": {
            "p_name": patient_details,
            "d_name": doctor_details,
            "session_id": session_id,
            "meeting_pw": meeting_pw,
            "meeting_start_time": meeting_start_time.strftime('%Y-%m-%d %H:%M'),
            "meeting_end_time": meeting_end_time.strftime('%Y-%m-%d %H:%M'),
            "meeting_link": meeting_link1,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }

    payload2 = {
        "template_key": "2518b.41c485fda42f6e5f.k1.373962d0-e2c0-11ee-adf0-525400674725.18e41e5e17d",
        # "bounce_address": "<EMAIL>",
        "from": {
            "address": "<EMAIL>",
            "name": "Health Unwired"
        },
        "to": [
            {
                "email_address": {
                    "address": app_obj.slot_id.doctor.email,
                    "name": doctor_details
                }
            }

        ],
        "merge_info": {
            "p_name": patient_details,
            "d_name": doctor_details,
            "session_id": session_id,
            "meeting_pw": meeting_pw,
            "meeting_start_time": meeting_start_time.strftime('%Y-%m-%d %H:%M'),
            "meeting_end_time": meeting_end_time.strftime('%Y-%m-%d %H:%M'),
            "meeting_link": meeting_link2,
            "fb_url": os.getenv("fb_url"),
            "insta_url": os.getenv("insta_url"),
            "twitter_url": os.getenv("twitter_url"),
            "linkedin_url": os.getenv("linkedin_url"),
            "youtube_url": os.getenv("youtube_url"),
        },
    }

    headers = {
        'Authorization': f'{os.getenv("ZEPTOMAIL_TOKEN")}',
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    }
    zeptomail_url = "https://api.zeptomail.in/v1.1/email/template"
    try:

        response = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload1))
        response1 = requests.request(
            "POST", zeptomail_url, headers=headers, data=json.dumps(payload2))

        response_status1 = True if response.status_code == 200 else True if response.status_code in [
            201, 202] else False
        response_status2 = True if response1.status_code == 200 else True if response1.status_code in [
            201, 202] else False
        return response_status1 and response_status2
    except HTTPError as e:
        return response_status1 and response_status2


def SendPushNotification(doctor_id, patient_name):
    device = FCMDevice.objects.filter(user_id__exact=doctor_id)

    u_name = get_user_model().objects.get(id__exact=doctor_id).name
    img_url = settings.MEDIA_ROOT+"\logo.png"
    current_time = timezone.now()
    a = device.send_message(Message(notification=Notification(title='Appointment scheduled!', body=f'Hi {u_name}, An appointment has been fixed with {patient_name}!!'), webpush=WebpushConfig(
        fcm_options=WebpushFCMOptions(link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP')+'/appointment'))))
    res = PushNotifications.objects.create(UserId=doctor_id, NotificationTime=current_time,
                                           Title='Appointment scheduled!', Body=f'Hi {u_name}, An appointment has been fixed with {patient_name}!!', Link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP')+'/appointment')
    print(f"push----{a}")
    return a


class ValidateMeetingSessionView(views.APIView):
    """
    API to validate a meeting session's expiration and details.
    Used by Next.js frontend to check if a meeting link is valid.
    """

    def post(self, request):
        try:
            session_id = request.data.get('sessionID')
            meeting_id = request.data.get('meetingId')
            appointment_id = request.data.get('appointmentId')

            # Validate required fields
            if not all([session_id, meeting_id, appointment_id]):
                return JsonResponse({"error": "Missing required parameters"}, status=400)

            # Fetch meeting session
            session = MeetingSession.objects.filter(
                SessionId=session_id,
                MeetingId=meeting_id,
                AppointmentId_id=appointment_id
            ).first()

            if not session:
                return JsonResponse({"error": "Invalid session or appointment"}, status=404)

            # Check expiration
            current_time = timezone.now()
            if current_time > session.ExpirationTime:
                return JsonResponse({"message": "This meeting link has expired"}, status=403)

            # Return session details if valid
            return JsonResponse({
                "status": "valid",
                "session_id": session.SessionId,
                "meeting_id": session.MeetingId,
                "appointment_id": session.AppointmentId_id,
                "expiration_time": session.ExpirationTime.strftime('%Y-%m-%d %H:%M')
            })

        except Exception as e:
            print(f"Error validating meeting session: {str(e)}")
            return JsonResponse({"error": "An error occurred"}, status=500)


class GetUserAppointments(generics.ListAPIView):
    queryset = Appointments.objects.all()
    serializer_class = AppointmentsSerializer
    lookup_field = 'id'

    def get_queryset(self):
        id_val = self.kwargs['id']
        user_role = get_cu_user_type(id_val)
        f = Appointments.objects.all()
        if user_role == "patient":
            f = f.filter(patient__id=id_val)
            if "name" in self.request.GET and self.request.GET['name'] != '':
                try:
                    name_id = int(self.request.GET['name'])
                    f = f.filter(slot_id__doctor__id__exact=name_id) | f.filter(
                        slot_id__doctor__name__icontains=self.request.GET['name'])
                except ValueError:
                    f = f.filter(
                        slot_id__doctor__name__icontains=self.request.GET['name'])

        elif user_role == "doctor" or user_role == "researcher" or user_role == "influencer":
            f = f.filter(slot_id__doctor__exact=id_val)
            if "name" in self.request.GET and self.request.GET['name'] != '':
                try:
                    name_id = int(self.request.GET['name'])
                    f = f.filter(patient__id__icontains=self.request.GET['name']) | f.filter(
                        patient__name__icontains=self.request.GET['name'])
                except ValueError:
                    f = f.filter(
                        patient__name__icontains=self.request.GET['name'])

        else:
            f = []

        if "status" in self.request.GET and self.request.GET['status'] != '':
            if self.request.GET['status'] == "Ongoing":
                meeting_d = MeetingSession.objects.filter(
                    MeetingStatus__exact=1, AppointmentId__status__in=['B', 'R', 'P'])
                appoint_ids = meeting_d.values("AppointmentId")
                f = f.filter(id__in=appoint_ids)
            elif self.request.GET['status'] == "Unattended":
                meeting_d = MeetingSession.objects.filter(
                    MeetingStatus__exact=3, AppointmentId__status__in=['B', 'R', 'P'])
                appoint_ids = meeting_d.values("AppointmentId")
                f = f.filter(id__in=appoint_ids)
            elif self.request.GET['status'] == "Expired":
                meeting_d = MeetingSession.objects.filter(
                    MeetingStatus__exact=0)
                appoint_ids = meeting_d.values("AppointmentId")
                f = f.filter(id__in=appoint_ids, slot_id__schedule_end_time__lte=timezone.now(
                ), status__in=["B", "R"])
            elif self.request.GET['status'] == "Upcoming":
                f = f.filter(
                    status__in=["B", "R"], slot_id__schedule_start_time__gte=timezone.now())
            elif self.request.GET['status'] == "Completed":
                meeting_d = MeetingSession.objects.filter(
                    MeetingStatus__exact=2, AppointmentId__status__in=['B', 'R', 'P'])
                appoint_ids = meeting_d.values("AppointmentId")
                f = f.filter(id__in=appoint_ids)
            elif self.request.GET['status'] == "Rescheduled":
                f = f.filter(status__exact="R",
                             slot_id__schedule_start_time__gte=timezone.now())
            elif self.request.GET['status'] == "Cancelled":
                f = f.filter(status__exact="C")
            elif self.request.GET['status'] == "Cancellation Rejected":
                f = f.filter(status__exact="C_R")
            elif self.request.GET['status'] == "Cancellation Pending":
                f = f.filter(status__exact="C_P")
            else:
                print("-----------not a valid appointment status----------")

        if "start_date" in self.request.GET and self.request.GET['start_date'] != '':
            start_date = timezone.make_aware(parse_datetime(self.request.GET['start_date']),
                                             timezone.get_current_timezone())
            f = f.filter(slot_id__schedule_start_time__gte=start_date)

        if "end_date" in self.request.GET and self.request.GET['end_date'] != '':
            end_date = timezone.make_aware(parse_datetime(self.request.GET['end_date']),
                                           timezone.get_current_timezone())
            f = f.filter(slot_id__schedule_end_time__lte=end_date)
        return f.order_by('-id')

    def get(self, request, *args, **kwargs):
        res = self.list(request, *args, **kwargs)
        if 'page' in request.GET and request.GET['page'] != "":
            page_number = request.GET.get('page', 1)
            items_per_page = request.GET.get('per_page', 10)
            total_items = len(res.data)
            paginator = Paginator(res.data, items_per_page)
            if int(page_number) not in range(1, int(paginator.num_pages)+1):
                return HttpResponse("Not a valid page number", status=400)
            res.data = paginator.page(page_number)

        app_data_items = []
        for x in res.data:
            slot = SchedulerSlots.objects.filter(
                id__exact=x['slot_id']).first()
            if not slot:
                continue

            doc = slot.doctor.id
            patient = CuUser.objects.filter(id__exact=x['patient']).first()
            if not patient:
                continue

            # Doctor availability check
            d_a_slots = SchedulerSlots.objects.filter(
                doctor_id__exact=doc).exclude(status__exact="C")
            d_a_x = []
            for d in d_a_slots:
                if Appointments.objects.filter(slot_id_id=d.id).exists():
                    d_a_x.append('0')
                else:
                    if (d.schedule_start_time - timezone.now()).days >= 0:
                        d_a_x.append('1')
                    else:
                        d_a_x.append('0')
            x['doctor_available_slot'] = 1 if "1" in d_a_x else 0

            # Prescriptions
            user_role = get_cu_user_type(doc)
            if user_role in ['researcher', 'influencer']:
                pres = Appointments.objects.filter(
                    id__exact=x['id']).first().irprescription_set.all()
            elif user_role == 'doctor':
                pres = Appointments.objects.filter(
                    id__exact=x['id']).first().prescription_set.all()
            else:
                pres = []
            pres_ids = [z.id for z in pres]

            # Cancel reason
            cancel_reason = AppointmentMgmt.objects.filter(
                AppId_id__exact=x['id'], EventType__exact="Cancelled").first()
            x['appointment_cancel_reason'] = cancel_reason.AppointmentReason if cancel_reason else ""

            rejection_reason = AppointmentMgmt.objects.filter(
                AppId_id__exact=x['id'], EventType__exact="Cancelled").only("RejectionReason").first()
            x['appointment_rejection_reason'] = rejection_reason.RejectionReason if rejection_reason else ""

            # Patient queries
            p_queries = Appointments.objects.filter(
                id__exact=x['id']).first().patientqueries_set.all()
            p_queries_list1 = ['0', '0']
            if p_queries.exists():
                p_queries_list1[0] = "1"
                if PatientQueries.objects.filter(ReplyTo__exact=p_queries.first().id).exists():
                    p_queries_list1[1] = "1"

            # Meeting and consent details
            meeting_session_details = Appointments.objects.filter(
                id__exact=x['id']).first().meetingsession_set.all()
            consent_details = AppointmentConsent.objects.filter(
                AppId_id__exact=x['id'])  # Changed from appointment to AppId_id

            # Time conversions
            converted_time1 = timezone.localtime(
                slot.schedule_start_time, timezone.get_current_timezone())
            converted_time2 = timezone.localtime(
                slot.schedule_end_time, timezone.get_current_timezone())

            # Patient data
            x['patient'] = serialize_model(patient, CuUserSerializer)
            p_o_data = filter_user_data(patient)
            if p_o_data['patient_other_details']['ProfilePhoto'] is not None:
                x['patient_ProfilePhoto'] = get_s3_signed_url_bykey(
                    p_o_data['patient_other_details']['ProfilePhoto'])
            if p_o_data['patient_other_details']['Signature'] is not None:
                x['patient_Signature'] = get_s3_signed_url_bykey(
                    p_o_data['patient_other_details']['Signature'])

            # Doctor data
            doctor = slot.doctor
            doctor_data = filter_user_data(doctor)
            x['doctor_id'] = doc
            x['doctor_email'] = doctor_data['email']
            x['doctor_approval'] = doctor_data['approval']
            x['doctor_name'] = doctor_data['name']
            x['expert_role'] = doctor_data['role']
            x['expert_prefix'] = doctor_data['prefix']
            x['doctor_department'] = doctor_data['doctor_other_details']['Dept']
            x['doctor_member_code'] = doctor_data['doctor_other_details']['MemberCode']
            if doctor_data['doctor_other_details']['ProfilePhoto'] is not None:
                x['doctor_ProfilePhoto'] = get_s3_signed_url_bykey(
                    doctor_data['doctor_other_details']['ProfilePhoto'])
            if doctor_data['doctor_other_details']['Signature'] is not None:
                x['doctor_Signature'] = get_s3_signed_url_bykey(
                    doctor_data['doctor_other_details']['Signature'])

            # Ratings
            Apps = Appointments.objects.filter(
                slot_id__doctor__exact=doctor.id)
            doctors_s = CuUser.objects.filter(id__exact=doctor.id)
            stories_count = 0
            reviews_count = 0
            rating_s = 0
            rating_r = 0
            for z in Apps:
                stories = z.patientstories_set.all()
                stories_count += stories.count()
                for v in stories:
                    rating_s += v.Rating
            for z in doctors_s:
                reviews = z.doctorreviews_set.all()
                for u in reviews:
                    if u.ReviewStatus == 2:
                        rating_r += u.ReviewRating
                        reviews_count += 1
            rating_in_number1 = int(
                rating_s/stories_count) if stories_count != 0 else 0
            rating_in_number2 = int(
                rating_r/reviews_count) if reviews_count != 0 else 0
            rating_in_number = (int(rating_in_number1+rating_in_number2)/2)

            # Current status
            current_status = ''
            if x['status'] in ['B', 'R']:
                if timezone.localtime(timezone.now(), timezone.get_current_timezone()).timestamp() >= timezone.localtime(slot.schedule_end_time, timezone.get_current_timezone()).timestamp():
                    if meeting_session_details and meeting_session_details[0].MeetingStatus == 0:
                        current_status = "Expired"
                    elif meeting_session_details and meeting_session_details[0].MeetingStatus == 2:
                        current_status = "Completed"
                    elif meeting_session_details and meeting_session_details[0].MeetingStatus == 1:
                        current_status = "Ongoing"
                    elif meeting_session_details and meeting_session_details[0].MeetingStatus == 3:
                        current_status = "Unattended"
                elif meeting_session_details and meeting_session_details[0].MeetingStatus == 3:
                    current_status = "Unattended"
                elif meeting_session_details and meeting_session_details[0].MeetingStatus == 2:
                    current_status = "Completed"
                else:
                    current_status = "Upcoming"
            x['current_status'] = current_status

            # Final data assembly
            x.update({
                'prescriptions': pres_ids,
                'p_queries': p_queries_list1,
                "patient_stories": stories_count,
                "doctor_reviews": reviews_count,
                "doctor_rating": rating_in_number,
                'slot_start_time': converted_time1,
                'slot_end_time': converted_time2,
                'meeting_session_details': [serialize_model(t, MeetingSessionSerializer) for t in meeting_session_details],
                'consent_details': [serialize_model(t, AppointmentConsentSerializer) for t in consent_details],
            })

            app_data_items.append(x)

        if 'page' in request.GET and request.GET['page'] != "":
            response_data = {
                'total_items': total_items,
                'total_pages': paginator.num_pages,
                'items': app_data_items
            }
            return JsonResponse(response_data)
        else:
            return JsonResponse(app_data_items, safe=False)


class StartMeetingSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()

        # Check if the meeting is expired
        current_time = timezone.now()
        if instance.ExpirationTime and current_time > instance.ExpirationTime:
            return JsonResponse(
                {"message": "This meeting has expired"},
                status=403
            )

        instance.IsDoctorPresent = True
        # instance.SessionStartTime=datetime.now()
        instance.SessionStartTime = timezone.now()
        instance.MeetingStatus = 1
        instance.save(
            update_fields=['IsDoctorPresent', 'SessionStartTime', 'MeetingStatus'])
        return instance

    def put(self, request, *args, **kwargs):
        res = self.partial_update(request, *args, **kwargs)

        # Check if res is a JsonResponse (error case)
        if isinstance(res, JsonResponse):
            return res

        # Success case: serialize the MeetingSession instance
        res_data = {
            'session_data': serialize_model(res, MeetingSessionSerializer),
            'role': 1 if self.kwargs.get('role') == "doctor" else 0
        }
        return JsonResponse(res_data)

    # def put(self, request, *args, **kwargs):

    #     res = self.partial_update(request, *args, **kwargs)
    #     res_data = dict()
    #     res_data['session_data'] = serialize_model(
    #         res, MeetingSessionSerializer)
    #     res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0
    #     return JsonResponse(res_data)


class AllowJoinMeetingSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        print(f"allowwww--------{self.kwargs.get('approval')}")

        instance.PatientJoinApproval = True if self.kwargs.get(
            'approval') == 1 else False
        instance.PatientRequest = False if self.kwargs.get(
            'approval') == 1 else True
        instance.DoctorAllowPatient = 2 if self.kwargs.get(
            'approval') == 1 else 0
        instance.save(
            update_fields=['PatientJoinApproval', 'PatientRequest', 'DoctorAllowPatient'])
        return instance

    def put(self, request, *args, **kwargs):
        obj1 = self.get_object()
        if obj1.MeetingStatus == 1:
            res = self.partial_update(request, *args, **kwargs)
            res_data = dict()
            res_data['session_data'] = serialize_model(
                res, MeetingSessionSerializer)
            res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0

        else:
            return JsonResponse({"message": "meeting not yet started by doctor"})

        return JsonResponse(res_data)
        # return JsonResponse(serialize_model(res, MeetingSessionSerializer))


class JoinMeetingSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        # Check if the meeting is expired

        instance.IsPatientPresent = True
        # instance.PatientJoinTime=datetime.now()
        instance.PatientJoinTime = timezone.now()
        instance.save(update_fields=['IsPatientPresent', 'PatientJoinTime'])
        return instance

    def put(self, request, *args, **kwargs):
        obj1 = self.get_object()
        current_time = timezone.now()
        if obj1.ExpirationTime and current_time > obj1.ExpirationTime:
            return JsonResponse({"message": "This meeting has expired"}, status=403)

        if obj1.MeetingStatus == 1:
            if obj1.PatientJoinApproval == True:
                res = self.partial_update(request, *args, **kwargs)
                res_data = dict()
                res_data['session_data'] = serialize_model(
                    res, MeetingSessionSerializer)
                res_data['role'] = 1 if self.kwargs.get(
                    'role') == "doctor" else 0
            else:
                return JsonResponse({"message": "Patient not yet allowed to join by the doctor"})

        else:
            return JsonResponse({"message": "meeting not yet started by doctor"})

        return JsonResponse(res_data)


class LeaveMeetingSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.IsPatientPresent = False
        # instance.PatientLeaveTime=datetime.now()
        instance.PatientLeaveTime = timezone.now()
        instance.PatientJoinApproval = False
        instance.PatientRequest = False
        instance.save(update_fields=[
                      'PatientJoinApproval', 'PatientLeaveTime', 'IsPatientPresent', 'PatientRequest'])
        return instance

    def put(self, request, *args, **kwargs):
        res = self.partial_update(request, *args, **kwargs)
        res_data = dict()
        res_data['session_data'] = serialize_model(
            res, MeetingSessionSerializer)
        res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0
        return JsonResponse(res_data)


def perform_action(app_id):
    app_obj = Appointments.objects.get(id__exact=app_id)
    pq_obj = PatientQueries.objects.filter(ApptId_id__exact=app_id)
    print("in perform action")
    if pq_obj.exists():
        print("in perform_action")
        # adding for the expert transaction table
        start_time = app_obj.slot_id.schedule_start_time
        end_time = app_obj.slot_id.schedule_end_time
        f_time = (end_time-start_time).total_seconds()
        f_time = f_time/1800
        transaction_amount = f_time * app_obj.slot_id.doctor.doctordetails.ConsultationFees
        c_p = app_obj.slot_id.doctor.doctordetails.CommissionPercentage
        c_a = 0
        if c_p > 0:
            c_a = app_obj.slot_id.doctor.doctordetails.ConsultationFees - \
                app_obj.slot_id.doctor.doctordetails.ConsultationFees * c_p * 0.01
        transaction_amount = transaction_amount - c_a
        wallet_obj = ExpertWalletTransactions.objects.filter(
            WalletId__ExpertId__exact=app_obj.slot_id.doctor.id)
        wallet_exp = ExpertWallet.objects.filter(
            ExpertId_id__exact=app_obj.slot_id.doctor.id)[0]
        app_mgm = AppointmentMgmt.objects.create(AppId_id=app_id, EventType="Expert_unpaid_appointment", By=CuUser.objects.get(
            id=app_obj.slot_id.doctor.id), AppointmentReason="Expert not replied to the query")
        if wallet_obj.exists():
            wallet_obj = wallet_obj.order_by('-TransactionDate')[0]
            balance = wallet_obj.BalanceAmount
            wallet_row = ExpertWalletTransactions.objects.create(
                BalanceAmount=balance, WalletId_id=wallet_exp.id, TransactionType=2, TransactionAmount=transaction_amount, CommissionAmount=c_a)
        else:
            balance = 0
            wallet_row = ExpertWalletTransactions.objects.create(
                BalanceAmount=balance, WalletId_id=wallet_exp.id, TransactionType=2, TransactionAmount=transaction_amount, CommissionAmount=c_a)
    else:
        print("in perform/action")
        # adding for the expert transaction table
        start_time = app_obj.slot_id.schedule_start_time
        end_time = app_obj.slot_id.schedule_end_time
        f_time = (end_time-start_time).total_seconds()
        f_time = f_time/1800
        transaction_amount = f_time * app_obj.slot_id.doctor.doctordetails.ConsultationFees
        c_p = app_obj.slot_id.doctor.doctordetails.CommissionPercentage
        c_a = 0
        if c_p > 0:
            c_a = app_obj.slot_id.doctor.doctordetails.ConsultationFees * c_p * 0.01
        transaction_amount = transaction_amount - c_a
        wallet_obj = ExpertWalletTransactions.objects.filter(
            WalletId__ExpertId__exact=app_obj.slot_id.doctor.id)
        wallet_exp = ExpertWallet.objects.filter(
            ExpertId_id__exact=app_obj.slot_id.doctor.id)[0]
        if wallet_obj.exists():
            wallet_obj = wallet_obj.order_by('-TransactionDate')[0]
            balance = wallet_obj.BalanceAmount + transaction_amount
            wallet_row = ExpertWalletTransactions.objects.create(
                BalanceAmount=balance, WalletId_id=wallet_exp.id, TransactionType=0, TransactionAmount=transaction_amount, CommissionAmount=c_a)
        else:
            balance = transaction_amount
            wallet_row = ExpertWalletTransactions.objects.create(
                BalanceAmount=balance, WalletId_id=wallet_exp.id, TransactionType=0, TransactionAmount=transaction_amount, CommissionAmount=c_a)
    print("Add the action you want to perform here")

# Function to schedule the task on the third day


def schedule_task(app_id, end_time):
    third_day_time = end_time + timedelta(days=3)
    scheduler.add_job(perform_action, 'date',
                      run_date=third_day_time, args=[app_id])


class EndMeetingSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        instance.IsPatientPresent = False
        instance.IsDoctorPresent = False
        # instance.SessionEndTime=datetime.now()
        instance.SessionEndTime = timezone.now()
        # instance.MeetingStatus=2
        # added
        if instance.PatientJoinTime is None:
            instance.MeetingStatus = 3
        else:
            instance.MeetingStatus = 2
        # ---------------------------------------
        instance.IsSuccess = 1
        instance.PatientJoinApproval = False
        instance.PatientRequest = False
        instance.DoctorAllowPatient = 1
        instance.save(update_fields=['IsPatientPresent', 'IsDoctorPresent', 'SessionEndTime',
                      'MeetingStatus', 'IsSuccess', 'PatientJoinApproval', 'PatientRequest', 'DoctorAllowPatient'])
        # added for background_job
        schedule_task(instance.AppointmentId_id, timezone.now())
        return instance

    def put(self, request, *args, **kwargs):
        res = self.partial_update(request, *args, **kwargs)
        res_data = dict()
        res_data['session_data'] = serialize_model(
            res, MeetingSessionSerializer)
        res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0
        return JsonResponse(res_data)


class PatientRequestSession(generics.UpdateAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        if instance.IsDoctorPresent == True:
            instance.PatientRequest = True
            instance.DoctorAllowPatient = 1
        instance.save(update_fields=['PatientRequest', 'DoctorAllowPatient'])
        return instance

    def put(self, request, *args, **kwargs):
        res = self.partial_update(request, *args, **kwargs)
        res_data = dict()
        res_data['session_data'] = serialize_model(
            res, MeetingSessionSerializer)
        res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0
        return JsonResponse(res_data)


class CheckPatientSessionApproval(generics.RetrieveAPIView):
    queryset = MeetingSession.objects.all()
    lookup_field = 'SessionId'
    serializer_class = MeetingSessionSerializer

    def get(self, request, *args, **kwargs):
        res = self.retrieve(request, *args, **kwargs)
        patient_name = self.get_object().AppointmentId.patient.name

        res_data = dict()

        res_data['session_data'] = res.data
        res_data['role'] = 1 if self.kwargs.get('role') == "doctor" else 0
        res_data['patient'] = patient_name
        return JsonResponse(res_data)


logger = logging.getLogger(__name__)


class AppointmentsView(generics.UpdateAPIView):
    queryset = Appointments.objects.all()
    lookup_field = 'id'
    serializer_class = AppointmentsSerializer

    def _process_expert_cancellation_refund(self, appointment_id):
        try:
            appointment = Appointments.objects.get(id=appointment_id)
            payment = PatientPayment.objects.get(AppointmentId=appointment)
            airwallex = AirwallexService()
            payment_intent = airwallex.get_payment_intent(
                payment.PaymentIntent)
            original_amount = Decimal(payment_intent['amount'])

            refund_response = airwallex.create_refund(
                payment_intent_id=payment.PaymentIntent,
                amount=float(original_amount),
                currency=payment_intent['currency'],
                reason="Full refund due to expert cancellation"
            )

            patient = appointment.patient
            doctor = appointment.slot_id.doctor

            refund_amount = float(original_amount)
            patient_url = os.getenv(
                'NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=payments'
            doctor_url = os.getenv(
            'NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment'

            slot_details = get_appointment_details(
                slot_id=appointment.slot_id_id,
                doctor_id=doctor.id
            )
            appointment_timings = slot_details["appointment_timings"]

            if email_check('Refund credit'):
                email_service.refund_email_patient(
                    patient.email,
                    refund_amount, patient.name, doctor.name, appointment.id, appointment_timings
                )

            if notification_check('Refund credit'):
                send_generic_push_notification(
                    user_id=patient.id,
                    title="Refund credited!",
                    body=f'Hi {patient.name}, Your refund for amount {refund_amount} has been credited.',
                    link=patient_url
                )
            if notification_check('Appointment cancellation'):
                send_generic_push_notification(
                    user_id=doctor.id,
                    title="Apointment Cancelled!",
                    body=f'Hi {doctor.name}, Your appointment with {patient.name} has been cancelled successfully.',
                    link=doctor_url
                )

            RefundedPayments.objects.create(
                PaymentIntent=payment.PaymentIntent,
                RefundId=refund_response['id'],
                AmountRefunded=original_amount,
                RefundStatus=refund_response['status'],
                RefundCurrency=payment_intent['currency'],
                AppId=appointment,
                RefundDate=timezone.now(),
                RefundObject={
                    "refund_type": "full_refund",
                    "reason": "Expert-initiated cancellation",
                    "original_amount": float(original_amount),
                    "refund_response": refund_response
                }
            )

            return {
                "success": True,
                "refunded_amount": float(original_amount),
                "currency": payment_intent['currency'],
                "status": refund_response['status']
            }

        except Exception as e:
            logger.exception("Error during refund processing")
            return {"success": False, "message": str(e)}

    def _send_expert_cancellation_notifications(self, appointment):
        try:
            patient_id = appointment.patient_id
            patient = appointment.patient
            doctor_id = appointment.slot_id.doctor_id
            slot_id = appointment.slot_id_id
            slot = appointment.slot_id
            doctor = slot.doctor

            doctor_url = os.getenv(
                'NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment'
            patient_url = os.getenv(
                'NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments'
            admin_url = os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar'

            if notification_check('Appointment cancellation'):
                send_generic_push_notification(
                    user_id=patient_id,
                    title="Appointment cancellation!",
                    body=f"Hi {patient.name}, Your upcoming appointment with {doctor.name} is cancelled.",
                    link=patient_url
                )
                send_generic_push_notification(
                    user_id=doctor_id,
                    title="Appointment cancellation!",
                    body=f"Hi {doctor.name}, Your upcoming appointment with {patient.name} is cancelled.",
                    link=doctor_url
                )
                send_generic_push_notification(
                    user_id=None,
                    title="Appointment cancellation!",
                    body=f"Appointment with id -{appointment.id} has been cancelled by expert!'",
                    link=admin_url,
                    send_to_admins=True
                )

            details = get_appointment_details(
                slot_id=slot_id, doctor_id=doctor_id)

            appointment_timings = details["appointment_timings"]
            expert_role = details["expert_role"]

            if email_check('Appointment cancellation'):
                email_service.cancellation_email_patient(
                    patient.email, patient.name, patient_url, patient.name, appointment_timings)
                email_service.cancellation_email_expert(
                    doctor.email, doctor.name, doctor_url, patient.name, appointment_timings, expert_role)

            return True
        except Exception as e:
            logger.exception("Notification error during cancellation")
            return False

    def _send_reschedule_email_and_notification(self, doctor, patient, instance, prev_slot_id, new_slot):
        details_prev = get_appointment_details(
            slot_id=prev_slot_id, doctor_id=doctor.id)
        details_new = get_appointment_details(
            slot_id=new_slot.id, doctor_id=doctor.id)

        new_appointment_timings = details_new["appointment_timings"]
        prev_appointment_timings = details_prev["appointment_timings"]
        expert_role = details_prev["expert_role"]

        try:
            doctor_url = os.getenv(
                'NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment'
            patient_url = os.getenv(
                'NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments'
            admin_url = os.getenv(
                'NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar'

            if notification_check('Appointment reschedule'):
                send_generic_push_notification(
                    user_id=patient.id,
                    title="Appointment reschedule!",
                    body=f'Hi {patient.name}, Appointment with {doctor.name} reschedueled to {new_appointment_timings} from {prev_appointment_timings}!',
                    link=patient_url
                )
                send_generic_push_notification(
                    user_id=doctor.id,
                    title="Appointment reschedule!",
                    body=f'Hi {doctor.name}, Appointment with {patient.name} reschedueled to {new_appointment_timings} from {prev_appointment_timings}!',
                    link=doctor_url
                )
                send_generic_push_notification(
                    user_id=None,
                    title="Appointment reschedule!",
                    body=f'Hi, Appointment with id -{instance.id} has been reschedueled to {new_appointment_timings} from {prev_appointment_timings}!',
                    link=admin_url,
                    send_to_admins=True
                )
            if email_check('Appointment reschedule'):
                email_service.reschedule_email_patient(
                    patient.email, patient.name, patient_url, new_appointment_timings, doctor.name, instance.id, prev_appointment_timings)
                email_service.reschedule_email_expert(
                    doctor.email, doctor.name, doctor_url, patient.name, new_appointment_timings, expert_role, instance.id, prev_appointment_timings)

        except Exception as e:
            logger.warning(
                f"Reschedule notification and emails failed: {str(e)}")

    def partial_update(self, request, *args, **kwargs):
        try:
            appointment_id = self.kwargs.get('id')
            event_type = request.data.get('EventType')
            user_id = request.data.get('By')

            instance = self.get_object()

            patient = instance.patient
            doctor = instance.slot_id.doctor
            slot_id = instance.slot_id_id

            details = get_appointment_details(
                slot_id=slot_id, doctor_id=doctor.id)

            appointment_timings = details["appointment_timings"]

            if event_type == "Cancelled":
                # instance = self.get_object()
                if instance.status in ["B", "R"]:
                    user_type = get_cu_user_type(user_id)

                    if user_type == "patient":
                        reason = request.data.get("AppointmentReason")
                        if not reason:
                            return Response("Appointment cancellation reason not specified", status=400)

                        instance.status = "C_P"
                        instance.save(update_fields=['status'])

                        AppointmentMgmt.objects.create(
                            AppId=instance,
                            EventType=event_type,
                            By=get_user_model().objects.get(id=user_id),
                            EventDate=timezone.now(),
                            AppointmentReason=reason
                        )

                        patient_url = os.getenv(
                            'NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments'
                        admin_url = os.getenv(
                            'NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar'
                        try:
                            if notification_check('Appointment cancellation'):
                                send_generic_push_notification(
                                    user_id=patient.id,
                                    title="Cancellation Request Sent!",
                                    body=f"Hi {patient.name}, Your cancellation request for the appointment with {doctor.name} has been sent to the admin.",
                                    link=patient_url
                                )
                                send_generic_push_notification(
                                    user_id=None,
                                    title="Appointment Cancellation Request!",
                                    body=f"{patient.name} has requested cancellation for appointment ID {instance.id}.",
                                    link=admin_url,
                                    send_to_admins=True
                                )

                            email_service.notify_admin_email(
                                template_key_name="APPOINTMENT_CANCELLATION_REQUEST_ADMIN",
                                custom_fields={
                                    "expert_name": doctor.name,
                                    "patient_name": patient.name,
                                    "appointment_id": instance.id,
                                    "appointment_timings": appointment_timings,
                                }
                            )

                        except Exception as e:
                            logger.warning(
                                f"Patient cancellation notification failed: {str(e)}")

                    else:
                        instance.status = "C"
                        instance.save(update_fields=['status'])

                        slot = SchedulerSlots.objects.get(
                            id=instance.slot_id_id)
                        slot.status = "C"
                        slot.save(update_fields=['status'])

                        AppointmentMgmt.objects.create(
                            AppId=instance,
                            EventType=event_type,
                            By=get_user_model().objects.get(id=user_id),
                            EventDate=timezone.now()
                        )

                        refund_result = self._process_expert_cancellation_refund(
                            instance.id)
                        
                        if not refund_result.get('success'):
                            return JsonResponse({
                                "message": "Appointment cancelled but refund failed - please contact support"
                            }, status=status.HTTP_207_MULTI_STATUS)

                        self._send_expert_cancellation_notifications(instance)

                else:
                    return Response({"message": "Only booked or rescheduled appointments can be cancelled."}, status=400)

            elif event_type == 'Rescheduled':
                flag = request.data.get('s_f')
                app = Appointments.objects.get(id=appointment_id)
                prev_slot_id = app.slot_id_id

                if flag == 0:
                    slot = SchedulerSlots.objects.filter(
                        id=request.data.get('s_id')).first()
                    if slot and timezone.now() < slot.schedule_start_time:
                        if get_cu_user_type(user_id) in ['doctor', 'researcher', 'influencer']:
                            old_slot = SchedulerSlots.objects.get(
                                id=prev_slot_id)
                            old_slot.status = None  # Set to None instead of 'R' to make it available again
                            old_slot.save()

                            # Update the new slot's status to 'B' (Booked)
                            slot.status = 'B'  # Mark the new slot as Booked
                            slot.save(update_fields=['status'])

                        app.slot_id_id = slot.id
                        app.status = 'R'
                        app.save()

                        self._send_reschedule_email_and_notification(
                            doctor, patient, instance, prev_slot_id, slot)

                        AppointmentMgmt.objects.create(
                            AppId=app,
                            EventType=event_type,
                            By=get_user_model().objects.get(id=user_id),
                            EventDate=timezone.now()
                        )

                elif flag == 1:
                    start_date = request.data.get('start_date')
                    time1 = parse_datetime(
                        f"{start_date}T{request.data.get('schedule_start_time')}")
                    time2 = parse_datetime(
                        f"{start_date}T{request.data.get('schedule_end_time')}")

                    converted_time1 = timezone.make_aware(
                        time1, timezone.get_current_timezone())
                    converted_time2 = timezone.make_aware(
                        time2, timezone.get_current_timezone())

                    duration = check_if_slot_time_valid(str(time1), str(time2))
                    doctor_email = CuUser.objects.get(id=user_id).email
                    slot_conflict = check_if_slot_alreadybooked({
                        "schedule_start_time": str(time1),
                        "schedule_end_time": str(time2),
                        "doctor": doctor_email
                    })

                    if duration in [30, 60, 90, 120, 150, 180] and not slot_conflict:
                        if get_cu_user_type(user_id) in ['doctor', 'researcher', 'influencer']:
                            new_slot = SchedulerSlots.objects.create(
                                schedule_start_time=converted_time1,
                                schedule_end_time=converted_time2,
                                doctor_id=user_id,
                                timezone=timezone.get_current_timezone()
                            )

                            SchedulerSlots.objects.filter(
                                id=prev_slot_id).update(status=None)  # Set to None instead of 'R'
                            app.slot_id_id = new_slot.id
                            app.status = 'R'
                            app.save()

                            self._send_reschedule_email_and_notification(
                                doctor, patient, instance, prev_slot_id, new_slot)

                            AppointmentMgmt.objects.create(
                                AppId=app,
                                EventType=event_type,
                                By=get_user_model().objects.get(id=user_id),
                                EventDate=timezone.now()
                            )
                    else:
                        raise APIException(
                            "Slot already booked or duration invalid")

                else:
                    raise ValidationError("Invalid s_f value")

            return super().partial_update(request, *args, **kwargs)

        except Exception as e:
            logger.exception("Unhandled error in partial_update")
            return Response({"error": "Internal server error", "details": str(e)}, status=500)

    def put(self, request, *args, **kwargs):
        event_type = request.data.get('EventType')
        if not event_type:
            logger.error("Missing 'EventType' in request body")
            return Response({"error": "Missing 'EventType' in request body"}, status=400)

        # Handle optional reschedule limit
        if event_type == 'Rescheduled':
            # Only count reschedules made by this user
            user_id = request.data.get("By")

            if not user_id:
                return Response({"error": "Missing 'By' (user ID) in request body"}, status=400)

            user_reschedules = AppointmentMgmt.objects.filter(AppId=kwargs['id'], EventType='Rescheduled', By_id=user_id)

            app_c_qs = AdminContentManagement.objects.filter(Category="Reschedule").order_by('-id').first()

            if app_c_qs:
                try:
                    app_c = int(app_c_qs.Content)
                    if user_reschedules.count() >= app_c:
                        logger.warning(
                            f"User {user_id} exceeded reschedule limit ({app_c}) for appointment {kwargs['id']}"
                        )
                        return Response(
                            {"error": f"You can not reschedule more than {app_c} times"},
                            status=403
                        )
                except ValueError:
                    logger.warning("Invalid reschedule limit value in AdminContentManagement")

            # if app_c_qs:  # Only apply limit if set
            #     try:
            #         app_c = int(app_c_qs.Content)
            #         if app_r.count() >= app_c:
            #             logger.warning(
            #                 f"Reschedule limit ({app_c}) reached for appointment {kwargs['id']}")
            #             return Response(
            #                 {"error": f"You can not reschedule more than {app_c} times"},
            #                 status=403
            #             )
            #     except ValueError:s
            #         logger.warning(
            #             "Invalid reschedule limit value in AdminContentManagement")
                    # Proceed without blocking if invalid config

        # Proceed with update
        res = self.partial_update(request, *args, **kwargs)
        return res if isinstance(res, Response) else Response({"message": res})


def AddCheckOutSessionDetails(a, b, c, d=None):
    r = PatientPayment.objects.create(
        CheckOutSessionId=a, PaymentIntent=b, PatientId=get_cu_user(int(c)), AppointmentId=d)
    if r is not None:
        return True
    else:
        return False


# added
def SendAdminAppCre(app_id, d_name, p_name):
    # notification to super_admin
    admin_obj = CuUser.objects.filter(is_admin__exact=1)
    for y in admin_obj:

        device_d = FCMDevice.objects.filter(user_id__exact=y.id)
        print(f"fcm devices----{device_d}---{type(device_d)}")
        img_url = settings.MEDIA_ROOT+"\logo.png"
        current_time = timezone.now()
        a = device_d.send_message(Message(notification=Notification(title='Appointment scheduled!', body=f'Hi {y.name}, Appointment with id -{app_id} has been schedueled between expert {d_name} and patient {p_name}!'), webpush=WebpushConfig(
            fcm_options=WebpushFCMOptions(link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar'))))
        res = PushNotifications.objects.create(UserId=y.id, NotificationTime=current_time,
                                               Title='Appointment scheduled!', Body=f'Hi {y.name}, Appointment with id -{app_id} has been schedueled between expert {d_name} and patient {p_name}!', Link=os.getenv('NEXT_CANCER_UNWIRED_ADMIN_APP')+'/calendar')
        print(f"push----{a}")
    return True


def SendPushReminder(u_id, uu_name, days, current_time, user_role, start_time, end_time):

    device = FCMDevice.objects.filter(user_id__exact=u_id)
    print(f"in reminderrrrrrrrrrr{device}")
    u_name = get_user_model().objects.get(id__exact=u_id).name
    print(f"in user nameeeeeeeeeeeeeeeeeeeee{u_name}")
    # print(f"fcm devices----{device}---{type(device)}")
    img_url = settings.MEDIA_ROOT + "\logo.png"
    data = ''
    print(data)

    s_date = start_time.date()
    ss_time = start_time.time()
    b = str(ss_time).split(":")
    s_time = b[0]+":"+b[1]

    ee_time = end_time.time()
    b = str(ee_time).split(":")
    e_time = b[0]+":"+b[1]

    if user_role == 'patient':

        # push_data = PushNotifications.objects.filter(UserId=u_id,Title='Appointment reminder!', Body=f'Hi {u_name}, You have an appointment with {uu_name} in {days} days!',Link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments')
        # if push_data is not None:

        a = device.send_message(Message(notification=Notification(title='Appointment reminder!',
                                                                  body=f'Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}'),
                                        webpush=WebpushConfig(fcm_options=WebpushFCMOptions(
                                            link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments'))))
        res = PushNotifications.objects.create(UserId=u_id, NotificationTime=current_time,
                                               Title='Appointment reminder!', Body=f'Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}', Link=os.getenv('NEXT_CANCER_UNWIRED_PATIENT_APP') + '/myprofile?tab=appointments')
        return a
    elif user_role in ['doctor', 'researcher', 'influencer']:
        # push_data = PushNotifications.objects.filter(UserId=u_id,Title='Appointment reminder!',Body=f'Hi {u_name}, You have an appointment with {uu_name} in {days} days!',Link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment')
        # if push_data is not None:

        a = device.send_message(Message(notification=Notification(title='Appointment reminder!',
                                                                  body=f'Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}'),
                                        webpush=WebpushConfig(fcm_options=WebpushFCMOptions(
                                            link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment'))))
        res = PushNotifications.objects.create(UserId=u_id, NotificationTime=current_time,
                                               Title='Appointment reminder!', Body=f'Hi {u_name}, You have appointment with {uu_name} on {s_date} {s_time}-{e_time}', Link=os.getenv('NEXT_CANCER_UNWIRED_DOCTOR_APP') + '/appointment')
        return a
    else:
        pass

    # print(f"in reminderrrrrrrrrrr response{a}")


def appointment_reminder(user_id, app_id):
    user_role = get_cu_user_type(user_id)
    current_time = timezone.now()
    n_e = notification_check("Appointment reminder")
    if user_role == 'patient' and n_e == True:
        app_data = Appointments.objects.filter(id=app_id)
        for x in app_data:
            slot_id = x.slot_id_id
            s_data = SchedulerSlots.objects.filter(id=x.slot_id_id)
            if s_data.exists():
                slot_data = SchedulerSlots.objects.filter(id=x.slot_id_id)[0]

                print(
                    f'-----------------------------slot_data----------{slot_data}')
                start_date = slot_data.schedule_start_time
                end_date = slot_data.schedule_end_time
                print(
                    f'--------------------------start_date-----------{start_date}')
                print(
                    f'--------------------------end_date-----------{end_date}')
                # start_date3 = datetime.strptime(str(parse_datetime(str(start_date))),format_data)
                # start_date2 = timezone.make_aware(parse_datetime(str(start_date)),timezone.get_current_timezone())
                converted_time1 = timezone.localtime(
                    start_date, timezone.get_current_timezone())
                print(
                    f'-------------------coverted_time---------{converted_time1}')
                converted_time2 = timezone.localtime(
                    end_date, timezone.get_current_timezone())
                print(
                    f'-------------------coverted_time---------{converted_time2}')

                doctor_id = slot_data.doctor_id
                uu_name = CuUser.objects.filter(id=doctor_id)[0].name
                a = SendPushReminder(
                    user_id, uu_name, "15", current_time, user_role, converted_time1, converted_time2)
    elif user_role in ['doctor', 'researcher', 'influencer'] and n_e == True:
        app_data = Appointments.objects.filter(id=app_id)
        slot_data = SchedulerSlots.objects.filter(id=app_data[0].slot_id.id)
        print(f"slot dataaaaaaaaaa{slot_data}")
        for x in slot_data:
            slot_id = x.id
            print(f"slot iddddddddddddd{slot_id}")
            p_data = Appointments.objects.filter(slot_id_id=slot_id)
            if p_data.exists():
                patient_data = Appointments.objects.filter(
                    slot_id_id=slot_id)[0]
                print(
                    f'-----------------------------slot_data----------{slot_data}')
                start_date = x.schedule_start_time
                end_date = x.schedule_end_time
                print(
                    f'--------------------------start_date-----------{start_date}')
                print(
                    f'--------------------------end_date-----------{end_date}')
                converted_time1 = timezone.localtime(parse_datetime(
                    str(start_date)), timezone.get_current_timezone())

                print(
                    f'-------------------converted_time1---------{converted_time1}')
                converted_time2 = timezone.localtime(
                    end_date, timezone.get_current_timezone())
                print(
                    f'-------------------converted_time---------{converted_time2}')

                patient_id = patient_data.patient_id
                uu_name = CuUser.objects.filter(id=patient_id)[0].name
                a = SendPushReminder(
                    user_id, uu_name, "15", current_time, user_role, converted_time1, converted_time2)


def notification_action(app_id, doctor_id, patient_id):
    n_s = notification_check('Appointment reminder')
    if n_s == True:
        try:
            a = appointment_reminder(doctor_id, app_id)
            a = appointment_reminder(patient_id, app_id)
            print(
                f'----------- appointment reminder response-------{a}------------')
        except Exception as e:
            print(e)

# added


def notification_jobs(app_id, start_time, doctor_id, patient_id):
    third_day_time = start_time + timedelta(minutes=15)
    scheduler.add_job(notification_action, 'date', run_date=third_day_time, args=[
                      app_id, doctor_id, patient_id])


def _map_status(airwallex_status):
    """Convert Airwallex status to your system's status"""
    status_map = {
        'requires_payment_method': 'REQUIRES_PAYMENT_METHOD',
        'requires_confirmation': 'REQUIRES_CONFIRMATION',
        'requires_action': 'REQUIRES_ACTION',
        'processing': 'PROCESSING',
        'succeeded': 'SUCCEEDED',
        'failed': 'FAILED',
        'canceled': 'CANCELED'
    }
    return status_map.get(airwallex_status.lower(), 'PROCESSING')


def CreateOrder(payment_intent_id, user_id=None, slot_id=None, summary=None, location=None, desc=None, currency=None, amount=None, raw_status=None):
    try:
        # Validate required fields
        if not all([user_id, slot_id]):
            print("Missing user_id or slot_id in metadata")
            return False

        # Get user and slot objects
        user = get_user_model().objects.filter(id=user_id).first()
        slot = SchedulerSlots.objects.filter(id=slot_id).first()

        if not user or not slot:
            print(f"Invalid user ({user_id}) or slot ({slot_id})")
            return False

        # Update slot status to pending
        slot.status = 'P'  # Pending
        slot.save(update_fields=['status'])

        # Create appointment
        appointment = Appointments.objects.create(
            slot_id=slot,
            patient=user,
            summary=summary or "Medical Appointment",
            location=location or "Clinic",
            description=desc or "",
            status='P'  # Pending fulfillment
        )

        # Link payment to appointment
        PatientPayment.objects.create(
            AppointmentId=appointment,
            PaymentIntent=payment_intent_id,
            PatientId=user,
            currency=currency,  # Add currency field
            amount=amount,  # Add amount field to your model
            # payment_status='completed',
            payment_status=_map_status(raw_status),  # Convert to your status
            raw_status=raw_status,  # Store original Airwallex status
            payment_date=timezone.now()
        )

        print(
            f"Created appointment {appointment.id} for payment {payment_intent_id}")

        return appointment

    except Exception as e:
        print(f"CreateOrder failed: {str(e)}")
        return False


def FulfillOrder(appointment_id, user_id=None, slot_id=None):
    try:
        appointment = Appointments.objects.get(id=appointment_id.id)

        # If called from webhook, we might need to get these values
        if not user_id:
            user_id = appointment.patient.id
        if not slot_id:
            slot_id = appointment.slot_id.id
        
        # Mark slot as booked
        appointment.status = 'B'  # Booked
        appointment.save()

        # Get related objects
        slot = SchedulerSlots.objects.get(id=slot_id)
        # Update slot status to booked
        slot.status = 'B'  # Booked
        slot.save(update_fields=['status'])
        
        doctor = slot.doctor
        patient = get_user_model().objects.get(id=user_id)

        # Add session details (assuming this creates meeting links, etc.)
        add_session_details(appointment_id)

        # Send email if enabled
        if email_check('Appointment schedule'):
            try:
                send_meet_mail(appointment_id)
            except Exception as e:
                print(f"Error sending email: {e}")

        # Send notifications
        notification_jobs(appointment_id.id,
                          slot.schedule_start_time, doctor.id, patient.id)

        if notification_check('Appointment schedule'):
            try:
                SendPushNotification(doctor.id, patient.name)
            except Exception as e:
                print(f"Error sending push notification: {e}")

        # Send admin notification
        SendAdminAppCre(appointment_id.id, doctor.name, patient.name)

        return True

    except Exception as e:
        print(f"Error fulfilling order: {e}")
        return False
# NEW CREATE FULLFILL ORDER

# --------------------------------------------------------------------------------
# update / delete scheduler slots only if status is null (i.e available)


class EditDeleteSlot(RetrieveUpdateDestroyAPIView):
    queryset = SchedulerSlots.objects.all()
    serializer_class = SlotSerializer
    lookup_field = 'id'

    def update(self, request, *args, **kwargs):
        instance = self.get_object()

        if instance.status not in [None, '', 'null']:
            return Response({"detail": "Only Available Slots can be updated."}, status=400)

        data = request.data.copy()

        data.pop('status', None)

        try:
            start_date = parse_datetime(data['start_date'])
            tz = timezone.get_current_timezone()

            data['schedule_start_time'] = timezone.make_aware(
                parse_datetime(
                    f"{start_date.date()}T{data['schedule_start_time']}"), tz
            ).isoformat()
            data['schedule_end_time'] = timezone.make_aware(
                parse_datetime(
                    f"{start_date.date()}T{data['schedule_end_time']}"), tz
            ).isoformat()

            user = get_user_model().objects.filter(
                email=data['doctor']).first()
            if not user:
                return Response({"doctor": ["User with this email does not exist."]}, status=400)
            data['doctor'] = user.id

        except Exception as e:
            return Response({"detail": f"Invalid input: {e}"}, status=400)

        serializer = self.get_serializer(instance, data=data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()

        if instance.status not in [None, '', 'null']:
            return Response({"detail": "Only Available Slots can be Deleted."}, status=400)

        self.perform_destroy(instance)
        return Response({"detail": "Slot deleted successfully."}, status=200)
